<?php

class Controller_Abandoned extends Controller_Website
{

   
   public function before()
    {
         parent::before();
          $this->pagination->items_per_page=500   ;   
    }


    public function action_index()
    {     
        
        $where = "";
        if($this->request->param('division')) $where.= sprintf(" AND  p.segmento='%s' ", $this->request->param('division') );
        if($this->request->param('xtras')) $where.= sprintf(" AND c.vendedor = '%s'", $this->request->param('xtras') );

     
        $sql = sprintf("SELECT c.id as _IDCLI, c.nome Cliente,c.bairro,cidade,estado,c.cnpj,c.cgc, nick, c.limite Limite, count(DISTINCT h.id) as Pedidos, sum(hi.valor_base*hi.quant) as Compras, p.segmento,  
                                MAX(h.data) as data, 
                                DATEDIFF( now(), MAX(h.data) ) as Dias
                        FROM clientes AS c
                        LEFT JOIN hoje AS h ON h.idcli = c.id
                        LEFT JOIN hist hi ON hi.pedido=h.id
                        LEFT JOIN inv i ON i.id=hi.isbn
                        LEFT JOIN produtos p ON p.id=i.idcf
                        LEFT JOIN rolemak_users u ON u.id=c.vendedor
                        WHERE h.id > 1000000 AND nop IN (27,28,51, 76)
                             %s
                        GROUP BY c.id   
                        HAVING Dias > 335
                        ORDER BY data DESC  
                        LIMIT %s,%s", 
                        $where,
                           $this->pagination->offset+$this->pagination->current_page-1,
                           $this->pagination->items_per_page
                           );
        
        
        $query = DB::query(Database::SELECT, $sql);
        
        $data = $query->execute()->as_array();	
        
        // s($data);
        // return  $result;
        
           //// Format Data
        $fmt['Revenda'] = $fmt['Diff'] = $fmt['ValorMedioAno'] = '2us'; 
        $fmt['Limite'] = $fmt['Compras'] = '0us';
         $data['format'] = $fmt;
        
        $view = parent::tablefy( $data );
        $response = parent::pagefy($view);
        
        $this->response->body($response);

     
        
    }



    public function action_cidades()
    {     
        
        $where = "";
        if($this->request->param('division')) $where.= sprintf(" AND  p.segmento='%s' ", $this->request->param('division') );
        if($this->request->param('xtras')) $where.= sprintf(" AND c.vendedor = '%s'", $this->request->param('xtras') );

     
        $sql = sprintf("SELECT c.id as _IDCLI, c.nome Cliente,c.bairro,c.cidade,c.estado,c.cnpj,c.cgc, 
                                CONCAT_WS('-',nick,c.vendedor) Vendedor ,
                                c.limite Limite, count(DISTINCT h.id) as Pedidos, sum(hi.valor_base*hi.quant) as Compras, p.segmento,  
                                MAX(h.data) as data, 
                                DATEDIFF( now(), MAX(h.data) ) as Dias,
                                 (SELECT SUM(cheques.valor) FROM cheques WHERE cheques.idcli=c.id AND cheques.data < CURDATE()-3 AND ( ISNULL(cheques.datadep) OR MONTH(cheques.datadep)=0 ) GROUP BY cheques.idcli) AS ValorEmAtraso,
                                 c.	memoh
                        FROM `vCidadesAbandonadas` v
                        
                        LEFT JOIN clientes c on (v.estado=c.estado AND v.cidade=c.cidade)
                        LEFT JOIN hoje h ON h.idcli = c.id
                        LEFT JOIN hist hi ON hi.pedido=h.id
                        LEFT JOIN inv i ON i.id=hi.isbn
                        LEFT JOIN produtos p ON p.id=i.idcf
                        LEFT JOIN rolemak_users u ON u.id=c.vendedor
                        WHERE h.id > 1000000 AND nop IN (27,28,51, 76) AND c.vendedor <> 9 AND c.estado = 'SP'
                             %s
                        GROUP BY c.id   
                        HAVING Dias > 365 
                        ORDER BY data DESC  
                        LIMIT %s,%s", 
                        $where,
                           $this->pagination->offset+$this->pagination->current_page-1,
                           $this->pagination->items_per_page
                           );
        
        
        $query = DB::query(Database::SELECT, $sql);
        
        $data = $query->execute()->as_array();	
        
        // s($data);
        // return  $result;
        
           //// Format Data
        $fmt['Revenda'] = $fmt['Diff'] = $fmt['ValorMedioAno'] = '2us'; 
        $fmt['Limite'] = $fmt['Compras'] = '0us';
         $data['format'] = $fmt;
        
        $view = parent::tablefy( $data );
        $response = parent::pagefy($view);
        
        $this->response->body($response);

     
        
    }

  
   
}