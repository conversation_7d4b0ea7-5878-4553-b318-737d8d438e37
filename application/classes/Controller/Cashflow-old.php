<?php

class Controller_Cashflow extends Controller_Websession
{

    public function before()
    {


        parent::before();

        // $this->action_constants();

        // $this->action_debug();
    
        $this->companies = $this->action_get_emit() ;
        // $this->segs = $this->action_get_seg() ;		
        // $this->sellers = $this->action_get_sellers() ;
        
    }
    // public function index()
    // {
    //     $sql = sprintf("SELECT * Vars
    //                         LIMIT 100 OFFSET 0");
    //         //s($sql);

    //         $query = DB::query(Database::SELECT, $sql);
    //         $response = $query->execute()->as_array();
    //         //s($sql,$response);
    // }

  public function action_index()
    {

        $vars= $this->action_vars();
        define("_DOLAR",$vars['Dolar']);
        $vars_terms= $this->action_cashflow_terms();
        $vars_shipments= $this->action_cashflow_shipments();


        $sql = sprintf("SELECT    
							IF(shipments.invoice <>'' ,shipments.invoice,'-') AS Fatura,
							IF( MONTH(shipments.date)>0 ,date_format(shipments.date,'%%d/%%m/%%y'),'-') AS Embarque,      

							IF( MONTH(shipments.arrival)>0 ,date_format(shipments.arrival,'%%d/%%m/%%y'),'-') AS Chegada, 
							IF(shipments.status      >0 ,date_format(shipments.status,'%%d/%%m/%%Y'),'-') AS Liberado,   

						    IF( MONTH(shipments.arrival)>0, TO_DAYS(shipments.arrival), TO_DAYS(shipments.date)+30) - TO_DAYS(now()) as Dias,
							DATE_ADD(shipments.date, INTERVAL 90 DAY) as DueDate,
							CONCAT(YEAR(DATE_ADD(shipments.date, INTERVAL 90 DAY)),'-', MONTH(DATE_ADD(shipments.date, INTERVAL 90 DAY))) as Mes,
							(TO_DAYS(shipments.date)+90) - TO_DAYS(now())  as Days,

							IF(shipments.bl <>'' ,shipments.bl,'-') AS B_L,
							SUM(shipments.cambio+shipments.freight) AS _Fob,
							IF(shipments.tax_paid    >0 ,0,shipments.Numerario) AS Numerario,					
							IF(shipments.tax_paid    >0 ,'pago','aberto') AS Impostos,
							IF(shipments.invoice_paid>0 ,'fechado','aberto') AS Cambio, 
							usd_real as USD,

							shipments.Id

						  FROM  shipments
						  WHERE 
								 shipments.id >0 
								and shipments.fob_paid =0

						  GROUP BY	 Mes					  
						  HAVING Days >0

						  ORDER BY  shipments.date 

						  LIMIT 200
						  " ); 

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();
      
      
        foreach( $result as $key => $value){
            if( strlen($value["Mes"])<7) {
                $value["Mes"]	= substr($value["Mes"],0,5). '0' . substr($value["Mes"],5,1);
            }
            $cf[$value["Mes"]]["Fob"] = $value["_Fob"]*_DOLAR;
        }


        $sql = sprintf("SELECT    
							IF( MONTH(shipments.date)>0 ,date_format(shipments.date,'%%d/%%m/%%y'),'-') AS Embarque,      
							IF( MONTH(shipments.arrival)>0 ,shipments.arrival,CONCAT(DATE_ADD(shipments.date, INTERVAL 45 DAY),'-prev' ) ) AS Chegada, 
							IF( MONTH(shipments.arrival)>0 , CONCAT(YEAR(shipments.arrival),'-', MONTH(shipments.arrival))
							, CONCAT(YEAR(DATE_ADD(shipments.date, INTERVAL 35 DAY)),'-', MONTH(DATE_ADD(shipments.date, INTERVAL 35 DAY)))
	 ) as Mes,


						    IF( MONTH(shipments.arrival)>0, TO_DAYS(shipments.arrival), TO_DAYS(shipments.date)+30) - TO_DAYS(now()) as Dias,
							DATE_ADD(shipments.date, INTERVAL 150 DAY) as DueDate,
							(TO_DAYS(shipments.date)+150) - TO_DAYS(now())  as Days,

							shipments.cambio  AS _Valor,
							shipments.Freight AS _Frete,
							SUM(shipments.Numerario) AS Numerario,					
						   	shipments.obs,
							importer as Unidade,
							usd_real as USD,
							shipments.id as Shid,									
							shipments.Id

						  FROM  shipments
						  WHERE month(shipments.date)>0
								and shipments.id >0 
								AND month(shipments.status)= 0
								AND shipments.tax_paid    =0 


						  GROUP BY	Mes					  



						  LIMIT 200
						  " ); 

        $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute()->as_array();
        // echo Kohana::debug((array) $result );

        foreach( $result as $key => $value){
            if( strlen($value["Mes"])<7) {
                $value["Mes"]	= substr($value["Mes"],0,5). '0' . substr($value["Mes"],5,1);
            }
            $cf[$value["Mes"]]["Num"] = $value["Numerario"];
            //echo $value["Mes"];
        }


        $sql = sprintf("SELECT sum(valor) as Pagar, CONCAT(YEAR(data),'-', MONTH(data)) as Mes
							  FROM  pagamentos
							  WHERE ( MONTH(datadep)=0  or ISNULL(pagamentos.datadep) ) and  data > now()  


							  GROUP BY Mes
							  ORDER BY data
							limit 5000	

							  		  ");
         $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute()->as_array();
        //echo Kohana::debug((array) $result);
        foreach( $result as $key => $value){
            if( strlen($value["Mes"])<7) {
                $value["Mes"]	= substr($value["Mes"],0,5). '0' . substr($value["Mes"],5,1);
            }
            $cf[$value["Mes"]]["Pagar"] = $value["Pagar"];




        }

        //echo Kohana::debug((array) $cf);		 

        $sql = sprintf("SELECT sum(valor) as Receber, CONCAT(YEAR(data),'-', MONTH(data)) as Mes
							  FROM  cheques ch
							  WHERE  MONTH(ch.datadep)=0  and  data > now()
							  GROUP BY Mes
							  ORDER BY data
							limit 22	

							  		  ");
         $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute()->as_array();
        foreach( $result as $key => $value){

            if( strlen($value["Mes"])<7) {
                $value["Mes"]	= substr($value["Mes"],0,5). '0' . substr($value["Mes"],5,1);
            }

            $cf[$value["Mes"]]["Receber"] = $value["Receber"];
        }
        ksort($cf);

        $days['useful']= $this->GetUsefulDays();
        $days['tillnow']= $this->GetUsefulDaysTillNow();
        $days['index']= 1-($days['tillnow']/$days['useful']);	
        $counter=0;
        foreach( $cf as $key => $value)
        {
        
            if(!isset($cf[$key]["Fob"])) $cf[$key]["Fob"]=0;
            if( isset($cf[$key]["Receber"]) ){
                $counter++;		
                //$cf[$key]["Num"]=0;
                $cf[$key]["NumPrev"]=0;
                if($counter>1) {
                    $cf[$key]["Prev"] = $vars["Target_Machines"]*($vars_terms[0]["0X"]/100);						
                }else{
                    //$cf[$key]["Num"]=$vars_shipments[0]["0X"];
                    $cf[$key]["Prev"] = $vars["Target_Machines"]*($vars_terms[0]["0X"]/100)*($days['index']);							
                }
                if($counter==2){
                    $cf[$key]["Prev"]+= $vars["Target_Machines"]*($vars_terms[0]["1X"]/100);
                    //$cf[$key]["Ship"]	=$vars_shipments[0]["1X"];
                    $cf[$key]["NumPrev"]	=$vars_shipments[0]["0X"]*.23*_DOLAR;
                }
                if($counter==3){
                    $cf[$key]["Prev"]+= $vars["Target_Machines"]*($vars_terms[0]["2X"]/100);									
                    $cf[$key]["NumPrev"]	=$vars_shipments[0]["1X"]*.23*_DOLAR;
                }
                if($counter==4){
                    $cf[$key]["Prev"]+= $vars["Target_Machines"]*($vars_terms[0]["3X"]/100);									
                    $cf[$key]["NumPrev"]	=$vars_shipments[0]["2X"]*.23*_DOLAR;

                }
                if($counter==5){
                    $cf[$key]["Prev"]+= $vars["Target_Machines"]*($vars_terms[0]["4X"]/100);
                    $cf[$key]["NumPrev"]	=$vars_shipments[0]["3X"]*.23*_DOLAR;
                    $cf[$key]["Fob"]+=$vars_shipments[0]["0X"]*_DOLAR;

                }
                if($counter==6){
                    $cf[$key]["Prev"]+= $vars["Target_Machines"]*($vars_terms[0]["5X"]/100);												
                    $cf[$key]["NumPrev"]	=$vars_shipments[0]["4X"]*.23*_DOLAR;
                    $cf[$key]["Fob"]+=$vars_shipments[0]["1X"]*_DOLAR;

                }
                if($counter>6){
                    $cf[$key]["Prev"]+= $vars["Target_Machines"]*($vars_terms[0]["6X"]/100);															
                    $cf[$key]["Fob"]+=$vars_shipments[0]["2X"]*_DOLAR;

                }
                //if($counter>7) $cf[$key]["Prev"]+= $vars["Target_Machines"]*($vars_terms[0]["7X"]/100);															
                //if($counter>8) $cf[$key]["Prev"]+= $vars["Target_Machines"]*($vars_terms[0]["8X"]/100);															

                $cf[$key]["Prev"]+= $vars["Target_Bearings"]*($vars_terms[1]["0X"]/100);						
                if($counter>1) $cf[$key]["Prev"]+= $vars["Target_Bearings"]*($vars_terms[1]["1X"]/100);						
                if($counter>2) $cf[$key]["Prev"]+= $vars["Target_Bearings"]*($vars_terms[1]["2X"]/100);						
                if($counter>3) $cf[$key]["Prev"]+= $vars["Target_Bearings"]*($vars_terms[1]["3X"]/100);						
                if($counter>4) $cf[$key]["Prev"]+= $vars["Target_Bearings"]*($vars_terms[1]["4X"]/100);						
                if($counter>5) $cf[$key]["Prev"]+= $vars["Target_Bearings"]*($vars_terms[1]["5X"]/100);						
                if($counter>6) $cf[$key]["Prev"]+= $vars["Target_Bearings"]*($vars_terms[1]["6X"]/100);						


                $cf[$key]["Prev"]+= $vars["Target_Parts"]*($vars_terms[2]["0X"]/100);	;						
                if($counter>1) $cf[$key]["Prev"]+= $vars["Target_Parts"]*($vars_terms[2]["1X"]/100);						
                if($counter>2) $cf[$key]["Prev"]+= $vars["Target_Parts"]*($vars_terms[2]["2X"]/100);						
                if($counter>3) $cf[$key]["Prev"]+= $vars["Target_Parts"]*($vars_terms[2]["3X"]/100);						
                if($counter>4) $cf[$key]["Prev"]+= $vars["Target_Parts"]*($vars_terms[2]["4X"]/100);						
                if($counter>5) $cf[$key]["Prev"]+= $vars["Target_Parts"]*($vars_terms[2]["5X"]/100);						
                if($counter>6) $cf[$key]["Prev"]+= $vars["Target_Parts"]*($vars_terms[2]["6X"]/100);						

            }
        }

        //echo Kohana::debug((array) $cf);		 
        //echo Kohana::debug((array) $result);	

        // s($vars,$vars_terms,$vars_shipments,$vars_shipments,$cf);
         
        $this->request->response =  View::factory('cashflow')
        //     ->set('vars',  $vars  )
        //     ->set('vars_terms',  $vars_terms  )
        //     ->set('vars_shipments',  $vars_shipments)
        //     ->set('stock',  $stock  )
        //     ->set('vendas',  $vendas  )
            ->set('list', $cf );
        $this->response->body($view)
                    
         
        //  die();

        // foreach($this->companies as $key => $val)	{								
        //     $stock[$key] = $this->action_stock($key);
        // }
        
        // $vendas = self::action_year_growth();

        // s($vars,$vars_terms,$vars_shipments,$vars_shipments,$stock,$vendas,$cf);
        // $this->request->response =  View::factory('fms/cashflow')
        //     ->set('vars',  $vars  )
        //     ->set('vars_terms',  $vars_terms  )
        //     ->set('vars_shipments',  $vars_shipments)
        //     ->set('stock',  $stock  )
        //     ->set('vendas',  $vendas  )
        //     ->set('list', $cf );



    }
    
      public function action_vars()
    {
        $sql = sprintf("SELECT *  FROM   Vars	");
          $query = DB::query(Database::SELECT, $sql);
            $response = $query->execute()->as_array();
        //echo Kohana::debug((array) $result);	
        return $response[0];
    }
    public function action_cashflow_terms()
    {
        $sql = sprintf("SELECT *  FROM   CashFlowTerms	");
          $query = DB::query(Database::SELECT, $sql);
            $response = $query->execute()->as_array();
        //echo Kohana::debug((array) $result);	
        return $response;
    }	
    public function action_cashflow_shipments()
    {
        $sql = sprintf("SELECT *  FROM   CashFlowShipments	");
         $query = DB::query(Database::SELECT, $sql);
            $response = $query->execute()->as_array();
        //echo Kohana::debug((array) $result);	
        return $response;
    }	

  public function GetUsefulDays() {

        $thismonth=date('n');
        $d=0;

        for ($i=1;$i<32;$i++) {
            $day = mktime (0, 0, 0, date("m")  ,$i, date("Y"));
            $date = getdate($day); 
            if ($date['mon']==$thismonth  and  $date['wday']<>0 and  $date['wday']<>6) {
                $d++;
            }   
        }
        return $d;

    }


    public function GetUsefulDaysIndex() {

        $d=$this->GetUsefulDays()/$this->GetUsefulDaysTillNow();

        return $d;

    }


    public function GetUsefulDaysTillNow() {

        $thismonth=date('n');
        $d=0;

        for ($i=1;$i<=date('d');$i++) {
            $day = mktime (0, 0, 0, date("m")  ,$i, date("Y"));
            $date = getdate($day); 
            if ($date['mon']==$thismonth  and  $date['wday']<>0 and  $date['wday']<>6) {
                $d++;
            }   
        }
        return $d;

    }
    public function table($tablename,$cnpj)	
    {	

        switch($tablename) {
            case('nfe'):
                return 	 'NFE'.addslashes ('.').$cnpj.'_NFe';	
            case('pedidos'):
                return 	 'NFE'.addslashes ('.').$cnpj.'_NFeTemPedidos';	
            case('docs'):
                return 	 'NFE'.addslashes ('.').$cnpj.'_NFeDoc';	
            case('total'):
                return 	 'NFE'.addslashes ('.').$cnpj.'_NFeTotal';	
            case('entradas'):
                return 	 'NFE'.addslashes ('.').$cnpj.'_NFentradas';				 
            case('entradas_det'):
                return 	 'NFE'.addslashes ('.').$cnpj.'_NFentradas_det';				 

        }

    }
   public function action_get_emit()	
    {	

        //echo  $this->request->uri() ;

        if( substr($this->request->uri(),0,5)== 'leads')			
        {
            $where=' ' ;	
        }else{
            $where=' WHERE tpAmb=1'		;
        }
        //die();

       

             $sql= sprintf("SELECT *,  %s as ID, %s AS Empresa FROM  %s %s", 'EmitentePOID', 'Fantasia', 'Emitentes', $where) ;
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute('mak')->as_array();

           
  foreach($result as $key => $val){
                $result[$key]['NFE']['nfe']= $this->table('nfe',  $val['CNPJ']);
                $result[$key]['NFE']['pedidos']= $this->table('pedidos',  $val['CNPJ']);
                $result[$key]['NFE']['docs']= $this->table('docs',  $val['CNPJ']);
                $result[$key]['NFE']['total']= $this->table('total',  $val['CNPJ']);
                $result[$key]['NFE']['entradas']= $this->table('entradas',  $val['CNPJ']);
                $result[$key]['NFE']['entradas_det']= $this->table('entradas_det',  $val['CNPJ']);
                $result[$key]['DB']['mak']= 'mak_'. substr($val['CNPJ'],10,4);
                $result[$key]['Transporter']= 'mak_'. substr($val['CNPJ'],10,4).'.transportadora';

                $this->global_cnpj[$val['CNPJ']]['NFE']['nfe']= $this->table('nfe',  $val['CNPJ']);
                $this->global_cnpj[$val['CNPJ']]['NFE']['pedidos']= $this->table('pedidos',  $val['CNPJ']);
                $this->global_cnpj[$val['CNPJ']]['NFE']['docs']= $this->table('docs',  $val['CNPJ']);
                $this->global_cnpj[$val['CNPJ']]['NFE']['total']= $this->table('total',  $val['CNPJ']);
                $this->global_cnpj[$val['CNPJ']]['NFE']['entradas']= $this->table('entradas',  $val['CNPJ']);
                $this->global_cnpj[$val['CNPJ']]['NFE']['entradas_det']= $this->table('entradas_det',  $val['CNPJ']);
                $this->global_cnpj[$val['CNPJ']]['DB']['mak']= 'mak_'. substr($val['CNPJ'],10,4);
                $this->global_cnpj[$val['CNPJ']]['Transporter']= 'mak_'. substr($val['CNPJ'],10,4).'.transportadora';
                $this->global_cnpj[$val['CNPJ']]['DATA']= $val;
            }
            foreach($result as $key => $val){
                $emit[$val['EmitentePOID']] = $val;


                if($val['EmitentePOID']==6) {
                    $emit[$val['EmitentePOID']]['logo16']= "<img title='".$val['Fantasia']."' src='http://icons.iconarchive.com/icons/ph03nyx/super-mario/16/Flower-Ice-icon.png'> ";
                    $emit[$val['EmitentePOID']]['logo24']= "<img title='".$val['Fantasia']."' src='http://icons.iconarchive.com/icons/ph03nyx/super-mario/24/Flower-Ice-icon.png'> ";
                    $emit[$val['EmitentePOID']]['logo32']= 'http://icons.iconarchive.com/icons/ph03nyx/super-mario/32/Flower-Ice-icon.png';
                    $emit[$val['EmitentePOID']]['logo48']= 'http://icons.iconarchive.com/icons/ph03nyx/super-mario/48/Flower-Fire-icon.png';
                    $emit[$val['EmitentePOID']]['logo64']= 'http://icons.iconarchive.com/icons/ph03nyx/super-mario/64/Flower-Fire-icon.png';							
                }else{

                    if($val['EmitentePOID']==5) {
                        $emit[$val['EmitentePOID']]['logo16']= "<img title='".$val['Fantasia']."' src='http://icons.iconarchive.com/icons/ph03nyx/super-mario/16/Flower-Ice-icon.png'> ";
                        $emit[$val['EmitentePOID']]['logo24']= "<img title='".$val['Fantasia']."' src='http://www.veryicon.com/icon/32/Leisure/At%20the%20Beach/sandcastle.png'> ";
                        $emit[$val['EmitentePOID']]['logo32']= 'http://www.veryicon.com/icon/32/Leisure/At%20the%20Beach/sandcastle.png';
                        $emit[$val['EmitentePOID']]['logo48']= 'http://www.veryicon.com/icon/48/Leisure/At%20the%20Beach/sandcastle.png';
                        $emit[$val['EmitentePOID']]['logo64']= 'http://www.veryicon.com/icon/64/Leisure/At%20the%20Beach/sandcastle.png';							
                    }else{

                        $emit[$val['EmitentePOID']]['logo16']= $val['Fantasia'];
                        $emit[$val['EmitentePOID']]['logo24']= $val['Fantasia'];
                        $emit[$val['EmitentePOID']]['logo32']= $val['Fantasia'];
                        $emit[$val['EmitentePOID']]['logo48']= '';
                        $emit[$val['EmitentePOID']]['logo64']= '';
                    } 
                } 

                if($val['EmitentePOID']==7) {
                    $emit[$val['EmitentePOID']]['logo24']= 
                        "<img  title='".$val['Fantasia']."' height='24px' src='https://imagepng.org/wp-content/uploads/2017/06/bandeira-de-goias-7.png' > ";




                }
                if($val['EmitentePOID']==1) {
                    $emit[$val['EmitentePOID']]['logo24']= 
                        "<img  title='".$val['Fantasia']."' height='24px' src='https://imagepng.org/wp-content/uploads/2017/05/bandeira-do-estado-de-sao-paulo-8.png' > ";


                }                            
            }
       
        return $emit;
    }   
    
    public function action_stock($unity=1,$business='1')
    {
        // s($this->companies);
        $sql = sprintf("SELECT p.segmento,
		 						p.nome,
								p.ncm,idcf, 
								sum(e.`EstoqueDisponivel`+ e.`EstoqueReservado`+ f.`EstoqueDisponivel`+ f.`EstoqueReservado`) as Qty,
								sum(i.fobset*(e.`EstoqueDisponivel`+e.`EstoqueReservado`+ f.`EstoqueDisponivel`+ f.`EstoqueReservado`)) as Fobset,
                                sum(i.fob*(e.`EstoqueDisponivel`+e.`EstoqueReservado`+ f.`EstoqueDisponivel`+ f.`EstoqueReservado`)) as Fob

		  				FROM 	%s.`Estoque` e, 
								%s.Estoque_TTD_1 f, 
								inv i 
						LEFT JOIN produtos p ON (p.id=i.idcf)
						WHERE  	i.id=e.`ProdutoPOID` AND 
								i.id=f.`ProdutoPOID` AND 
								e.`EstoqueDisponivel`+e.`EstoqueReservado`+ f.`EstoqueDisponivel`+ f.`EstoqueReservado` >0 
						GROUP BY   p.segmento
						ORDER BY    p.segmento ", 
                       $this->companies[$unity]['DB']['mak'], 
                       $this->companies[$unity]['DB']['mak']);

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();
        //echo Kohana::debug((array) $result);		
        return $result;
    }

    public function action_year_growth()
    {

        

        $sql= sprintf("SELECT produtos.segmento,EXTRACT(YEAR_MONTH FROM hoje.data) as date ,SUM(hist.quant*hist.valor_base) as VT FROM hist,inv,produtos,hoje LEFT JOIN clientes ON (clientes.id=hoje.idcli) WHERE hoje.nop in (27,28,51) AND hist.pedido=hoje.id AND inv.id=hist.isbn AND produtos.id=inv.idcf AND hist.idcli <>707602 AND hist.valor_base>0 AND hist.isbn=inv.id AND hoje.id > 1175000 and YEAR(hoje.data) > 2017 AND produtos.segmento<>'faucets' AND produtos.segmento<> 'geral' GROUP BY segmento,date ORDER BY segmento, date DESC LIMIT 300"

                                     );

        $query = DB::query(Database::SELECT, $sql);
        
        $response = $query->execute()->as_array();	
       
        foreach($response as $segmento => $val)
            {
            $vendas[$val['segmento']][$val['date']] = $val['VT'];

        }

        return $vendas;

        // echo Kohana::debug((array) $vendas);


    }


}