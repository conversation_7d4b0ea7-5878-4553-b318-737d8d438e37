<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Chartfy extends Controller_Websession {

	public function chartfy($arr)
	{
		// s($arr[0]);
		// s(count($arr[0]));
		$this->subtotal=0;
		$this->qty=0;
		$this->pagination = Pagination::factory(array(
				'total_items'    => 100,
				'items_per_page' => 10,
				// 'current_page' => $arr['pagination']->current_page,
				// 'current_first_item' => 2,
				// 'current_last_item' => 2,
				// 'first_page' => 2,
				// 'next_page' => 2,
				// 'offset' => 2,
				
			));		
			
		$this->theme = 1;
		// $this->pagination->offset;
			
			
		// $arr = $this->request->post('array');
		
		if(empty($arr)) return '';

		if(isset($arr['sum']))
		{
			$this->sum =  $arr['sum'];
			unset($arr['sum']);
		}
		
		if(isset($arr['format']))
		{
			$this->format =  $arr['format'];
			unset($arr['format']);
		}
		
		if(isset($arr['color']))
		{
			$this->color =  $arr['color'];
			unset($arr['color']);
		}
		
		if(isset($arr['editable']))
		{
			$this->editable =  $arr['editable'];
			unset($arr['editable']);
		}
		
		if(isset($arr['theme']))
		{
			$this->theme =  $arr['theme'];
			unset($arr['theme']);
		}
		
			$this->menu='';
		if(isset($arr['menu']))
		{
			$this->menu =  $arr['menu'];
			unset($arr['menu']);
		}
		
		


		$menu='';
		$this->caption ="Tabela de dados";
	
		if(isset($arr['caption']))
		{
			
			
			$this->caption = '<ul class="flex">';
			
			foreach($arr['caption'] as $cap) 
			{
				$this->caption.= sprintf('<li class="mr-6"><a class="text-blue-500 hover:text-blue-800" href="%s">%s</a></li>', $cap['link'],$cap['title']);
			}
			
			$this->caption.= '</ul>';
			
			unset($arr['caption']);
		}
		
		
		if(isset($arr['htmx']))
		{
			
			$this->caption = '<ul class="flex">';
			
			foreach($arr['htmx'] as $cap) 
			{
				$this->caption.= sprintf('<li class="mr-6">%s</li>',$cap['title']);
			}
			
			$this->caption.= '</ul>';
			
			unset($arr['htmx']);
		}
		
		
		if(isset($arr['pagination']))
		{
			
			// foreach($arr['pagination'] as $kpg -> $pg)
			// {
			// 	 $this->pagination->$kpg =  $pg;
			// }
			
			 $this->pagination =  $arr['pagination'];
			// $this->pagination = Pagination::factory( $arr['pagination']);	
			unset($arr['pagination']);
			
		}
		
	

		// $themeChosen = 1;

		$theme[1]['tb-bg-color']	= 'bg-gray-600';
		$theme[1]['tb-txt-color'] = 'text-white';
		
		$theme[1]['th-bg-color']	= 'bg-gray-700';
		$theme[1]['th-txt-color'] = 'text-gray-100';
		
		$theme[1]['tbody-bg-color']	= 'bg-gray-700';
		$theme[1]['tbody-txt-color'] = 'text-gray-100';
		
	
		$theme[2]['tb-bg-color']	= 'bg-white';
		$theme[2]['tb-txt-color'] = 'text-gray-900';
		
		$theme[2]['th-bg-color']	= 'bg-gray-100';
		$theme[2]['th-txt-color'] = 'text-gray-700';
		
		$theme[2]['tbody-bg-color']	= 'bg-gray-100';
		$theme[2]['tbody-txt-color'] = 'text-gray-700';
		
		
		$theme[3]['tb-bg-color']	= 'bg-dark';
		$theme[3]['tb-txt-color'] = 'text-gray-900';
		
		$theme[3]['th-bg-color']	= 'bg-green-900';
		$theme[3]['th-txt-color'] = 'text-green-100';
		
		$theme[3]['tbody-bg-color']	= 'bg-green-100';
		$theme[3]['tbody-txt-color'] = 'text-green-700';	
		
		$themeChosen = $this->theme;

	
	  
		
		$this->table=$menu.$this->menu;
		
		if(!$arr)
		{
			 return $this->caption ;
		}
		// $this->table.= sprintf('<table   border="1" class="table tablesorter  %s %s border-separate space-y-6 text-sm">', $theme[$themeChosen]['tb-bg-color'], $theme[$themeChosen]['tb-txt-color'], $theme[$themeChosen]['tb-txt-color']    );
		
		$this->table.= '<table id="table-sparkline"  class="table border-separate space-y-6 text-sm">';

		
		$thead = $arr[0];
		
		/// THEAD
		$this->table.=sprintf('<thead>' );
		$this->table.= '<tr>';
		// $this->table.= '<th>#</th>';
		
		$colCounter=0;
		foreach( $thead as $key => $value)
		{
			if(substr($key,0,1)=='_' and $key<>'_ISBN') continue;
			$colCounter++;
			
			list($key) = self::head($key);	
			
			$this->table.= $key;
		}
		
		$this->table.= '</tr>';
		$this->table.='</thead>';
		
		/// TBODY
		$this->table.='<tbody id="tbody-sparkline" >';
		
		$accum = 0;
		
		$row = 0;
		foreach( $arr as $key => $value)
		{
			$row++;
			$this->table.= '<tr>';
			// $this->table.=sprintf('<td class="">%s</td>',$row+$this->pagination->offset);
			// self::tdfy($key,$value,$k,$v);	
			
			foreach( $value as $k => $v)
			{
			
				$sub=false;
				
				if($k=='Sub')  $sub= true;
					
				$class = '';
				
				if($k=='Evolução')
				{
			    	$td=sprintf('<td data-sparkline="%s"/>',$v);
			    	$show=true;
				}else{
					list($td,$show) = self::tdfy($key,$value,$k,$v);			
				}
			    
			    
				if($show == false ) continue;
	
				// if($k=='Modelo' and isset( $value['_ISBN'])) 
				// 	$v=sprintf('<a class="nav-link winbox-iframe pl-1 cursor-pointer" rel="/K3/tip/vproduct/%s/" title="%s"> %s </a>', $value['_ISBN'], $v, $v );
				
				$this->table.= $td;
				
				if($sub==true) 
				{
					$accum += $v;
					// $this->table.= '<th  class="p-3">'.number_format($accum,0,',','.').'</th>';
				}
			
			}
			
			$this->table.= '</tr>';
		}

		$this->table.='</tbody>';
		if($this->subtotal>0) 
		{
			$this->table.= sprintf('<tfoot><tr><td colspan=%s>', $colCounter-1) ;
		    if($this->qty > 0) $this->table.= sprintf('<td colspan=b1><span class="text-right">%s</span></td>', $this->qty) ;
			$this->table.= sprintf('<td colspan=b1><span class="text-right">%s</span></td>', $this->subtotal) ;
			$this->table.= sprintf('</tr></tfoot>') ;
		}
		$this->table.='</table> ';

		return $this->table;
		
	}

	function head($key)
	{
		$sub= false;
		 
		$exp = preg_split("/[\.]+/", $key);
        
        $field = $exp[0];
        
		$fieldName=  ( preg_replace(["/([A-Z]+)/", "/_([A-Z]+)([A-Z][a-z])/"], ["<br>$1", "_$1_$2"], ($field) ) );
		
		if($key=='_ISBN') $fieldName='Imagem';
		
		$nkey = '<th  class="p-3">'.$fieldName.'</th>';	
		
		if($field=='Sub')  $sub= true;
			

		return array ($nkey);
	}


	function recursive($arr)
	{
		  //return "";
		
		  if(!isset($arr[0]))  return "";
		
		 $tbl='<table border="1" class="table border-separate space-y-6 text-sm">';
		 $tbl.='<thead>';
		 
		 
		 foreach($arr[0] as $title => $var)
			{
					$tbl.='<th>'.$title.'</th>';
			}
			$tbl.='</thead>';		
			
		 foreach($arr as $key => $val)
		 {
		    if(is_array($val))
		    {
		    		$tbl.='<tr>';		
				 	  foreach($val as $k => $v)
						{
									if(is_array($v))
									{
						 				$tbl.='<td>'.implode('-',$v).'</td>';
									}else{
									    $tbl.='<td>'.$v.'</td>';
									}
						}
						$tbl.='</tr>';
		    }
		 }
		 
	
		 
		 $tbl.='</table>';
		 
		 return $tbl;
		
	}
	
	function tdfy($key,$value,$k,$v)
	{
		 if($k=='Subtotal') $this->subtotal+=$v;
		 if($k=='QTD') $this->qty+=$v;
		// if(is_array($v)) return array ('<td>array</td>', true);
		
		if(is_array($v))
		{ 
			  $str = self::recursive($v);
			  $td = sprintf('<td>%s</td>',$str);
			  return array ($td, true);
			  
			 // $nv = implode(',', array_map('implode', $v, array_fill(0, count($v), '')));
			 // $td = sprintf('<td>%s</td>','$str');
				// return array ($td, $show);
		}
		
		
		$class = '';
		$color = '';
		$rule = '';
		$editable = '';
		$format =  '';
		$comma = '.';
		$separator = ',';
		$decimals =  0;
		$posfix = '';
		$show = true;
		
		// if(is_numeric($v)) $class.=' text-right';
		
		$nv = $v;
		
	
		if(isset($this->format[$k]) )
		{
			$format = $this->format[$k];
		}
		
		
		$span_color='';
		
		if(isset($this->color[$k]) and 	!is_null($v))
		{
			
			if(isset($this->color[$k][0]) )
			{
				 //s($this->color[$k]);
				 foreach($this->color[$k] as $kc => $cv)
				 {
				 	
				 	$color = $cv['class'];
					$rule  = $cv['rule'];
					$rules= explode(' ', trim($rule));
					
					if(count($rules)==2)
					{
						
						eval(" if($v $rule) { \$span_color = '$color' ; } ") ;	
					}
					
					if(count($rules)==3  )
					{
						if(is_numeric($rules[2]) )
						{
							$code= $value[$rules[0]].$rules[1].$rules[2];
						}else{
							$code= $value[$rules[0]].$rules[1].$value[$rules[2]];
						}
						eval(" if($code) { \$span_color = '$color' ; } ") ;	
						
					}

				 
				 }
			// s($rule);

			}else{
				
				$color = $this->color[$k]['class'];
				
				
		
				if(isset($this->color[$k]['rule'])) 
				{
					$rule  = $this->color[$k]['rule'];

			
					if(!empty($rule) )
					{
						$rules= explode(' ', trim($rule));
						
						if(count($rules)==2)
						{
							eval(" if($v $rule) { \$span_color = '$color' ; } ") ;	
						}
						
						if(count($rules)==3)
						{
							// s($rules);
							$code= $value[$rules[0]].$rules[1].$value[$rules[2]];
							eval(" if($code) { \$span_color = '$color' ; } ") ;	
						}
					}
				}else{
						eval("  \$span_color = '$color' ;  ") ;	
				}	
		 	 //$php = " $v $rule ";
		 	 //eval("  if($v $rule) { echo\"$php\"; } ") ;
		 	 
		 	 
		 	// / if( $r == true ) $span_color = $color ;
		 	
		 }

		}
		
		

		if(isset($this->editable[$k]) )
		{
			$edtExp = explode('|',$this->editable[$k]);
			$edtExp[3] = $value[$edtExp[3]];
			$edt =  implode('|',$edtExp);
			$editable = sprintf(" id='%s' ",$edt);
			$class.=  'editable text-green-500';
		}
	   
                            
		if(isset( $value['_ISBN']) and $k=='_ISBN') 
		{
			$nv=sprintf('<img class="rounded-full h-30 w-30  object-cover" src="https://img.rolemak.com.br/id/w64/%s.jpg"/>', $value['_ISBN'] );
		 }else{
			
			if(substr($k,0,1)=='_')  $show = false;  // hide field starting with _
		}	
		
		if(isset( $value['Marca']) and $k=='Marca') 
		{
			// https://cdn.rolemak.com.br/svg/marca/zoje.svg?version=7.73
			if($value['Marca']<>'')
			$nv=sprintf('<img class="h-6 " src="https://cdn.rolemak.com.br/svg/marca/%s.svg?version=7.73"/>', str_replace(' ','-',strtolower($value['Marca'])) );
		}	
			
		if(isset( $value['Img']) and $k=='Img') 
		{
			// https://cdn.rolemak.com.br/svg/marca/zoje.svg?version=7.73
			$nv=sprintf('<img class="rounded-full h-20   object-cover" src="%s"/>', $value['Img'] );
		}		
			
	
		if($format <> '' ) 
		{
			$class.=  ' text-right';
				
			$decimals =  substr($format,0,1);
				
			if(substr($format,1,2) == 'br' )
			{
				$comma = ',';
				$separator = '.';
			}
			
			if(substr($format,1,2) == 'kb' and $v > 0)
			{
				$posfix = 'K';
				$v = $v/1000;
				$comma = ',';
				$separator = '.';
			}
		
			if(substr($format,1,2) == 'ms' and $v > 0)
			{
				$posfix = '';
				$v = $v/1000;
				$comma = '.';
				$separator = ',';
			}
			
			// s($v,$decimals,$comma,$separator);
			$nv = number_format($v,$decimals,$comma,$separator).$posfix;
			
		
		 }else{
		 	if(is_numeric($v)) $class.=' text-right';
		 }
		 
		 if($k=='Modelo' and isset( $value['_ISBN']))
		 {
				$nv=sprintf('<a  color="#FFA500" width="60%%" height="100%%" class="nav-link winbox-iframe cursor-pointer" rel="/K3/tip/vproduct/%s/" title="%s">%s</a>', $value['_ISBN'], $v, $v );
		 }
				
		 if($k=='Cliente' and isset( $value['_IDCLI']))
		 {
				$nv=sprintf('<a  color="#FFA500"  width="90%%" height="90%%"  class="nav-link winbox-iframe cursor-pointer" rel="/K3/tip/vcustomer/%s/" title="%s">%s</a>', $value['_IDCLI'], $v, ucwords($v) );
		 }
		 
		 if($k=='Seller' and isset( $value['Seller']))
		 {
				$nv=sprintf('<a class="text-blue-600 cursor-pointer" href="%s" target="_new" title="%s">Ver</a>', $value['Seller'], $v );
		 }
		 
		 if( $k=='Anúncio' and isset( $value['Anúncio']) )
		 {
				$nv=sprintf('<a class="text-blue-600 cursor-pointer" href="%s" target="_new" title="%s">Ver</a>', $value['Anúncio'], $v );
		 }
				
		 if($k=='Pedido')
		 {
				// $nv=sprintf('<a class="nav-link winbox-iframe cursor-pointer" rel="/K3/tip/order/%s/" title="%s">%s</a>', $value['Pedido'], $v, $v );				
				$nv=sprintf('<a class="nav-link winbox-iframe cursor-pointer" rel="/crm/v4/orders/order/%s" title="%s">%s</a>', base64_encode($value['Pedido']), $v, $v );				
		 }
		 
		 	 if($k=='IDPAG' and isset( $value['IDPAG']))
		 {
				$nv=sprintf('<a  color="#FFA500" width="30%%" height="30%%" class="nav-link winbox-iframe cursor-pointer" rel="/sage/tip/payment/%s/" title="%s">%s</a>', $value['IDPAG'], $v, $v );
		 }
		 
		 	 if($k=='Fornecedor' and isset( $value['_IDFORN']))
		 {
				$nv=sprintf('<a  color="#FFA500" width="30%%" height="30%%" class="nav-link winbox-iframe cursor-pointer" rel="/sage/tip/supplier/%s/" title="%s">%s</a>', $value['_IDFORN'], $v, $v );
		 }
			
		 if($k=='MercadoLivre' and isset( $value['_ISBN']))
		 {
			
                                        
				$nv=sprintf('<button class="bg-green-900 text-white rounded px-5 py-5 mx-10 my-10" hx-swap="innerHTML" _="on click fetch /metrics/mercadolivre/index/%s
                                        put the result into me then transition opacity to 1
                                        wait 1s then transition opacity to 0
                                        put \'Atualizado Me!\' into me then transition opacity to 1" style="opacity: 1;">Ver ML</button> ', $value['_ISBN'] );
                                        
				$nv.=sprintf('<button class="bg-gray-900 text-white rounded px-5 py-5 mx-10 my-10" hx-swap="innerHTML" _="on click fetch /nodered/mercadolivre/edit?id=%s&field=price&value=%s
                                        put the result into me then transition opacity to 1
                                        wait 1s then transition opacity to 0
                                        put \'Atualizado Me!\' into me then transition opacity to 1" style="opacity: 1;">Atualizar ML</button> ', $value['_ISBN'], $v  );                                        
                                        
                                        
                                        // https://office.vallery.com.br/metrics/mercadolivre/index/100476
		 }	
				
		// $string = 'taça';
		// $name = 'café';
		// $str = 'Esta é uma $string com o meu $name nela.';
		// echo $str. "\n";
		// eval("\$str = \"$str\";");
		// echo $str . "\n";

		
		// s($nv);
		 //if($nv==0) $nv='-';
		 if($nv=="0") $nv='';
		 
		 $td = sprintf('<td style="max-width:400px;"  %s class="%s "><span class="%s" >%s</span></td>',
						$editable, 
						$class,
						$span_color, 
						$nv);
		 
		 
		
		return array ($td,$show);
	}

	function xplode($char,$str)
	{
		$expl =  explode($char,$str);
		
		return $expl;
	}

} 
