<?php defined('SYSPATH') or die('No direct script access.');


class Controller_Chat extends Controller_Website {

    public function action_index() {
        // Exibe a página inicial do chat
        $view = View::factory('chat/index');
        $this->response->body($view);
    }

    public function action_send_message() {
        // Obtém a mensagem do POST
        $message = $this->request->post('message');
        
        // Chamada para a API do ChatGPT usando cURL
        $apiKey = '***********************************************************************************************************************************************************';
        $url = 'https://api.openai.com/v1/chat/completions';

        $data = [
            "model" => "gpt-4",
            "messages" => [
                ["role" => "system", "content" => "You are a helpful assistant."],
                ["role" => "user", "content" => $message]
            ]
        ];

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey,
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

        $result = curl_exec($ch);
        curl_close($ch);

        // Converte o resultado em um objeto PHP
        $response = json_decode($result, true);

        // Extrai a resposta do ChatGPT
        $chat_response = $response['choices'][0]['message']['content'];

        // Retorna a resposta ao HTMX (ajax)
        $this->response->body($chat_response);
    }
}
