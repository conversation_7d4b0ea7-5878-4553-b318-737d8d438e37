<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Chinese extends Controller_Website {

//  private $openai_api_url = 'https://api.openai.com/v1/chat/completions'; // URL da API do GPT
    // private $api_key = '***************************************************'; // Sua chave de API do OpenAI
 
    public function before()
    {
        parent::before();
        
        
        
    }
    
    public function action_index()
    {
         $response=    $this->request->response = View::factory('chinese/home');
            


        $this->response->body($response);

    }
    


    public function action_chatgpt() {

        $userInput = $this->request->post('userInput');
        $api_key = '***************************************************'; // Substitua com sua chave de API

        $postData = json_encode([
            'model' => 'gpt-3.5-turbo-16k',
            'messages' => [['role' => 'user', 'content' => $userInput]]
        ]);

        $ch = curl_init('https://api.openai.com/v1/chat/completions');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $api_key
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        // Processar a resposta da API
        $response_data = json_decode($response, true);
        $chatMessage = $response_data['choices'][0]['message']['content'] ?? 'Não foi possível obter uma resposta.';
        $response  = array('chatResponse' => trim($chatMessage) );
        $chatResponse = json_encode($response);
        
        

        $this->response->headers('Content-Type', 'application/json');
        $this->response->body($chatResponse);
    }

   public function action_sounds()
    {
         $response=    $this->request->response = View::factory('chinese/sounds');
            


        $this->response->body($response);

    }
    

} 