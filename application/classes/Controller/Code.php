<?php

class Controller_Code extends Controller_Template {
    public $template = 'layout';

    public function before() {
        parent::before();

        // Load CSS snippets
        $css_snippets = ORM::factory('Snippet')->where('type', '=', 'css')->find_all();
        $css_content = '';
        foreach ($css_snippets as $css) {
            $css_content .= $css->content . "\n";
        }
        $this->template->css_snippets = $css_content;

        // Load JS snippets
        $js_snippets = ORM::factory('Snippet')->where('type', '=', 'js')->find_all();
        $js_content = '';
        foreach ($js_snippets as $js) {
            $js_content .= $js->content . "\n";
        }
        $this->template->js_snippets = $js_content;
    }

    // List all snippets
    public function action_index() {
        $snippets = ORM::factory('Snippet')->find_all();
        $this->template->content = View::factory('code/index')->set('snippets', $snippets);
    }

    // Upload new snippet
    public function action_upload() {
        if ($this->request->method() === Request::POST) {
            $snippet = ORM::factory('Snippet');
            $snippet->name = $this->request->post('name');
            $snippet->description = $this->request->post('description');
            $snippet->content = $this->request->post('content');
            $snippet->type = $this->request->post('type');

            // Validate content based on type
            try {
                switch ($snippet->type) {
                    case 'sql':
                        // Basic SQL validation
                        Database::instance()->query(Database::SELECT, 'EXPLAIN ' . $snippet->content);
                        break;
                    case 'php':
                        // Basic PHP syntax check
                        if (php_check_syntax($snippet->content) === false) {
                            throw new Exception('Invalid PHP syntax');
                        }
                        break;
                }

                $snippet->save();
                $this->redirect('code');
            } catch (Exception $e) {
                $this->template->content = View::factory('code/upload')
                    ->set('error', $e->getMessage())
                    ->set('snippet', $snippet);
                return;
            }
        }
        $this->template->content = View::factory('code/upload');
    }

    // View a snippet
    public function action_view() {

        $id = $this->request->param('id');

        $snippet = ORM::factory('Snippet', $id);
        if (!$snippet->loaded()) {
            throw new HTTP_Exception_404('Snippet not found');
        }

        // Handle execution if requested
        $result = null;
        if ($this->request->post('execute') && $snippet->type === 'sql') {
            try {
                $result = Database::instance()
                    ->query(Database::SELECT, $snippet->content)
                    ->execute();
            } catch (Exception $e) {
                $result = $e->getMessage();
            }
        }

        $this->template->content = View::factory('code/view')
            ->set('snippet', $snippet)
            ->set('result', $result);
    }

    // Edit a snippet
    public function action_edit() {
         $id = $this->request->param('id');
        $snippet = ORM::factory('Snippet', $id);
        if (!$snippet->loaded()) {
            throw new HTTP_Exception_404('Snippet not found');
        }

        if ($this->request->method() === Request::POST) {
            $snippet->name = $this->request->post('name');
            $snippet->description = $this->request->post('description');
            $snippet->content = $this->request->post('content');
            $snippet->type = $this->request->post('type');

            try {
                $snippet->save();
                $this->redirect('code');
            } catch (Exception $e) {
                $this->template->content = View::factory('code/edit')
                    ->set('error', $e->getMessage())
                    ->set('snippet', $snippet);
                return;
            }
        }

        $this->template->content = View::factory('code/edit')
            ->set('snippet', $snippet);
    }

    // Delete a snippet
    public function action_delete() {
         $id = $this->request->param('id');
        $snippet = ORM::factory('Snippet', $id);
        if ($snippet->loaded()) {
            $snippet->delete();
        }
        $this->redirect('code');
    }

    // Helper function to check PHP syntax
    private function php_check_syntax($code) {
        return @eval('return true;' . $code);
    }
}