<?php

class Controller_Consulta extends Controller_Website
{

   
   public function before()
    {
         parent::before();
         
    }


    public function action_df()
    {
        //  66876 - 27812036
        //  70436 - 53240502
        //  71740 - 90128802
        // 269781 - 23823425
       
        //  17478
        // "35240704348118000166550010000174781000174780";
        // $val = "422305-050139100004-75 55001000070436153240502";
        
        // $AAMM = "2406"; //date('ym');
        // $mod = '55';
        // $serie = '1';
        // $nNF = '87100';
        // $cNF = //$this->randomNumber(8, $nNF);
        // $tpEmis = '1'; // Forma de Emissão da NF-e - 1 a Normal/ 2-Contingência
        // $dEmi = date('Y-m-d');
        // $dSaiEnt = date('Y-m-d');
        // $hSaiEnt = date('H:i:s');
        // $dhEmi = date("Y-m-d\TH:i:sP");
        // $dhSaiEnt = $dhEmi;
        // $idNFeChave = $this->data['Emitente']['EmitentecUF'];  //Codigo da UF 02
        // $idNFeChave .= $AAMM; //AAMM da emissao 04
        // $idNFeChave .= $this->data['Emitente']['EmitenteCnpj']; //CNPJ do Emitente 14
        // $idNFeChave .= $mod; //Modelo 02
        // $idNFeChave .= str_repeat('0', 3 - strlen($serie)).$serie; //Serie 03
        // $idNFeChave .= str_repeat('0', 9 - strlen($nNF)).$nNF; //Numero da NF-e 09
        // $idNFeChave .= $tpEmis.$cNF; //Codigo Numerico 09
        // $cDV = $this->dvCalcMod11($idNFeChave);
        // $idNFeChave .= $cDV;

        // $segid = $this->request->param('division');
        $chave = substr($this->request->query('chave'),0,43);
        // $val = '42191181589327000142550010001699551003174528	';
        
        // $val = '3524060434811800016655001000017478100017478';
        $df =  self::dvCalcMod11($chave);
        
        // s($chave,$df);
        echo $chave,$df;
        die();
        $segid = $this->request->param('division');
        $brand = $this->request->param('xtras');
        // die('sis');
        
       
        // $this->response->body($view);
                // = $this->action_connect_SELECT($sql);
    }
    

    public function dvCalcMod11($val)
    {
        $peso = 2;
        $soma = 0;

        for ($counter = strlen($val); $counter > 0; --$counter) {
            if (10 == $peso) {
                $peso = 2;
            }
            $digit = (int) substr($val, $counter - 1, 1);
            $ponderacao = $digit * $peso;
            $soma += $ponderacao;

            //echo $digit.' * '.$peso.' = '.$ponderacao;
            //echo '<br> soma = '.$soma.'<br>';
            ++$peso;
        }

        //$soma=644;
        //echo '<br> soma=> '. $soma .'<br>';
        $div = ($soma / 11);
        //echo '<br> div=> '. $div .'<br>';
        $floor = floor($div);
        //echo '<br> div=> '. $floor .'<br>';
        $rest = $soma - ($floor * 11);
        //echo '<br> rest=> '. $rest .'<br>';
        if (0 == $rest or 1 == $rest) {
            $dv = 0;
        } else {
            $dv = 11 - $rest;
        }

        //echo '<br> dv=> '. $dv .'<br>';

        return $dv;
    }
    public function action_html()
    {
        $segid = $this->request->param('division');
        $id = $this->request->param('xtras');
      //  s($id);
             
         $sql = sprintf("SELECT html  FROM Analytics.ssw s 
                          
                            WHERE id = %s
                          
                            LIMIT 1", $id  );
             $query = DB::query(Database::SELECT, $sql);
             $result = $query->execute()->as_array();
             
            // echo $result[0]['html'];
            
            $view = parent::mustache($result,'ssw/ssw-html');

            $this->response->body($view);
        
    }


}