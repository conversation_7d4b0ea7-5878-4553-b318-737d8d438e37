<?php

class Controller_Customers extends Controller_Website
{

   
   public function before()
    {
         parent::before();
         
    }


    public function action_index()
    {     
        $segid      = $this->request->param('division');
        list($data,$found_rows)       = self::getCustomers($segid);
        // s($found_rows);
        // die();
        // $tot        = self::getTotalAmount($segid);
        
        $theme='light';
        $view = '';
        
        // s($data);
        if($this->request->query('data')=='json') 
        {
            $view  = json_encode($data);
        }
        
        // field color    
        $color['Pendentes'][0]['class'] = ' text-red-400 text-md font-bold';
        $color['Pendentes'][0]['rule']  = ' > 100 ';
        $color['Pendentes'][1]['class'] = ' text-red-600 text-xl  font-semibold';
        $color['Pendentes'][1]['rule']  = ' > 1000';
        $color['Pendentes'][2]['class'] = ' text-red-700 text-2xl  font-bold';
        $color['Pendentes'][2]['rule']  = ' > 10000 ';

        $color['UltimaCompraDias'][0]['class'] = ' text-green-400 text-md font-bold';
        $color['UltimaCompraDias'][0]['rule']  = ' > 0 ';
        $color['UltimaCompraDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['UltimaCompraDias'][1]['rule']  = ' > 30 ';
        $color['UltimaCompraDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        $color['UltimaCompraDias'][2]['rule']  = ' > 60';
        $color['UltimaCompraDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        $color['UltimaCompraDias'][3]['rule']  = ' > 90 ';
        
         $color['UltimoTicketDias'][0]['class'] = ' text-green-400 text-md font-bold';
        $color['UltimoTicketDias'][0]['rule']  = ' > 0 ';
        $color['UltimoTicketDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['UltimoTicketDias'][1]['rule']  = ' > 30 ';
        $color['UltimoTicketDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        $color['UltimoTicketDias'][2]['rule']  = ' > 60';
        $color['UltimoTicketDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        $color['UltimoTicketDias'][3]['rule']  = ' > 90 ';
        
        $data['color'] = $color;
        
        //// Editable
        
        $edt['VendedorID'] = 'mak.clientes|vendedor|id|_IDCLI';
        $data['editable'] = $edt;
        
        $idven =  $this->request->query('idven');
        $baseurl='/metrics/customers/index';
        
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=1&max=7&rows=100", 'title' => '1-7') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=30&max=60&rows=100", 'title' => '30-60') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=60&max=90&rows=100", 'title' => '60-90') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=90&max=120&rows=100", 'title' => '90-120') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=120&max=365&rows=100", 'title' => '120-365') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=180&max=240&rows=100", 'title' => '180-240') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=240&max=300&rows=100", 'title' => '240-300') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=300&max=365&rows=100", 'title' => '300-365') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=365&max=730&rows=100", 'title' => '365-730') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=730&max=1460&rows=100", 'title' => '730-1460');
        
                
        
        $data['caption'][] = array('link'=>$baseurl."/1?idven=122&min=120&max=365&rows=100", 'title' => 'Amanda-122') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=98&min=120&max=365&rows=100", 'title' => 'Angelique-98') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=83&min=120&max=365&rows=100", 'title' => 'Camila CB-83') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=170&min=120&max=365&rows=100", 'title' => 'Camilly-170') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=51&min=120&max=365&rows=100", 'title' => 'Carlos Fer') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=89&min=120&max=365&rows=100", 'title' => 'Debora-89') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=218&min=120&max=365&rows=100", 'title' => 'Ediam-218') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=32&min=120&max=365&rows=100", 'title' => 'Edilene-32') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=174&min=120&max=365&rows=100", 'title' => 'Fabiola-174') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=197&min=120&max=365&rows=100", 'title' => 'Kelly-197') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=231&min=120&max=365&rows=100", 'title' => 'Máq Livre-231') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=69&min=120&max=365&rows=100", 'title' => 'Mayara-69') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=117&min=120&max=365&rows=100", 'title' => 'Miriam-117') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=107&min=120&max=365&rows=100", 'title' => 'Regiane-107') ;
        $data['caption'][] = array('link'=>$baseurl."/1?idven=131&min=120&max=365&rows=100", 'title' => 'Rosana S-131') ;
            
        // s( $this->pagination);
        // $data['pagination'] = $this->pagination;
        $data['theme'] = 3;
        
    //   $data['found_rows'] = $found_rows;
        // $data['pager']['pagination'] = $this->pagination;
        // // die();
        
            // $data['caption'][] = array('link'=>$baseurl.'/1/?brand=zoje', 'title' => 'Zoje') ;
            // $data['caption'][] = array('link'=>$baseurl.'/1/?name=reta', 'title' => 'Retas') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=620', 'title' => '6200') ;


        
        $view.= parent::tablefy( $data );
        
        // s($data['pagination']);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);

    }

    private function getCustomers($segment=1)
    {
        // $order ='Sub DESC';
        $this->segment = $this->segments[$segment]['Name'];
        // $order =' Subtotal DESC'; 
       
        // if($this->request->query('sort')=='') $order =' Age DESC'; 
        // if($this->request->query('sort')=='last') $order =' UltimaVenda DESC'; 
        
        // $this->order = $order; 
        
        $this->pagination = Pagination::factory(array(
            'total_items'    => 100,
            'items_per_page' => 10,
        ));
       
       
        $w = ''; 

        if(isset($_GET['nome'])) {
            $w.=sprintf(" AND c.nome LIKE '%s' ", $_GET['nome'].'%' );
            $this->pagination = Pagination::factory(array(
                'total_items'    => 200,
                'items_per_page' => 5,
            ));
        }
      
       $this->pagination->items_per_page=10;
       $start =120;
       $end   =365;
        if($this->request->query('idven')) $w.=sprintf(" AND c.vendedor = '%s' ",     $this->request->query('idven'));
        if($this->request->query('min'))  $start= $this->request->query('min'); 
        if($this->request->query('max'))  $end=$this->request->query('max'); 
        if($this->request->query('rows'))  $this->pagination->items_per_page =$this->request->query('rows');
        
        
     
        //             ROUND( c.Limite - (SELECT  SUM(ch.valor) FROM cheques ch WHERE ch.idcli=c.id AND ( YEAR(datadep)=0 OR datadep> current_date ) GROUP BY ch.idcli),0) AS LimiteDisponível
        //  (SELECT DATEDIFF(NOW(), data) FROM Ecommerce.clientes_login cl WHERE cl.idcli=c.id ORDER BY cl.id DESC LIMIT 1 ) as ÚltimoAcesso,
                        
       $sql = sprintf("    
                    SELECT SQL_CALC_FOUND_ROWS
						c.id as _IDCLI,
						nome as Cliente,
						cidade as Cidade,
						estado as UF,
						u.nick as GerenteDoCliente,
						u.id as VendedorID,
						TO_DAYS(NOW()) - (SELECT TO_DAYS(max(hoje.datae)) FROM hoje WHERE  hoje.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51) and hoje.datae <= CURDATE() GROUP BY hoje.idcli ) AS UltimaCompraDias,
						                   
                        (SELECT DATEDIFF(NOW(), data) FROM crm.chamadas ch WHERE ch.cliente_id=c.id ORDER BY ch.id DESC LIMIT 1 ) as UltimoTicketDias,
                        
                        (SELECT DATEDIFF(NOW(), date) FROM webteam.clientes_visitas cv WHERE cv.idcli=c.id ORDER BY cv.id DESC LIMIT 1 ) as UltimaVisita,
                        
                        (SELECT SUM(cheques.valor) FROM cheques WHERE cheques.idcli=c.id AND cheques.data < CURDATE()-3 AND ( ISNULL(cheques.datadep) OR MONTH(cheques.datadep)=0 ) GROUP BY cheques.idcli) AS _ValorEmAtraso,
                    
                        c.Limite as LimiteLiberado,
                        
                        (SELECT  SUM(ch.valor) FROM cheques ch WHERE ch.idcli=c.id AND ( YEAR(datadep)=0 OR datadep> current_date ) GROUP BY ch.idcli) AS Pendentes
                  
                   

					FROM 	clientes c
                    LEFT JOIN mak.users u ON (u.id=c.vendedor)
                    WHERE nome <> '' AND u.segmento='%s' %s
                    GROUP BY c.id
                    HAVING UltimaCompraDias BETWEEN %s AND %s 
                    ORDER BY UltimaCompraDias DESC
            						
					LIMIT %s,%s", 
                           $this->segment,
                           $w,
                           $start,
                           $end,
                           $this->pagination->offset+$this->pagination->current_page-1,
                           $this->pagination->items_per_page);

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();
        
        $sql = "SELECT FOUND_ROWS()";
        $query = DB::query(Database::SELECT, $sql);
        $found_rows = $query->execute()->as_array();

     
        
        foreach( $result as $key => $val) 
        {
           // $result[$key]['CV'] =  self::getCV($val['_IDCLI']);
            
            foreach( $val as $k => $v) 
            {
                if(empty($v)) $result[$key][$k] = 0;
            }
            
            // if($val['UltimaCompra']< 1) $result[$key]['UltimaCompra'] = 'nunca';
            if($val['Pendentes']< 1) $result[$key]['Pendentes'] = 0.00;


        }


        
    

        return  array($result,$found_rows[0]['FOUND_ROWS()']);

    }

  
    private function getCV($idcli=1)
    {
        
        $w=sprintf(" AND c.id='%s'",$idcli);

        // e.contatos as Contatos,
        $sql = sprintf("
                    SELECT u.id as _IDVEN,u.nick as Vendedor,u.segmento as Divisão, contact_rate as ContactRate,
                    c.recency AS Recência,
                    

                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and 
                    YEAR(h.data)= YEAR(CURRENT_DATE - INTERVAL 2 MONTH) AND MONTH(h.data)=  MONTH(CURRENT_DATE - INTERVAL 2 MONTH) and h.vendedor=cv.idven and  h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51)  GROUP BY h.idcli ),0) AS  MesRetrasado,

                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and YEAR(h.data)= YEAR(CURRENT_DATE - INTERVAL 1 MONTH) AND MONTH(h.data)=  MONTH(CURRENT_DATE - INTERVAL 1 MONTH) and  h.vendedor=cv.idven and  h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id  and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51)  GROUP BY h.idcli ),0) AS  MesPassado,
                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  
                            FROM hoje h, hist hi, inv i, produtos p  
                            WHERE   h.id > 1220000 and 
                                    YEAR(h.data)=YEAR(NOW()) AND 
                                    MONTH(h.data)=MONTH(now()) and 
                                    h.vendedor=cv.idven AND
                                    h.id=hi.pedido and 
                                    hi.isbn=i.id and 
                                    i.idcf=p.id and 

                                    h.idcli=c.id  AND 
                                    ( nop=27 OR nop=28 OR nop=51)  
                            GROUP BY h.idcli ),0) AS  compras_mes,

                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(now()) and DAY(h.data)=DAY(now()) and h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id and p.segmento <> 'parts' and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51) and h.vendedor=cv.idven GROUP BY h.idcli ),0) AS  compras_hoje,


                   /* (SELECT  SUM(h.valor) FROM hoje h WHERE  h.id > 1220000 and h.idcli=c.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW()) GROUP BY h.idcli) AS Ano, */

                    /* (SELECT  SUM(h.valor) FROM hoje h WHERE  h.id > 1220000   AND ( nop=27 OR nop=28 OR nop=51)  and h.idcli=c.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW())-1 GROUP BY h.idcli) AS AnoP,  */

                     (SELECT DATEDIFF(NOW(), ch.data_retorno)  FROM crm.`chamadas` ch WHERE ch.cliente_id=c.id and MONTH(data_retorno)>0 ORDER BY ch.id DESC LIMIT 1) AS agenda

            FROM mak.clientes_vendedores cv 
            LEFT JOIN mak.clientes c ON (cv.idcli=c.id)
            LEFT JOIN crm.rfm ON (rfm.clientes_id=cv.id)
            LEFT JOIN crm.empresas e ON (e.idcli=c.id)
            LEFT JOIN mak.users u ON (u.id=c.vendedor)
            WHERE cv.ativo=1  %s
            GROUP BY cv.id ,cv.idcli
            ORDER BY contact_rate DESC
            LIMIT 10
            ", $w );

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();        
        
        $view ='';
        if(!empty($result))
        {
            $theme='light';
            $result['theme'] = 2;
            $view = parent::tablefy( $result,$theme );
        }
        
        return $view;
                           
    }


   public function action_search()
    {     
        $segid                  = $this->request->param('division');
        list($data,$found_rows) = self::searchCustomers($segid);
        // s($found_rows);
        // die();
        // $tot        = self::getTotalAmount($segid);
        
        $theme='light';
        $view = '';
        
        // s($data);
        if($this->request->query('data')=='json') 
        {
            $view  = json_encode($data);
        }
        
        // field color    
        // $color['Pendentes'][0]['class'] = ' text-red-400 text-md font-bold';
        // $color['Pendentes'][0]['rule']  = ' > 100 ';
        // $color['Pendentes'][1]['class'] = ' text-red-600 text-xl  font-semibold';
        // $color['Pendentes'][1]['rule']  = ' > 1000';
        // $color['Pendentes'][2]['class'] = ' text-red-700 text-2xl  font-bold';
        // $color['Pendentes'][2]['rule']  = ' > 10000 ';

        // $color['UltimaCompraDias'][0]['class'] = ' text-green-400 text-md font-bold';
        // $color['UltimaCompraDias'][0]['rule']  = ' > 0 ';
        // $color['UltimaCompraDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        // $color['UltimaCompraDias'][1]['rule']  = ' > 30 ';
        // $color['UltimaCompraDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        // $color['UltimaCompraDias'][2]['rule']  = ' > 60';
        // $color['UltimaCompraDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        // $color['UltimaCompraDias'][3]['rule']  = ' > 90 ';
        
        //  $color['UltimoTicketDias'][0]['class'] = ' text-green-400 text-md font-bold';
        // $color['UltimoTicketDias'][0]['rule']  = ' > 0 ';
        // $color['UltimoTicketDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        // $color['UltimoTicketDias'][1]['rule']  = ' > 30 ';
        // $color['UltimoTicketDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        // $color['UltimoTicketDias'][2]['rule']  = ' > 60';
        // $color['UltimoTicketDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        // $color['UltimoTicketDias'][3]['rule']  = ' > 90 ';
        
        // $data['color'] = $color;
        
        //// Editable
        
        $edt['VendedorID'] = 'mak.clientes|vendedor|id|_IDCLI';
        $edt['CNPJ'] = 'mak.clientes|cnpj|id|_IDCLI';
        $data['editable'] = $edt;
        
        $idven =  $this->request->query('idven');
        $baseurl='/metrics/customers/index';
        
        // // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=1&max=7&rows=100", 'title' => '1-7') ;
        // // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=30&max=60&rows=100", 'title' => '30-60') ;
        // // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=60&max=90&rows=100", 'title' => '60-90') ;
        // // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=90&max=120&rows=100", 'title' => '90-120') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=120&max=365&rows=100", 'title' => '120-365') ;
        // // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=180&max=240&rows=100", 'title' => '180-240') ;
        // // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=240&max=300&rows=100", 'title' => '240-300') ;
        // // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=300&max=365&rows=100", 'title' => '300-365') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=365&max=730&rows=100", 'title' => '365-730') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=$idven&min=730&max=1460&rows=100", 'title' => '730-1460');
        
                
        
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=122&min=120&max=365&rows=100", 'title' => 'Amanda-122') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=98&min=120&max=365&rows=100", 'title' => 'Angelique-98') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=83&min=120&max=365&rows=100", 'title' => 'Camila CB-83') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=170&min=120&max=365&rows=100", 'title' => 'Camilly-170') ;
        // // $data['caption'][] = array('link'=>$baseurl."/1?idven=51&min=120&max=365&rows=100", 'title' => 'Carlos Fer') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=89&min=120&max=365&rows=100", 'title' => 'Debora-89') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=218&min=120&max=365&rows=100", 'title' => 'Ediam-218') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=32&min=120&max=365&rows=100", 'title' => 'Edilene-32') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=174&min=120&max=365&rows=100", 'title' => 'Fabiola-174') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=197&min=120&max=365&rows=100", 'title' => 'Kelly-197') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=231&min=120&max=365&rows=100", 'title' => 'Máq Livre-231') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=69&min=120&max=365&rows=100", 'title' => 'Mayara-69') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=117&min=120&max=365&rows=100", 'title' => 'Miriam-117') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=107&min=120&max=365&rows=100", 'title' => 'Regiane-107') ;
        // $data['caption'][] = array('link'=>$baseurl."/1?idven=131&min=120&max=365&rows=100", 'title' => 'Rosana S-131') ;
            
        // // s( $this->pagination);
        // $data['pagination'] = $this->pagination;
        $data['theme'] = 2;
        
    //   $data['found_rows'] = $found_rows;
        // $data['pager']['pagination'] = $this->pagination;
        // // die();
        
            // $data['caption'][] = array('link'=>$baseurl.'/1/?brand=zoje', 'title' => 'Zoje') ;
            // $data['caption'][] = array('link'=>$baseurl.'/1/?name=reta', 'title' => 'Retas') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=620', 'title' => '6200') ;


        
        $view.= parent::tablefy( $data );
        
        // s($data['pagination']);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);

    }

    private function searchCustomers($segment=1)
    {
        
        // s($this->request->post());
        $this->segment = $this->segments[$segment]['Name'];
        $this->pagination = Pagination::factory(array(
            'total_items'    => 100,
            'items_per_page' => 10,
        ));
       
       
        $w = ''; 

        if(isset($_GET['nome'])) {
            $w.=sprintf(" AND c.nome LIKE '%s' ", $_GET['nome'].'%' );
            $this->pagination = Pagination::factory(array(
                'total_items'    => 200,
                'items_per_page' => 5,
            ));
        }
      
       $this->pagination->items_per_page=100;
       $start =120;
       $end   =365;
       
        // if($this->request->post('idven')) $w.=sprintf(" AND c.vendedor = '%s' ",     $this->request->post('idven'));
        // if($this->request->post('min'))  $start= $this->request->post('min'); 
        // if($this->request->post('max'))  $end=$this->request->post('max'); 
        // if($this->request->post('rows'))  $this->pagination->items_per_page =$this->request->post('rows');
        
        if($this->request->post('nome')) $w.=sprintf(" AND ( c.nome LIKE '%s' or c.cidade LIKE '%s' or c.bairro LIKE '%s' or c.cep LIKE '%s' ) ",    
                                                        '%'.$this->request->post('nome').'%', 
                                                        '%'.$this->request->post('nome').'%', 
                                                        '%'.$this->request->post('nome').'%', 
                                                        $this->request->post('nome').'%');
        
        
     
        //             ROUND( c.Limite - (SELECT  SUM(ch.valor) FROM cheques ch WHERE ch.idcli=c.id AND ( YEAR(datadep)=0 OR datadep> current_date ) GROUP BY ch.idcli),0) AS LimiteDisponível
        //  (SELECT DATEDIFF(NOW(), data) FROM Ecommerce.clientes_login cl WHERE cl.idcli=c.id ORDER BY cl.id DESC LIMIT 1 ) as ÚltimoAcesso,
                        
         $sql = sprintf("    
                    SELECT SQL_CALC_FOUND_ROWS
						c.id as _IDCLI,
						c.cnpj CNPJ,
						c.CGC,
						nome as Cliente,
						bairro as Bairro,
						cidade as Cidade,
						estado as UF,
						cep as Cep,
						cnpj,
						u.nick as GerenteDoCliente,
						 ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and YEAR(h.data)= YEAR(CURRENT_DATE - INTERVAL 1 MONTH) AND MONTH(h.data)=  MONTH(CURRENT_DATE - INTERVAL 1 MONTH) and   h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id  and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51)  GROUP BY h.idcli ),0) AS  ComprasMesPassado,
                        
                        ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and YEAR(h.data)= YEAR(CURRENT_DATE) AND MONTH(h.data)=  MONTH(CURRENT_DATE) and   h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id  and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51)  GROUP BY h.idcli ),0) AS  ComprasMesCorrente,
                        
                        ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and YEAR(h.data)= YEAR(CURRENT_DATE)  and   h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id  and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51)  GROUP BY h.idcli ),0) AS  ComprasAnoCorrente,
                        
                          ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and YEAR(h.data)= YEAR(CURRENT_DATE - INTERVAL 1 YEAR)  and   h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id  and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51)  GROUP BY h.idcli ),0) AS  ComprasAnoPassado,
                          
						u.id as _VendedorID,
						TO_DAYS(NOW()) - (SELECT TO_DAYS(max(hoje.datae)) FROM hoje WHERE  hoje.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51) and hoje.datae <= CURDATE() GROUP BY hoje.idcli ) AS UltimaCompraDias,
						                   
                        (SELECT DATEDIFF(NOW(), data) FROM crm.chamadas ch WHERE ch.cliente_id=c.id ORDER BY ch.id DESC LIMIT 1 ) as _UltimoTicketDias,
                        
                        (SELECT DATEDIFF(NOW(), date) FROM webteam.clientes_visitas cv WHERE cv.idcli=c.id ORDER BY cv.id DESC LIMIT 1 ) as _UltimaVisita,
                        
                        (SELECT SUM(cheques.valor) FROM cheques WHERE cheques.idcli=c.id AND cheques.data < CURDATE()-3 AND ( ISNULL(cheques.datadep) OR MONTH(cheques.datadep)=0 ) GROUP BY cheques.idcli) AS _ValorEmAtraso,
                    
                        c.Limite as LimiteLiberado,
                        
                        
                       
                    
                        (SELECT  SUM(ch.valor) FROM cheques ch WHERE ch.idcli=c.id AND ( YEAR(datadep)=0 OR datadep> current_date ) GROUP BY ch.idcli) AS Pendentes
                  
                   

					FROM 	clientes c
                    LEFT JOIN mak.users u ON (u.id=c.vendedor)
                    WHERE cgc <> '' AND  nome <> '' AND u.segmento='%s' %s
                    GROUP BY c.id
                    
                    ORDER BY Cliente ASC
            						
					LIMIT %s,%s", 
                           $this->segment,
                           $w,
                           $this->pagination->offset+$this->pagination->current_page-1,
                           $this->pagination->items_per_page);


// 	FROM 	clientes c
//                     LEFT JOIN mak.users u ON (u.id=c.vendedor)
//                     WHERE nome <> '' AND u.segmento='%s' %s
//                     GROUP BY c.id
//                     HAVING UltimaCompraDias BETWEEN %s AND %s 
//                     ORDER BY UltimaCompraDias DESC
            						
// 					LIMIT %s,%s", 
//                           $this->segment,
//                           $w,
//                           $start,
//                           $end,
//                           $this->pagination->offset+$this->pagination->current_page-1,
//                           $this->pagination->items_per_page);
                           

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();
        
        $sql = "SELECT FOUND_ROWS()";
        $query = DB::query(Database::SELECT, $sql);
        $found_rows = $query->execute()->as_array();

     
        
        foreach( $result as $key => $val) 
        {
           // $result[$key]['CV'] =  self::getCV($val['_IDCLI']);
            
            foreach( $val as $k => $v) 
            {
                if(empty($v)) $result[$key][$k] = 0;
            }
            
            // if($val['UltimaCompra']< 1) $result[$key]['UltimaCompra'] = 'nunca';
            if($val['Pendentes']< 1) $result[$key]['Pendentes'] = 0.00;


        }


        
    

        return  array($result,$found_rows[0]['FOUND_ROWS()']);

    }

    public function action_cnpj()
    {
        
        
        $segid      = $this->request->param('division');
        $cnpj      = $this->request->param('xtras');
        
        // s($segid,$cnpj);
        
         echo $sql = sprintf("    
                    SELECT 
                            SQL_CALC_FOUND_ROWS
                            id as _IDCLI,
                            nome,
                            ender,
                            bairro,
                            cidade,
                            estado,
                            limite,
                            cgc,
                            cnpj,
                            TRIM(REPLACE(REPLACE(REPLACE(cgc, '.', ''), '/', ''), '-', '')) as x
	
					FROM 	clientes c
					WHERE cnpj ='%s' or '%s' =TRIM(REPLACE(REPLACE(REPLACE(cgc, '.', ''), '/', ''), '-', '')) 
					LIMIT 10",
					$cnpj,$cnpj
                           );
                           
        $query = DB::query(Database::SELECT, $sql);
        $data = $query->execute()->as_array();
        
         s($data);

        $theme='dark';
        $view = '';

        // field color    
        $color['Pendentes'][0]['class'] = ' text-red-400 text-md font-bold';
        $color['Pendentes'][0]['rule']  = ' > 100 ';
        $color['Pendentes'][1]['class'] = ' text-red-600 text-xl  font-semibold';
        $color['Pendentes'][1]['rule']  = ' > 1000';
        $color['Pendentes'][2]['class'] = ' text-red-700 text-2xl  font-bold';
        $color['Pendentes'][2]['rule']  = ' > 10000 ';

        $color['UltimaCompraDias'][0]['class'] = ' text-green-400 text-md font-bold';
        $color['UltimaCompraDias'][0]['rule']  = ' > 0 ';
        $color['UltimaCompraDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['UltimaCompraDias'][1]['rule']  = ' > 30 ';
        $color['UltimaCompraDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        $color['UltimaCompraDias'][2]['rule']  = ' > 60';
        $color['UltimaCompraDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        $color['UltimaCompraDias'][3]['rule']  = ' > 90 ';
        
         $color['UltimoTicketDias'][0]['class'] = ' text-green-400 text-md font-bold';
        $color['UltimoTicketDias'][0]['rule']  = ' > 0 ';
        $color['UltimoTicketDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['UltimoTicketDias'][1]['rule']  = ' > 30 ';
        $color['UltimoTicketDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        $color['UltimoTicketDias'][2]['rule']  = ' > 60';
        $color['UltimoTicketDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        $color['UltimoTicketDias'][3]['rule']  = ' > 90 ';
        
        $data['color'] = $color;
        
        //// Editable
        
        $edt['cnpj'] = 'mak.clientes|cnpj|id|_IDCLI';
        $data['editable'] = $edt;
        
        $data['theme'] = 3;
        
        $view.= parent::tablefy( $data );
        
       
        $response = parent::pagefy($view,$theme);
       
        $this->response->body($response);
        
     
    }
    
     public function action_update_cnpj()
    {
        
        
        $segid      = $this->request->param('division');
        $cnpj      = $this->request->param('xtras');
        
        s($segid,$cnpj);
        
        // diw()
        
         $sql = sprintf("    
                    SELECT 
                            SQL_CALC_FOUND_ROWS
                            id,
                            nome,
                            ender,
                            bairro,
                            cidade,
                            estado,
                            limite,
                            cgc,
                            cnpj,
                            TRIM(REPLACE(REPLACE(REPLACE(cgc, '.', ''), '/', ''), '-', '')) as x
						
                  
                   

					FROM 	clientes c
					WHERE cnpj ='%s' and cgc <> ''  and cgc <> '.'

					LIMIT 10",
					$cnpj
                           );
                           
        // $s =   "REPLACE('cgc, '.', '')"

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();
        
        s($result);
        
        
        // $sql = sprintf("UPDATE clientes SET cnpj= TRIM(REPLACE(REPLACE(REPLACE(cgc, '.', ''), '/', ''), '-', '')) WHERE cnpj ='' and cgc <> ''  and cgc <> '.' LIMIT 10	"      );
                           
        

        // $query = DB::query(Database::UPDATE, $sql);
        // $result = $query->execute();
        
        // s($sql);
        
        // $sql = "SELECT FOUND_ROWS()";
        // $query = DB::query(Database::SELECT, $sql);
        // $found_rows = $query->execute()->as_array();
    }
}