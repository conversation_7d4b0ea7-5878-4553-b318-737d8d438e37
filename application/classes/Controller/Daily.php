<?php

use thiagoalessio\TesseractOCR\TesseractOCR;

class Controller_Daily extends Controller_Website
{
    public function before()
    {
        parent::before();
        $this->initializeRequestParams();
    }

    private function initializeRequestParams()
    {
        $this->cnpj = $this->request->query('cnpj') ?? null;
        $this->segid = $this->request->param('division');
        $this->brand = $this->request->param('xtras');
        $this->dateArray = explode('-', $this->request->query('data'));
        $this->sqlOrder = $this->request->query('order') ?? " s.id desc ";
        $this->sqlSeller = $this->request->query('seller') ?? 'rolemak';
        $this->sellerCnpj = $this->getSellerCnpj($this->sqlSeller);
        $this->sqlBairro = $this->request->query('bairro') ?? 'luz';
        $this->sqlCidade = $this->request->query('cidade') ?? null;
        $this->sqlMeso = $this->request->query('meso') ?? null;
        $this->sqlEstado = $this->request->query('estado') ?? null;
        $this->sqlGroup = $this->request->query('group') ?? 'bairro';
        $this->modelo = $this->request->query('group') ?? 'nfe';
        $this->nfe = $this->request->query('group') ?? 'nfe';
        $this->uri_original = $_SERVER['REQUEST_URI'];
        $this->setUrlsBasedOnSeller($this->sqlSeller);
        $this->view = "ssw";
        $this->sqlHaving = "";
        $this->where = "";
    }

    private function getSellerCnpj($seller)
    {
        switch ($seller) {
            case 'welttec':
                return '08088938000893';
            case 'ss':
                return '05013910000475';
            default:
                return null;
        }
    }

    private function setUrlsBasedOnSeller($seller)
    {
        $url = str_replace('//', '/', $_SERVER['HTTP_X_FORWARDED_PREFIX'] . $_SERVER['REQUEST_URI']);
        if ($seller === 'rolemak') {
            $this->uri_green = $url;
            $this->uri_blue = str_replace('rolemak', 'welttec', $url);
            $this->uri_red = str_replace('rolemak', 'ss', $url);
        } elseif ($seller === 'welttec') {
            $this->uri_blue = $url;
            $this->uri_green = str_replace('welttec', 'rolemak', $url);
            $this->uri_red = str_replace('welttec', 'ss', $url);
        } elseif ($seller === 'ss') {
            $this->uri_red = $url;
            $this->uri_green = str_replace('=ss', '=rolemak', $url);
            $this->uri_blue = str_replace('=ss', '=welttec', $url);
        }
    }

    public function action_index()
    {
        $array['rolemak'] = $this->daily_rolemak();
        $array['blue'] = $this->daily_ssw();

        $daily = new Daily($this);
        $daysArray = $daily->process($array);
        $daysArray['cnpjs'] = $this->getSswCnpjRank();
        $daysArray['group'] = $this->getCnpjGroup($this->cnpj);

        $view = parent::mustache($daysArray, 'ssw/daily');
        $this->response->body($view);
    }

    private function daily_ssw()
    {
        $where = $this->buildWhereClause();
        $select = $this->buildSelectClause();
        $title = $this->buildTitle();

        $sql = sprintf("SELECT  '%s' as seller,
                                '%s' as grupo,
                                '%s' as title,
                                idcli,
                                nome as nome,
                                ender,
                                bairro,
                                cidade,
                                estado,
                                trim(lcase(%s)) AS Modelo,
                                EXTRACT(YEAR FROM s.data)  AS Ano,
                                EXTRACT(MONTH FROM s.data)  AS Mes,
                                EXTRACT(DAY FROM s.data)  AS Dia,
                                EXTRACT(YEAR_MONTH FROM s.data)  AS DT,
                                s.data  AS DATA,
                                SUM(IF( (s.peso/s.volumes*2)>50,round(s.volumes/2,0),0)) as QT
                            FROM Analytics.ssw_view s
                            WHERE 1=1 and s.seller='%s' %s
                            GROUP BY s.data
                            ORDER BY Ano Desc, Mes Desc, Dia Asc
                            LIMIT 4000",
                            $this->sqlSeller,
                            $this->sqlGroup,
                            $title,
                            $select,
                            $this->sqlSeller,
                            $where);

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

        return $result;
    }

    private function daily_rolemak()
    {
        $where = $this->buildWhereClause();
        $select = $this->buildSelectClause();
        $title = $this->buildTitle();

        $sql = sprintf("SELECT  '%s' as seller,
                                '%s' as grupo,
                                '%s' as title,
                                idcli id,
                                nome as nome,
                                '' ender,
                                bairro bairro,
                                nomemunic as cidade,
                                uf estado,
                                trim(lcase(%s)) AS Modelo,
                                EXTRACT(YEAR FROM s.data)  AS Ano,
                                EXTRACT(MONTH FROM s.data)  AS Mes,
                                EXTRACT(DAY FROM s.data)  AS Dia,
                                EXTRACT(YEAR_MONTH FROM s.data)  AS DT,
                                s.data  AS DATA,
                                SUM(IF(valor > 1300, quant, 0 )) as QT
                            FROM  mak.`vPedidos` s
                            WHERE Year(data)>2020 and segmento='machines' %s
                            GROUP BY  Ano , Mes, Dia
                            ORDER BY Ano Desc, Mes Desc, Dia Asc
                            LIMIT 1000",
                            $this->sqlSeller,
                            $this->sqlGroup,
                            $title,
                            $select,
                            $where);

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

        return $result;
    }

    private function buildWhereClause()
    {
        $where = '';
        if ('cnpj' == $this->sqlGroup) {
            $var = $this->cnpj ?? '07702018000184';
            $where .= sprintf(" AND ( s.cnpj='%s' OR s.cnpj in (SELECT cnpj FROM Analytics.`ssw_group` WHERE name = (SELECT name FROM Analytics.`ssw_group` WHERE cnpj='%s' limit 1)))", $var, $var);
        }
        if ('estado' == $this->sqlGroup) {
            $where .= sprintf(" AND estado='%s' ", $this->sqlEstado);
        }
        if ('meso' == $this->sqlGroup) {
            $where .= sprintf(" AND Meso='%s' ", str_replace("'", "\'", $this->sqlMeso));
        }
        if ('cidade' == $this->sqlGroup) {
            $where .= sprintf(" AND cidade='%s' ", str_replace("'", "\'", $this->sqlCidade));
        }
        if ('bairro' == $this->sqlGroup) {
            $where .= sprintf(" AND bairro='%s' ", str_replace("'", "\'", $this->sqlBairro));
        }
        return $where;
    }

    private function buildSelectClause()
    {
        switch ($this->sqlGroup) {
            case 'cnpj':
                return "s.cnpj";
            case 'estado':
                return "estado";
            case 'meso':
                return "nomeMunic";
            case 'cidade':
                return "cidade";
            case 'bairro':
                return "bairro";
            default:
                return "s.cnpj";
        }
    }

    private function buildTitle()
    {
        switch ($this->sqlGroup) {
            case 'cnpj':
                return sprintf(" %s por cnpj : %s", $this->sqlSeller, $this->cnpj);
            case 'estado':
                return sprintf(" %s por %s : %s", $this->sqlSeller, 'estado', $this->sqlEstado);
            case 'meso':
                return ' Agrupado por Meso região : ' . $this->sqlMeso;
            case 'cidade':
                return sprintf(" %s por %s : %s", $this->sqlSeller, 'cidade', $this->sqlCidade);
            case 'bairro':
                return sprintf(" %s por %s : %s", $this->sqlSeller, 'bairro', $this->sqlBairro);
            default:
                return ' XRAY ';
        }
    }

    private function getSswCnpjRank()
    {
        $sql = "SELECT lcase(nome) nome, cnpj
                FROM Analytics.`ssw_rank`
                WHERE estado <> ''
                LIMIT 50";

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

        return $result;
    }

    private function getCnpjGroup($cnpj)
    {
        $sql = sprintf("SELECT lcase(nome) nome, lcase(ender) ender, lcase(bairro) bairro, lcase(cidade) cidade, estado, cnpj
                        FROM mak.clientes
                        WHERE cnpj IN (SELECT cnpj FROM Analytics.`ssw_group` WHERE name = (SELECT name FROM Analytics.`ssw_group` WHERE cnpj='%s' limit 1))",
                        $cnpj);

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

        return $result;
    }

    public function action_nfes()
    {
        $seller = $this->sqlSeller ?? 'welttec';

        $sql = sprintf("SELECT  DATE_FORMAT(data, '%%Y-%%m') as MonthYear,
                                MIN(`nfe`) as MinNfe, MAX(`nfe`) as MaxNfe,
                                RIGHT(MAX(`nfe`),5)-RIGHT(MIN(`nfe`),5)+1 AS MOnthlyNfeTaken,
                                count(DISTINCT cnpj) as CnpjFound,
                                count(*) as QtyFound,
                                Concat(Round(count(*)/(RIGHT(MAX(`nfe`),5)-RIGHT(MIN(`nfe`),5)+1)*100,0),'%%') as PercentFound,
                                SUM(IF( (peso/volumes*2)>50,round(volumes/2,0),0)) as QT
                        FROM Analytics.ssw
                        WHERE seller = '%s'
                        GROUP BY DATE_FORMAT(data, '%%m-%%Y')
                        ORDER BY `MonthYear` DESC",
                        $seller);

        $query = DB::query(Database::SELECT, $sql);
        $data = $query->execute()->as_array();

        $theme = 'dark';
        $view = parent::tablefy($data);
        $response = parent::pagefy($view, $theme);
        $this->response->body($response);
    }

    public function action_nfRolemak()
    {
        $sql = "SELECT DATE_FORMAT(dataGeracao, '%Y-%m') as Month_Year,
                        MIN(`NFePOID`) as Min_Rate, MAX(`NFePOID`) as Max_Rate,
                        RIGHT(MAX(`NFePOID`),5)-RIGHT(MIN(`NFePOID`),5)+1 AS nfes, count(*) as Found
                FROM NFE.01103171000885_NFe
                GROUP BY DATE_FORMAT(dataGeracao, '%m-%Y')
                ORDER BY `Month_Year` DESC";

        $query = DB::query(Database::SELECT, $sql);
        $data = $query->execute()->as_array();

        $theme = 'dark';
        $view = parent::tablefy($data);
        $response = parent::pagefy($view, $theme);
        $this->response->body($response);
    }

    public function action_nfBlumenau()
    {
        $sql = "SELECT DATE_FORMAT(dataGeracao, '%Y-%m') as Month_Year,
                        MIN(`NFePOID`) as Min_Rate, MAX(`NFePOID`) as Max_Rate,
                        RIGHT(MAX(`NFePOID`),5)-RIGHT(MIN(`NFePOID`),5)+1 AS nfes, count(*) as Found
                FROM NFE.01103171000613_NFe
                GROUP BY DATE_FORMAT(dataGeracao, '%m-%Y')
                ORDER BY `Month_Year` DESC";

        $query = DB::query(Database::SELECT, $sql);
        $data = $query->execute()->as_array();

        $theme = 'dark';
        $view = parent::tablefy($data);
        $response = parent::pagefy($view, $theme);
        $this->response->body($response);
    }

    public function action_nfLoja()
    {
        $sql = "SELECT DATE_FORMAT(dataGeracao, '%Y-%m') as Month_Year,
                        MIN(`NFePOID`) as Min_Rate, MAX(`NFePOID`) as Max_Rate,
                        RIGHT(MAX(`NFePOID`),5)-RIGHT(MIN(`NFePOID`),5)+1 AS nfes, count(*) as Found
                FROM NFE.01103171000370_NFe
                GROUP BY DATE_FORMAT(dataGeracao, '%m-%Y')
                ORDER BY `Month_Year` DESC";

        $query = DB::query(Database::SELECT, $sql);
        $data = $query->execute()->as_array();

        $theme = 'dark';
        $view = parent::tablefy($data);
        $response = parent::pagefy($view, $theme);
        $this->response->body($response);
    }

    public function action_nfTatuape()
    {
        $sql = "SELECT DATE_FORMAT(dataGeracao, '%Y-%m') as Month_Year,
                        MIN(`NFePOID`) as Min_Rate, MAX(`NFePOID`) as Max_Rate,
                        RIGHT(MAX(`NFePOID`),5)-RIGHT(MIN(`NFePOID`),5)+1 AS nfes, count(*) as Found
                FROM NFE.01103171000109_NFe
                GROUP BY DATE_FORMAT(dataGeracao, '%m-%Y')
                ORDER BY `Month_Year` DESC";

        $query = DB::query(Database::SELECT, $sql);
        $data = $query->execute()->as_array();

        $theme = 'dark';
        $view = parent::tablefy($data);
        $response = parent::pagefy($view, $theme);
        $this->response->body($response);
    }

    public function action_miguel()
    {
        $sql = "SELECT * FROM Analytics.`saomiguel`";
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();
        $data = json_decode($result[3]['json'], true);

        s($data);
    }

    public function action_ocr()
    {
        echo (new TesseractOCR(APPPATH . '/cache/tmp/46505.png'))->run();
    }

    public function action_sp()
    {
        $sql = "SELECT c.id as _IDCLI, c.nome AS Cliente, bairro, cidade as cidade, estado as estado,
                        sum(volumes) as volumes,
                        sum(peso) as peso,
                        round(sum(peso)/sum(volumes),0) as rate,
                        ROUND(SUM(IF(seller='welttec' AND YEAR(s.data)=2023 and (s.peso/s.volumes*2)>50,volumes/2,0)),0) as welttec23,
                        ROUND(SUM(IF(seller='welttec' AND YEAR(s.data)=2024 and (s.peso/s.volumes*2)>50,volumes/2,0)),0) as welttec24,
                        ROUND(SUM(IF(seller='ss' and (s.peso/s.volumes*2)>50,volumes/2,0)),0) as ss,
                        (SELECT sum(IF(hi.valor_base>0,quant,0))
                            FROM mak.hist hi
                            LEFT JOIN mak.hoje h on h.id=hi.pedido
                            LEFT JOIN mak.inv i on hi.isbn=i.id
                            LEFT JOIN mak.produtos p ON (i.idcf=p.id)
                            WHERE hi.idcli=c.id and year(hi.timestamp)=2023 and segmento='machines' and nop IN (27,28)) AS mak23,
                        (SELECT sum(IF(hi.valor_base>0,quant,0))
                            FROM mak.hist hi
                            LEFT JOIN mak.hoje h on h.id=hi.pedido
                            LEFT JOIN mak.inv i on hi.isbn=i.id
                            LEFT JOIN mak.produtos p ON (i.idcf=p.id)
                            WHERE hi.idcli=c.id and year(hi.timestamp)=2024 and segmento='machines' and nop IN (27,28)) AS mak24,
                        (SELECT sum(IF(hi.valor_base>0,quant,0))
                            FROM mak.hist hi
                            LEFT JOIN mak.hoje h on h.id=hi.pedido
                            LEFT JOIN mak.inv i on hi.isbn=i.id
                            LEFT JOIN mak.produtos p ON (i.idcf=p.id)
                            WHERE hi.idcli=c.id and year(hi.timestamp)=2024 and month(hi.timestamp)=1 and segmento='machines' and nop IN (27,28)) AS mak2401,
                        (SELECT sum(IF(hi.valor_base>0,quant,0))
                            FROM mak.hist hi
                            LEFT JOIN mak.hoje h on h.id=hi.pedido
                            LEFT JOIN mak.inv i on hi.isbn=i.id
                            LEFT JOIN mak.produtos p ON (i.idcf=p.id)
                            WHERE hi.idcli=c.id and year(hi.timestamp)=2024 and month(hi.timestamp)=2 and segmento='machines' and nop IN (27,28)) AS mak2402,
                        nick
                FROM  Analytics.`ssw` s
                LEFT JOIN mak.clientes c ON ( s.cnpj=c.cnpj)
                LEFT JOIN mak.users u on u.id=c.vendedor
                WHERE YEAR(s.data) > 2022
                GROUP BY s.cnpj
                ORDER BY `ss` DESC
                LIMIT 20";

        $query = DB::query(Database::SELECT, $sql);
        $data = $query->execute()->as_array();

        $theme = 'dark';
        $view = parent::tablefy($data);
        $response = parent::pagefy($view, $theme);
        $this->response->body($response);
    }

    public function action_yearly()
    {

        $sql="SELECT
                    s.cnpj,lcase(nome) as Cliente,
                    peso _Peso,
                    volumes _Volumes,
                    IF(volumes>1, ROUND(peso/(volumes/2),1), 1) as _PesoPorVolune,
                    IF( peso/(volumes/2)>50, Sum(round(volumes/2,0)),0) as _Sets,
                    TRIM((bairro)) bairro,
                    TRIM((cidade)) Cidade,
                    Estado,
                    c.id as _IDCLI,
                    c.tags as Tags,
                    COUNT(DISTINCT c.cnpj) as _QtdClientes,
                    SUM( IF( peso/(volumes/2)>50, round(volumes/2,0),0) ) as Subtotal,
                    SUM( IF( s.seller = 'welttec' AND  year(s.data) = YEAR(NOW())-2 AND (peso/(volumes/2))>50,round(volumes/2,0),0)) as BlueQtdBeforeLastYear,
                    SUM( IF( s.seller = 'welttec' AND  year(s.data) = YEAR(NOW())-1 AND (peso/(volumes/2))>50,round(volumes/2,0),0)) as BlueQtdLastYear,
                    SUM( IF( s.seller = 'welttec' AND  year(s.data) = YEAR(NOW()) AND (peso/(volumes/2))>50,round(volumes/2,0),0)) as BlueQtdThisYear,

                    SUM( IF( s.seller = 'ss' AND  year(s.data) = YEAR(NOW())-2 AND (peso/(volumes/2))>50,round(volumes/2,0),0)) as SunQtdBeforeLastYear,
                    SUM( IF( s.seller = 'ss' AND  year(s.data) = YEAR(NOW())-1 AND (peso/(volumes/2))>50,round(volumes/2,0),0)) as SunQtdLastYear,
                    SUM( IF( s.seller = 'ss' AND  year(s.data) = YEAR(NOW()) AND (peso/(volumes/2))>50,round(volumes/2,0),0)) as SunQtdThisYear,

                    ( SELECT SUM(IF(hi.valor_base>0,hi.quant,0)) FROM mak.hoje h LEFT JOIN mak.hist hi ON (hi.pedido=h.id) LEFT JOIN mak.inv i ON (i.id=hi.isbn) LEFT JOIN mak.produtos p ON (p.id=i.idcf) WHERE YEAR(h.data) = YEAR(now())-1 AND h.nop IN (27,28,51,76) AND h.idcli=c.id AND p.segmento='machines' ) as RolemakLastYear,

                    ( SELECT SUM(IF(hi.valor_base>0,hi.quant,0)) FROM mak.hoje h LEFT JOIN mak.hist hi ON (hi.pedido=h.id) LEFT JOIN mak.inv i ON (i.id=hi.isbn) LEFT JOIN mak.produtos p ON (p.id=i.idcf) WHERE YEAR(h.data) = YEAR(now()) AND h.nop IN (27,28,51,76) AND h.idcli=c.id AND p.segmento='machines' ) as RolemakThisYear


                FROM Analytics.ssw s
                LEFT JOIN mak.clientes c on (s.cnpj=c.cnpj)
                LEFT JOIN mak.users u ON (u.id=c.vendedor)
                WHERE 1=1
                GROUP BY cnpj
                HAVING (Subtotal>10 OR BlueQtdThisYear>0)
                ORDER BY BlueQtdThisYear desc
                LIMIT 1000";

        $query = DB::query(Database::SELECT, $sql);
        $data = $query->execute()->as_array();

        $theme = 'dark';
        $view = parent::tablefy($data);
        $response = parent::pagefy($view, $theme);
        $this->response->body($response);
    }
}
