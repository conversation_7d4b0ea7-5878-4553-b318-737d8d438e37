<?php

class Controller_Do<PERSON> extends Controller
{

   
   public function before()
    {
         parent::before();
    }


    public function action_ok()
    {
        echo 'ok!';
    }
    
    public function action_index()
    {
        $resp['theme'] = 3; 
        
        $resp = self::dolar();
        
        $text = sprintf("USDBRL *%s*", $resp['USDBRL']['ask']);
        $text .= sprintf("Time %s", $resp['USDBRL']['create_date']);
        
        $users[]='+5511973362107'; // Edilene
        // $users[]='5511999672762'; // Ronald
        $users[]='+5511995070209'; // Robson
        // $users[]='5511996316891'; // Cristina
        
        // $group= '5511937789530-1626539130'; //MakMetas
        
        // echo $x= array_slice(explode('.', $_SERVER['HTTP_HOST']), -1)[0] ;
        // $s= implode('.',array_slice(explode('.', $_SERVER['HTTP_HOST']), 1)) ;
        // s($s);
        
        // echo array_slice(explode('.',$_SERVER['HTTP_HOST']),0)[0];

        //   $m === 'a' ? $this->test() : $this->r($m);
        // s( $_SERVER);
        // die();
        $host    = $_ENV["namespace"] ;
        $service = 'metrics';
        $action  = '/dolar/ok/';
        $url = sprintf('http://%s%s%s',$service,$host,$action);
        // echo "dev"===array_slice(explode('.',$_SERVER['HTTP_HOST']),0)[0]?
        //     $url=sprintf('http://%s%s',$service,$action):
        //     $url=sprintf('http://%s%s',$service.'.'.implode('.',array_slice(explode('.', $_SERVER['HTTP_HOST']),1)),$action) ;
        
        $response = Request::factory($url)
            // ->headers('content-type', 'application/json')
            ->method('GET')
            // ->headers("Authorization", $_SESSION['Authorization'])
            ->execute()
            ->body();

        s($text,$response);
        
        // return json_decode($response, true);
        // $this->response->body( json_encode($resp) );
    }


    public function dolar()
    {
        $url = 'http://economia.awesomeapi.com.br/json/last/USD-BRL/';
        $response = Request::factory($url)
            // ->headers('content-type', 'application/json')
            ->method('GET')
            // ->headers("Authorization", $_SESSION['Authorization'])
            ->execute()
            ->body();

        return json_decode($response, true);
    }
    
   
}