<?php
class Controller_Ecommerce extends Controller_Websession
{

    public function action_today()
    {
        $this->auto_render = false;

        $where = null;
        $total = 0;

        $response = array();

        if ( $this->request->param('division') )
        {
            $where.=sprintf(" AND u.segmento = '%s' ", $this->request->param('division') );
        }

        $where.=sprintf(" AND DATE(cl.data) = DATE(CURDATE()) ");

        $sql= sprintf("SELECT ns.segmento as divisao, clientes.id, cl.data, 
                              YEAR(cl.data) AS year, MONTH(cl.data) AS month, MONTHNAME(cl.data) as monthname,
                              -- clientes_atividade.atividade,
                              -- clientes_atividade.segmento_id,
                              count(DISTINCT clientes.id) as totalClientes
                         FROM mak.clientes
                         LEFT JOIN users as u on (u.id=clientes.vendedor)
                         LEFT JOIN novo_segmento as ns on ( ns.id=clientes.novo_segmento)
                         LEFT JOIN Ecommerce.clientes_login as cl on ( cl.idcli=clientes.id)
                         -- LEFT JOIN clientes_atividade on (clientes_atividade.id=clientes.atividade_id)
                         -- LEFT JOIN clientes_segmentos on (clientes_segmentos.id=clientes_atividade.segmento_id)
                         WHERE 1=1 
                         %s
                         ORDER BY totalClientes DESC", $where );

        $query = DB::query(Database::SELECT, $sql);
        $response = $query->execute()->as_array();

        //s($sql, $response);
        //die();

        if ( isset( $response[0] ) )
        {
            $total = $response[0]['totalClientes'];
        }

        return $this->response->body( $total );
    }

    public function action_sales()
    {
        $where = "";
        if($this->request->param('division')) $where.= sprintf(" AND segmento = '%s'", $this->request->param('division') );
        $sql = sprintf("SELECT * FROM Analytics.`vendas_hoje` WHERE 1=1  %s LIMIT 1 ", $where);

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

        s($result);

        $this->response->body($view);
    }

}