<?php

class Controller_Follow extends Controller_Website
{

   
   public function before()
    {
         parent::before();
         
    }

   public function action_mesos()
    { 
        
    }
    public function action_index()
    {     
        $segid      = $this->request->param('division');
        list($data,$found_rows)       = self::getCustomers('Lists.clientes_follow',$segid);
        // s($found_rows);
        // die();
        // $tot        = self::getTotalAmount($segid);
        
        $theme='dark';
        $view = '';
        
        // s($data);
        if($this->request->query('data')=='json') 
        {
            $view  = json_encode($data);
        }
        
        // field color    
        // $color['Pendentes'][0]['class'] = ' text-red-400 text-md font-bold';
        // $color['Pendentes'][0]['rule']  = ' > 100 ';
        // $color['Pendentes'][1]['class'] = ' text-red-600 text-xl  font-semibold';
        // $color['Pendentes'][1]['rule']  = ' > 1000';
        // $color['Pendentes'][2]['class'] = ' text-red-700 text-2xl  font-bold';
        // $color['Pendentes'][2]['rule']  = ' > 10000 ';

        $color['UltimaCompraDias'][0]['class'] = ' text-green-400 text-md font-bold';
        $color['UltimaCompraDias'][0]['rule']  = ' > 0 ';
        $color['UltimaCompraDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['UltimaCompraDias'][1]['rule']  = ' > 30 ';
        $color['UltimaCompraDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        $color['UltimaCompraDias'][2]['rule']  = ' > 60';
        $color['UltimaCompraDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        $color['UltimaCompraDias'][3]['rule']  = ' > 90 ';
        
         $color['UltimoTicketDias'][0]['class'] = ' text-green-400 text-md font-bold';
        $color['UltimoTicketDias'][0]['rule']  = ' > 0 ';
        $color['UltimoTicketDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['UltimoTicketDias'][1]['rule']  = ' > 30 ';
        $color['UltimoTicketDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        $color['UltimoTicketDias'][2]['rule']  = ' > 60';
        $color['UltimoTicketDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        $color['UltimoTicketDias'][3]['rule']  = ' > 90 ';
        
        $data['color'] = $color;
        
        //// Editable
        
        $edt['VendedorID'] = 'mak.clientes|vendedor|id|_IDCLI';
        $edt['ParecerComercial'] = 'mak.clientes|ParecerComercial|id|_IDCLI';
        $data['editable'] = $edt;
        
        $idven =  $this->request->query('idven');
        $baseurl='/metrics/customers/index';
        
        
            
        // s( $this->pagination);
        // $data['pagination'] = $this->pagination;
        $data['theme'] = 3;
        
    //   $data['found_rows'] = $found_rows;
        // $data['pager']['pagination'] = $this->pagination;
        // // die();
        
            // $data['caption'][] = array('link'=>$baseurl.'/1/?brand=zoje', 'title' => 'Zoje') ;
            // $data['caption'][] = array('link'=>$baseurl.'/1/?name=reta', 'title' => 'Retas') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=620', 'title' => '6200') ;


        
        $view.= parent::tablefy( $data );
        
        // s($data['pagination']);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);

    }


    public function action_spnorte()
    {
         self::process('Lists.SPNorte');
         
    }

    public function action_spsul()
    {
         self::process('Lists.SPSul');
         
    }
    
    public function action_spleste()
    {
         self::process('Lists.SPLeste');
         
    }
    
    public function action_spoeste()
    {
         self::process('Lists.SPOeste');
         
    }
    
    
        public function action_gspoeste()
    {
         self::process('Lists.GRSPOeste');
         
    }
        public function action_gspleste()
    {
         self::process('Lists.GRSPLeste');
         
    }
            public function action_gspnorte()
    {
         self::process('Lists.GRSPNorte');
         
    }
    
    public function action_gspsul()
    {
         self::process('Lists.GRSPSul');
         
    }
    
    public function action_splitoral()
    {
         self::process('Lists.SPLitoral');
         
    }
 
 
    public function action_spamericana()
    {
         self::process('Lists.SPAmericana');
         
    }      
    
      
       public function action_spassis()
    {
         self::process('Lists.SPAssis');
         
    }   
    
      
    public function action_spbauru()
    {
         self::process('Lists.SPBauru');
         
    }   
    
    public function action_spbraganca()
    {
         self::process('Lists.SPBraganca');
         
    }   
    
    public function action_spbirigui()
    {
         self::process('Lists.SPBirigui');
         
    }   
    
    public function action_spcampinas()
    {
         self::process('Lists.SPCampinas');
         
    }   
    
    public function action_spindaiatuba()
    {
        self::process('Lists.SPIndaiatuba');
         
    }   
    
    public function action_spriopreto()
    {
        self::process('Lists.SPRioPreto');
         
    }   
    
    public function action_spRibeiraoPreto()
    {
        self::process('Lists.SPRibeiraoPreto');
         
    }       
    
  
    public function action_sptiete()
    {
        self::process('Lists.SPTiete');
         
    }       
    
     public function action_scblumenau()
    {
        self::process('Lists.SCBlumenau');
    }       
     public function action_scbrusque()
    {
        self::process('Lists.SCBrusque');
    }       
    
     public function action_scflorianopolis()
    {
        self::process('Lists.SCFlorianopolis');
    }       
    
      public function action_scjoinville()
    {
        self::process('Lists.SCJoinville');
    }       
    
      public function action_scjaragua()
    {
        self::process('Lists.SCJaragua');
    }       
   
      public function action_scIndaial()
    {
        self::process('Lists.SCIndaial');
    }        
    
    public function action_scCriciuma()
    {
        self::process('Lists.SCCriciuma');
    }        
    
        public function action_scTubarao()
    {
        self::process('Lists.SCTubarao');
    }        
       public function action_scOeste()
    {
        self::process('Lists.SCOeste');
    }       
      public function action_scRioDosul()
    {
        self::process('Lists.SCRioDoSul');
    }       
    
    public function action_scSombrio()
    {
        self::process('Lists.SCSombrio');
    }       
    
    public function action_bras()
    {    
        self::process('Lists.Bras');
    }
    
    public function action_bretiro()
    {   
        self::process('Lists.BomRetiro');
    }
   
    public function action_scaetano()
    {     
        self::process('Lists.SaoCaetano');
    }
    
    public function action_list()
    {     
        $division = $this->request->param('division');
        $table = 'Lists.'.$this->request->param('xtras');    
        
        self::process($table,$division);
    }
    
    
    public function process($table,$segid=1)
    {     
        
        list($data,$found_rows)       = self::getCustomers($table,$segid);
        // s($found_rows);
        // die();
        // $tot        = self::getTotalAmount($segid);
        
        $theme='dark';
        $view = '';
        
        // s($data);
        if($this->request->query('data')=='json') 
        {
            $view  = json_encode($data);
        }
        
        // field color    
        // $color['Pendentes'][0]['class'] = ' text-red-400 text-md font-bold';
        // $color['Pendentes'][0]['rule']  = ' > 100 ';
        // $color['Pendentes'][1]['class'] = ' text-red-600 text-xl  font-semibold';
        // $color['Pendentes'][1]['rule']  = ' > 1000';
        // $color['Pendentes'][2]['class'] = ' text-red-700 text-2xl  font-bold';
        // $color['Pendentes'][2]['rule']  = ' > 10000 ';

       
        
         $color['ComprasMês'][0]['class'] = ' text-green-300 text-md font-bold';
        $color['ComprasMês'][0]['rule']  = ' > 10000 ';
        $color['ComprasMês'][1]['class'] = ' text-green-400 text-xl font-bold';
        $color['ComprasMês'][1]['rule']  = ' > 20000 ';
        $color['ComprasMês'][2]['class'] = ' text-green-500 text-2xl  font-bold';
        $color['ComprasMês'][2]['rule']  = ' > 50000';
        $color['ComprasMês'][3]['class'] = ' text-green-700 text-3xl  font-bold';
        $color['ComprasMês'][3]['rule']  = ' > 75000 ';
        
        
        $color['ComprasAno'][0]['class'] = ' text-green-200 text-md font-bold';
        $color['ComprasAno'][0]['rule']  = ' > 50000 ';
        $color['ComprasAno'][1]['class'] = ' text-green-300 text-lg font-bold';
        $color['ComprasAno'][1]['rule']  = ' > 100000 ';
        $color['ComprasAno'][2]['class'] = ' text-green-400 text-xl font-bold';
        $color['ComprasAno'][2]['rule']  = ' > 200000 ';
        $color['ComprasAno'][3]['class'] = ' text-green-500 text-2xl  font-bold';
        $color['ComprasAno'][3]['rule']  = ' > 300000';
        $color['ComprasAno'][4]['class'] = ' text-green-600 text-3xl  font-bold';
        $color['ComprasAno'][4]['rule']  = ' > 500000 ';
        
        
        $color['UltimaCompraDias'][0]['class'] = ' text-green-400 text-md font-bold';
        $color['UltimaCompraDias'][0]['rule']  = ' > 0 ';
        $color['UltimaCompraDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['UltimaCompraDias'][1]['rule']  = ' > 30 ';
        $color['UltimaCompraDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        $color['UltimaCompraDias'][2]['rule']  = ' > 60';
        $color['UltimaCompraDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        $color['UltimaCompraDias'][3]['rule']  = ' > 90 ';
        
         $color['UltimoTicketDias'][0]['class'] = ' text-green-400 text-md font-bold';
        $color['UltimoTicketDias'][0]['rule']  = ' > 0 ';
        $color['UltimoTicketDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['UltimoTicketDias'][1]['rule']  = ' > 30 ';
        $color['UltimoTicketDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        $color['UltimoTicketDias'][2]['rule']  = ' > 60';
        $color['UltimoTicketDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        $color['UltimoTicketDias'][3]['rule']  = ' > 90 ';
        
        $data['color'] = $color;
        
        //// Editable
        $edt['VendedorID'] = 'mak.clientes|vendedor|id|_IDCLI';
        $edt['ParecerComercial'] = 'mak.clientes|ParecerComercial|id|_IDCLI';
        $data['editable'] = $edt;
        
        $fmt['LimiteLiberado'] = $fmt['ComprasAno'] = $fmt['ComprasMês']  = '1kb';
        $fmt['AGE'] = '0us'; 
        $data['format'] = $fmt;
        
        $idven =  $this->request->query('idven');
        $baseurl='/metrics/customers/index';
        
        
            
        // s( $this->pagination);
        // $data['pagination'] = $this->pagination;
        $data['theme'] = 2;
        
    //   $data['found_rows'] = $found_rows;
        // $data['pager']['pagination'] = $this->pagination;
        // // die();
        
            // $data['caption'][] = array('link'=>$baseurl.'/1/?brand=zoje', 'title' => 'Zoje') ;
            // $data['caption'][] = array('link'=>$baseurl.'/1/?name=reta', 'title' => 'Retas') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=620', 'title' => '6200') ;

    
        
        $view.= parent::tablefy( $data );
        
        // s($data['pagination']);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);

    }
    
  
   
    private function getCustomers($table='clientes_follow',$segment=1)
    {
        // $order ='Sub DESC';
        $this->segment = $this->segments[$segment]['Name'];
        // $order =' Subtotal DESC'; 
       
        // if($this->request->query('sort')=='') $order =' Age DESC'; 
        // if($this->request->query('sort')=='last') $order =' UltimaVenda DESC'; 
        // $this->order = $order; 
        
        $this->pagination = Pagination::factory(array(
            'total_items'    => 100,
            'items_per_page' => 10,
        ));
       
       
        $w = ''; 

        if(isset($_GET['nome'])) {
            $w.=sprintf(" AND c.nome LIKE '%s' ", $_GET['nome'].'%' );
            $this->pagination = Pagination::factory(array(
                'total_items'    => 200,
                'items_per_page' => 5,
            ));
        }
      
       $this->pagination->items_per_page=10;
       $start =120;
       $end   =365;
        if($this->request->query('idven')) $w.=sprintf(" AND c.vendedor = '%s' ",     $this->request->query('idven'));
        if($this->request->query('min'))  $start= $this->request->query('min'); 
        if($this->request->query('max'))  $end=$this->request->query('max'); 
        if($this->request->query('rows'))  $this->pagination->items_per_page =$this->request->query('rows');
        
        
     
        //             ROUND( c.Limite - (SELECT  SUM(ch.valor) FROM cheques ch WHERE ch.idcli=c.id AND ( YEAR(datadep)=0 OR datadep> current_date ) GROUP BY ch.idcli),0) AS LimiteDisponível
        //  (SELECT DATEDIFF(NOW(), data) FROM Ecommerce.clientes_login cl WHERE cl.idcli=c.id ORDER BY cl.id DESC LIMIT 1 ) as ÚltimoAcesso,
    // FORMAT((SELECT SUM(hoje.valor) FROM hoje WHERE hoje.idcli=cf.idcli  AND (nop=27 OR nop=28 OR nop=51) AND DAY(hoje.data)=DAY(NOW()) AND MONTH(hoje.data)=MONTH(NOW()) and YEAR(hoje.data) = YEAR(NOW())),0,'de_DE') AS ComprasDia,  
    //  FORMAT((SELECT SUM(hoje.valor) FROM hoje WHERE hoje.idcli=cf.idcli  AND (nop=27 OR nop=28 OR nop=51) AND WEEK(hoje.data)=WEEK(NOW()) and YEAR(hoje.data) = YEAR(NOW()) ),0,'de_DE') AS ComprasSemana,
    //  (SELECT  SUM(ch.valor) FROM cheques ch WHERE ch.idcli=c.id AND ( YEAR(datadep)=0 OR datadep> current_date ) GROUP BY ch.idcli) AS Pendentes,
       $sql = sprintf("    
                    SELECT SQL_CALC_FOUND_ROWS
						c.id,
						c.id as _IDCLI,
						LCASE(nome) as Cliente,
						CONCAT_WS(' - ',ender,nro) as _Endereço,
						LEFT(cep,5) as Cep,
						LCASE(cidade) as Cidade,
						UCASE(estado) as UF,
						u.nick as GerenteDoCliente,
						u.id as VendedorID,
						TO_DAYS(NOW()) - (SELECT TO_DAYS(max(hoje.datae)) FROM hoje WHERE  hoje.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51) and hoje.datae <= CURDATE() GROUP BY hoje.idcli ) AS UltimaCompraDias,
						                   
                        (SELECT DATEDIFF(NOW(), data) FROM crm.chamadas ch WHERE ch.cliente_id=c.id ORDER BY ch.id DESC LIMIT 1 ) as UltimoTicketDias,
                        (SELECT DATEDIFF(NOW(), date) FROM webteam.clientes_visitas cv WHERE cv.idcli=c.id ORDER BY cv.id DESC LIMIT 1 ) as _UltimaVisita,
                        (SELECT DATEDIFF(NOW(), data) FROM Ecommerce.clientes_login cl WHERE cl.idcli=c.id ORDER BY cl.id DESC LIMIT 1 ) as _UltimoAcessoSite,
                        
                        
                        (SELECT SUM(hoje.valor) FROM hoje WHERE hoje.idcli=cf.idcli  AND (nop=27 OR nop=28 OR nop=51) AND DAY(hoje.data)=DAY(NOW()) AND MONTH(hoje.data)=MONTH(NOW()) and YEAR(hoje.data) = YEAR(NOW())) AS ComprasDia,  
                        
                        (SELECT SUM(hoje.valor) FROM hoje WHERE hoje.idcli=cf.idcli  AND (nop=27 OR nop=28 OR nop=51) AND WEEK(hoje.data)=WEEK(NOW()) and YEAR(hoje.data) = YEAR(NOW()) )AS ComprasSemana,
                        
                        (SELECT SUM(hoje.valor) FROM hoje WHERE hoje.idcli=cf.idcli  AND (nop=27 OR nop=28 OR nop=51) AND MONTH(hoje.data)=MONTH(NOW()) and YEAR(hoje.data) = YEAR(NOW()) ) AS ComprasMês,
                        (SELECT SUM(hoje.valor) FROM hoje WHERE hoje.idcli=cf.idcli  AND (nop=27 OR nop=28 OR nop=51) AND YEAR(hoje.data) = YEAR(NOW()) ) AS ComprasAno,
                        
                        
                        
                     
                        
                        (SELECT SUM(cheques.valor) FROM cheques WHERE cheques.idcli=c.id AND cheques.data < CURDATE()-3 AND ( ISNULL(cheques.datadep) OR MONTH(cheques.datadep)=0 ) GROUP BY cheques.idcli) AS ValorEmAtraso,
                    
                        c.Limite as LimiteLiberado,
                        
                      
                        
                        c.ParecerComercial
                  
                   

					FROM %s cf
					LEFT JOIN mak.clientes c ON (cf.idcli=c.id)
                    LEFT JOIN mak.users u ON (u.id=c.vendedor)
                    WHERE 1
                    GROUP BY c.id
                    
                    ORDER BY UltimaCompraDias ASC
            						
					LIMIT 100", $table,
                           
                           $this->pagination->offset+$this->pagination->current_page-1,
                           $this->pagination->items_per_page);

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();
        
        //s($result);
        
        $sql = "SELECT FOUND_ROWS()";
        $query = DB::query(Database::SELECT, $sql);
        $found_rows = $query->execute()->as_array();

     
        
        foreach( $result as $key => $val) 
        {
            // $result[$key]['CV'] =  self::getCV($val['_IDCLI']);
            
            $result[$key]['Cliente'] =  ucwords($val['Cliente']);
            $result[$key]['Cidade'] =  ucwords($val['Cidade']);
            // foreach( $val as $k => $v) 
            // {
            //     if(empty($v)) $result[$key][$k] = 0;
            // }
            
            // if($val['UltimaCompra']< 1) $result[$key]['UltimaCompra'] = 'nunca';
            // if($val['Pendentes']< 1) $result[$key]['Pendentes'] = 0.00;


        }


        
    

        return  array($result,$found_rows[0]['FOUND_ROWS()']);

    }

  
    private function getCV($idcli=1)
    {
        
        $w=sprintf(" AND c.id='%s'",$idcli);

        // e.contatos as Contatos,
        $sql = sprintf("
                    SELECT u.id as _IDVEN,u.nick as Vendedor,u.segmento as Divisão, contact_rate as ContactRate,
                    c.recency AS Recência,
                    

                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and 
                    YEAR(h.data)= YEAR(CURRENT_DATE - INTERVAL 2 MONTH) AND MONTH(h.data)=  MONTH(CURRENT_DATE - INTERVAL 2 MONTH) and h.vendedor=cv.idven and  h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51)  GROUP BY h.idcli ),0) AS  MesRetrasado,

                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and YEAR(h.data)= YEAR(CURRENT_DATE - INTERVAL 1 MONTH) AND MONTH(h.data)=  MONTH(CURRENT_DATE - INTERVAL 1 MONTH) and  h.vendedor=cv.idven and  h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id  and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51)  GROUP BY h.idcli ),0) AS  MesPassado,
                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  
                            FROM hoje h, hist hi, inv i, produtos p  
                            WHERE   h.id > 1220000 and 
                                    YEAR(h.data)=YEAR(NOW()) AND 
                                    MONTH(h.data)=MONTH(now()) and 
                                    h.vendedor=cv.idven AND
                                    h.id=hi.pedido and 
                                    hi.isbn=i.id and 
                                    i.idcf=p.id and 

                                    h.idcli=c.id  AND 
                                    ( nop=27 OR nop=28 OR nop=51)  
                            GROUP BY h.idcli ),0) AS  compras_mes,

                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(now()) and DAY(h.data)=DAY(now()) and h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id and p.segmento <> 'parts' and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51) and h.vendedor=cv.idven GROUP BY h.idcli ),0) AS  compras_hoje,


                   /* (SELECT  SUM(h.valor) FROM hoje h WHERE  h.id > 1220000 and h.idcli=c.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW()) GROUP BY h.idcli) AS Ano, */

                    /* (SELECT  SUM(h.valor) FROM hoje h WHERE  h.id > 1220000   AND ( nop=27 OR nop=28 OR nop=51)  and h.idcli=c.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW())-1 GROUP BY h.idcli) AS AnoP,  */

                     (SELECT DATEDIFF(NOW(), ch.data_retorno)  FROM crm.`chamadas` ch WHERE ch.cliente_id=c.id and MONTH(data_retorno)>0 ORDER BY ch.id DESC LIMIT 1) AS agenda

            FROM mak.clientes_vendedores cv 
            LEFT JOIN mak.clientes c ON (cv.idcli=c.id)
            LEFT JOIN crm.rfm ON (rfm.clientes_id=cv.id)
            LEFT JOIN crm.empresas e ON (e.idcli=c.id)
            LEFT JOIN mak.users u ON (u.id=c.vendedor)
            WHERE cv.ativo=1  %s
            GROUP BY cv.id ,cv.idcli
            ORDER BY contact_rate DESC
            LIMIT 10
            ", $w );

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();        
        
        $view ='';
        if(!empty($result))
        {
            $theme='light';
            $result['theme'] = 2;
            $view = parent::tablefy( $result,$theme );
        }
        
        return $view;
                           
    }


}