<?php
class Controller_Ia_Cost extends Controller_Websession
{

    public function action_index()
    {
        $response = View::factory('ia/cost');
         
        $this->response->body($response);
      
    }

    public function action_calculate()
    {
        // var_dump($_POST);
        // s($this->request->method());
        //  if (HTTP_Request::POST == $this->request->method()){
        //         // Post has no data
        //         print_r($this->request->post());

        //  }
       
        if ($this->request->method() === Request::POST) {
            // Decodifica o JSON recebido
            $dados = json_decode($this->request->body(), true);
            
            // s($dados);
        }
        
        // Receber dados da solicitação POST
        $fob = $dados['fob'];
        $frete = $dados['frete'];
        $seguro = $dados['seguro'];
        $ii = $dados['ii'];
        $ipi = $dados['ipi'];
        $pis_import = $dados['pis_import'];
        $cofins_import = $dados['cofins_import'];
        $icms = $dados['icms'];
        $margem = $dados['margem'];
        $mva = $dados['mva'];

        // Cálculo do CIF
        $cif = $fob + $frete + $seguro;

        // Cálculo dos impostos de importação
        $ii_value = $cif * ($ii / 100);
        $ipi_value = ($cif + $ii_value) * ($ipi / 100);
        $pis_import_value = $cif * ($pis_import / 100);
        $cofins_import_value = $cif * ($cofins_import / 100);

        // Cálculo do ICMS
        $icms_base = $cif + $ii_value + $ipi_value + $pis_import_value + $cofins_import_value;
        $icms_value = $icms_base / (1 - ($icms / 100)) * ($icms / 100);

        // Cálculo do CTM
        $ctm = $cif + $ii_value + $ipi_value + $pis_import_value + $cofins_import_value + $icms_value;

        // Cálculo do Preço de Venda Bruto (PVB)
        $pvb = $ctm / (1 - ($margem / 100));

        // Cálculo do ICMS-ST
        // Base de Cálculo Ajustada
        $base_calculo_st = $icms_base * (1 + ($mva / 100));
        // ICMS Final
        $icms_final = $base_calculo_st * ($icms / 100);
        // ICMS-ST
        $icms_st = $icms_final - $icms_value;

        // Retornar os valores calculados
        $result = [
            'cif' => $cif,
            'ii_value' => $ii_value,
            'ipi_value' => $ipi_value,
            'pis_import_value' => $pis_import_value,
            'cofins_import_value' => $cofins_import_value,
            'icms_value' => $icms_value,
            'ctm' => $ctm,
            'pvb' => $pvb,
            'base_calculo_st' => $base_calculo_st,
            'icms_final' => $icms_final,
            'icms_st' => $icms_st,
        ];

        $this->response->body(json_encode($result));
    }
}
