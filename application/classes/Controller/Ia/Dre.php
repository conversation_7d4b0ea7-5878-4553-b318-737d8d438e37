<?php
class Controller_<PERSON><PERSON>_Dre extends Controller_Websession
{

    public function action_show()
    {
        $response = View::factory('ia/dre')
          // ->set('css', View::factory('base/css')->set('color', $color))
          // ->set('template', View::factory('base/template-multi'))
          // ->set('scripts', View::factory('base/scripts')
            
            ;
            
        $this->response->body($response);
        //$this->response->body('Calc API');
    }


    public function action_index()
    {
        $period = $this->request->query('period'); // Ex: 2024-08-01 (mês específico)
        $dre = DB::select()->from('Analytics.dre_records')->where('period', '=', $period)->execute()->as_array();
        $this->response->body(json_encode($dre));
    }

    public function action_add()
    {
        if ($this->request->method() === Request::POST) {
            // Decodifica o JSON recebido
            $dados = json_decode($this->request->body(), true);
            
            // s($dados);
        }
        
        
        if ($this->request->method() === 'POST') {
            $description = $dados['description'];
            $value = $dados['value'];
            $period = $dados['period'];

            DB::insert('Analytics.dre_records', ['description', 'value', 'period'])
                ->values([$description, $value, $period])
                ->execute();

            $this->response->body(json_encode(['status' => 'success']));
        }
    }

    public function action_calculate()
    {
        $period = $this->request->query('period'); // Ex: 2024-08-01

        $receita_bruta = $this->getDreValue('Receita Bruta', $period);
        $deducoes = $this->getDreValue('Deduções da Receita Bruta', $period);
        $receita_liquida = $receita_bruta - $deducoes;

        $cmv = $this->getDreValue('Custo das Mercadorias Vendidas (CMV)', $period);
        $lucro_bruto = $receita_liquida - $cmv;

        $despesas_operacionais = $this->getDreValue('Despesas Operacionais', $period);
        $lucro_operacional = $lucro_bruto - $despesas_operacionais;

        $despesas_financeiras = $this->getDreValue('Despesas Financeiras', $period);
        $receitas_financeiras = $this->getDreValue('Receitas Financeiras', $period);
        $lair = $lucro_operacional - $despesas_financeiras + $receitas_financeiras;

        $impostos = $this->getDreValue('Imposto de Renda e Contribuição Social', $period);
        $lucro_liquido = $lair - $impostos;

        $dre = [
            'Receita Bruta' => $receita_bruta,
            'Deduções da Receita Bruta' => $deducoes,
            'Receita Líquida' => $receita_liquida,
            'Custo das Mercadorias Vendidas (CMV)' => $cmv,
            'Lucro Bruto' => $lucro_bruto,
            'Despesas Operacionais' => $despesas_operacionais,
            'Lucro Operacional' => $lucro_operacional,
            'Despesas Financeiras' => $despesas_financeiras,
            'Receitas Financeiras' => $receitas_financeiras,
            'Lucro Antes do IR (LAIR)' => $lair,
            'Imposto de Renda e CSLL' => $impostos,
            'Lucro Líquido' => $lucro_liquido,
        ];

        $this->response->body(json_encode($dre));
    }

    private function getDreValue($description, $period)
    {
        $result = DB::select('value')
            ->from('Analytics.dre_records')
            ->where('description', '=', $description)
            ->and_where('period', '=', $period)
            ->execute()
            ->get('value');

        return $result ?: 0;
    }
}
