<?php
class Controller_<PERSON><PERSON>_<PERSON><PERSON> extends Controller_Websession
{

    public function action_index()
    {
        $response = View::factory('ia/kanitz');  
        $this->response->body($response);
    
    }
    

    public function action_calculate()
    {
     if ($_SERVER['REQUEST_METHOD'] === 'POST') 
     {
        $ativos = $_POST['ativos'] ?? 0;
        $passivos = $_POST['passivos'] ?? 0;
        $patrimonio_liquido = $ativos-$passivos ?? 0;
        $lucro_liquido = $_POST['lucro_liquido'] ?? 0;
        $ativo_circulante = $_POST['ativo_circulante'] ?? 0;
        $realizavel_longo_prazo = $_POST['realizavel_longo_prazo'] ?? 0;
        $passivo_circulante = $_POST['passivo_circulante'] ?? 0;
        $exigivel_longo_prazo = $_POST['exigivel_longo_prazo'] ?? 0;
        $passivo_total = $_POST['passivo_total'] ?? 0;
    
        $a = $patrimonio_liquido != 0 ? $lucro_liquido / $patrimonio_liquido : 0;
        $b = ($ativo_circulante + $realizavel_longo_prazo) != 0 ? ($ativo_circulante + $realizavel_longo_prazo) / ($passivo_circulante + $exigivel_longo_prazo) : 0;
        $c = $passivo_circulante != 0 ? $ativo_circulante / $passivo_circulante : 0;
        $d = $passivo_circulante != 0 ? $ativo_circulante / $passivo_circulante : 0;
        $e = $patrimonio_liquido != 0 ? $passivo_total / $patrimonio_liquido : 0;
    
       // Calcula o Fator de Insolvência
        $fi = (0.05 * $a) + (1.65 * $b) + (3.55 * $c) - (1.06 * $d) - (0.33 * $e);

    }

        $response = sprintf('  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="p-4 bg-gray-100 rounded-lg">
                                    <p><strong>A:</strong> Rentabilidade do PL</p>
                                    <p id="a-result" class="text-blue-600 text-lg font-semibold">%s</p>
                                </div>
                                <div class="p-4 bg-gray-100 rounded-lg">
                                    <p><strong>B:</strong> Liquidez Geral</p>
                                    <p id="b-result" class="text-blue-600 text-lg font-semibold">%s</p>
                                </div>
                                <div class="p-4 bg-gray-100 rounded-lg">
                                    <p><strong>C:</strong> Liquidez Seca</p>
                                    <p id="c-result" class="text-blue-600 text-lg font-semibold">%s</p>
                                </div>
                                <div class="p-4 bg-gray-100 rounded-lg">
                                    <p><strong>D:</strong> Liquidez Corrente</p>
                                    <p id="d-result" class="text-blue-600 text-lg font-semibold">%s</p>
                                </div>
                                <div class="p-4 bg-gray-100 rounded-lg">
                                    <p><strong>E:</strong> Grau de Endividamento</p>
                                    <p id="e-result" class="text-blue-600 text-lg font-semibold">%s</p>
                                </div>
                                 <div class="p-4 bg-gray-100 rounded-lg">
                                    <p><strong>FI:</strong> Fator de Insolvencia</p>
                                    <p id="e-result" class="text-blue-600 text-lg font-semibold">%s</p>
                                </div>
                            </div>', number_format($a, 2),number_format($b, 2),number_format($c, 2),number_format($d, 2),number_format($e, 2), $fi
                            );
                            
          $this->response->body($response);
    }
}
