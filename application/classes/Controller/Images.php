
<?php

class Controller_Images extends Controller_Website
{

   
   public function before()
    {
         parent::before();
         
    }

    public function action_index()
    {
        for($x=200; $x<260; $x++)
        {
            $image_name = $x.".jpg";
            $url ="http://instruct.strongh.cn/2021/files/mobile/".$image_name;
            // $image_size = getimagesize($url);
            $image = addslashes(file_get_contents($url));
            
            // s($image_size,$image_name);
            
            $sql = "INSERT INTO parts.image_h_strong (image,image_name) VALUES('$image','$image_name')";
            $query = DB::query(Database::INSERT, $sql);
            $result = $query->execute();

        }
        
        
        
        // $image = addslashes(file_get_contents($url));

        // echo $image_name = addslashes($_FILES['image']['name']);
        // // $type=$_POST['type'];

        // echo  $image_size = getimagesize($_FILES['image']['tmp_name']);

        // if($image_size==FALSE)
        //     echo "That's not an image.";
        // else
        // {

        //     if(!(mysql_query("INSERT INTO store (name,image,type) values  ('$image_name','$image','$type')")))
        //         echo "Problem uploading image";
        //     else
        //     {
        //         $lastid = mysql_insert_id();
        //         echo "Image uploaded. <p /> Your image: <p /> <img id='imageId' src=get.php?id=$lastid>";
        //     }
        // }
        
        // $image = file_get_contents($url);
        
        // $sql = "INSERT INTO parts.image_h_strong SET image = '$image'";
        
        
        //  $query = DB::query(Database::INSERT, $sql);
        //  $result = $query->execute();
        
        
    //   $imageData = base64_encode(file_get_contents($url));
// echo '<img src="data:image/jpeg;base64,'.$imageData.'">';
die();

        // $image = file_get_contents($url);
        
        
        // header ("Content-type: image/jpeg");
        // print $image;
        // header('Location: '.$url);
        // exit;
        
        // self::get($url) ;
        
        // $this->response->body(View::factory('image_link_form'));
    }

    // Baixa a imagem do link e a armazena no banco
    private function get($url) {
        
        $file = 'your_images.jpg';

    header('Content-Type: image/jpeg');
    // header('Content-Length: ' . filesize($file));
    echo file_get_contents($url);
 die();
        // if ($this->request->method() === Request::POST) {
            // $url = $this->request->post('image_url');

            // Valida a URL e baixa a imagem
            if (filter_var($url, FILTER_VALIDATE_URL)) {
                $imageData = file_get_contents($url);

                if ($imageData !== false) {
                    $imageName = basename(parse_url($url, PHP_URL_PATH));
                    $file_size = 1000;
                    Header ("Content-type: image/jpeg");
                    print $imageData;
                    die();
                    // Salva no banco de dados
                    //$image = ORM::factory('Image');
                    self::save($imageName, $imageData, $file_size);

                    $this->response->body('Imagem salva com sucesso!');
                } else {
                    $this->response->body('Falha ao baixar a imagem.');
                }
            } else {
                $this->response->body('URL inválida!');
            }
      //  } else {
        //    $this->response->body('Método de requisição inválido!');
        //}
    }

    // Exibe uma imagem armazenada no banco pelo ID
    public function action_view($id) {
        $image = ORM::factory('Image', $id);

        if ($image->loaded()) {
            header("Content-Type: image/jpeg");
            echo $image->image_data;
        } else {
            $this->response->body('Imagem não encontrada.');
        }
    }



    private function save($imageData,$imageName,$file_size)
    {
         echo 	$sql = "INSERT INTO parts.image_h_strong "
				. "(image_type, 
				    image, 
				    image_size, 
					image_name, 
					image_date, 
					image_user) ";
			$sql.= "VALUES (";
			$sql.= "'jpg',";
			$sql.= "'{$imageData}',";
			$sql.= "'{$file_size}',";
			$sql.= "'{$imageName}',";
			$sql.= " NOW(), ";
			$sql.= $_SESSION['MM_Userid']."' )";
			
		$query = DB::query(Database::INSERT, $sql);
        $result = $query->execute();
        
        return;
			
			
			
        echo 	$sql = "INSERT INTO $image_table "
				. "(image_type, 
				    image, 
					image_thumbnail,  
					image_size, 
					image_name, 
					image_date, 
					image_user) ";
			$sql.= "VALUES (";
			$sql.= "'{$file_type}', '{$userfile}',";
			$sql.=  "'";
			$sql.=  $src;
			$sql.=  "', ";	
			$sql.=  " '{$file_size}', "
				. "'{$file_name}', NOW(), '".$_SESSION['MM_Userid']."'  
				)";
			@mysql_query ($sql, $conn);
			$go=$_SERVER["PHP_SELF"].'?SEGMENTO='.$_GET["SEGMENTO"];
			Header("Location:".$go);
			exit();
    }


    private function get_image($id)
    {
            $sql    = "SELECT image FROM  parts.image_h_strong WHERE image_id=".$id;
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute()->as_array();   
            $row =$result[0];
            return $imageData = base64_encode($row["image"]);

    }
    
    public function action_download($id=28)
    {
        
	     $file_name = 'parts.image_h_strong';
	     
        $result = DB::select()->from($file_name)->where('image_id', '=', 28)->execute();
        // if(count($result)>0)
        // {
        // 		foreach($result as $img)
        // 		{
        // 			$image_type = $img["image_type"] ;
        // 			$image_content= $img['image'] ;
        // 		}
        
        
        // 	}
        	
        
        		//  header("Cache-Control:  max-age=3600, must-revalidate"); // HTTP/1.1
        		header("Cache-Control: public"); 
        	  	header("Expires: Sat, 26 Jul 2017 05:00:00 GMT"); 
        		header ("Content-type: image/jpg");
        
        	  
         print $result[0]['image'];
        
        
        exit;
	}

    private function get_image_all($id=1)
    {
            $sql    = "SELECT * FROM  parts.image_h_strong WHERE image_id< 5";
            $query = DB::query(Database::SELECT, $sql);
            return $result = $query->execute()->as_array();   
            // $row =$result[0];
            // return $imageData = base64_encode($row["image"]);

    }	

    public function action_show($id=60)
    {
        
        // $file_name = 'parts.image_h_strong';
        
        $html="";
        $images = self::get_image_all();
        foreach ($images  as $k => $image) 
        {
            // clearstatcache();
            $html.='<br/><br/><img id="'.$k.'"width="400px" src="data:image/jpeg;base64,'.base64_encode($image["image"]).'">';
            $html.='<img width="200px" src="/metrics/images/download/" >';
            // $html.='<img width="200px" src="https://img.rolemak.com.br/id/h800/31395.jpg?version=9.02">';

        }
    //  clearstatcache();
       print ($html);
//      		 $vars[1] = $this->request->query('id');  
// 		  $size =  substr($vars[1],0,1) ;
//      		$id  =  str_replace('.jpg','', substr($vars[1], 1, strlen($vars[1])-1 ) ) ;		  
// 		  		  switch($size) {
// 			case('1'):
// 			   $size = 'carrinho';
// 			   break;
// 			case('2'):
// 			   $size = 'vitrine';
// 			   break;
// 			case('3'):
// 			   $size = 'detalhes';
// 			   break;
// 			case('4'):
// 			   $size = 'image';
// 			   break;
// 			case('5'):
// 			   $size = 'd_lista';
// 			   break;
// 			default: 
// 			   $size = 'image';
			  
// 		  }


  
// 	$result = DB::select()->from($file_name)->where('image_id', '=', $id)->execute();
// // 	if(count($results)>0)
// // 	{
// // 		foreach($results as $img)
// // 		{
// // 			$image_type = $img["image_type"] ;
// // 			$image_content= $img[$size] ;
// // 		}
// // 	}else{
// // 		$results = DB::select()->from($this->file_name)->where('idfile', '=', '999999')->execute();
// // 		foreach($results as $img)
// // 		{
// //     		$image_type = $img["image_type"] ;
// // 			$image_content= $img[$size] ;
// // 		}

// // 	}
	

// 		//  header("Cache-Control:  max-age=3600, must-revalidate"); // HTTP/1.1
// 		header("Cache-Control: public"); 
// 	  	header("Expires: Sat, 26 Jul 2017 05:00:00 GMT"); 
// 		header ("Content-type: image/jpeg");

	  
// 	  print $result[0]['image'];


// 	exit;
    }
    

}