<?php

class Controller_Invoice extends Controller_Website
{
    // Constants for percentages and fixed costs
    const PIS_PERCENTAGE = 2.10;
    const COFINS_PERCENTAGE = 10.45;
    const ICMS_TAX_1 = 3.51;
    const ICMS_TAX_2 = 8.80;
    const DESEMBARACO_ADUANEIRO = 3036;
    const TAXA_SISCOMEX = 154.23;
    const FIXED_EXPENSE_PERCENTAGE = 5;
    const SEA_FREIGHT_FIXED_COST = 1800;
    const CONTAINER_WEIGHT_CAPACITY = 25000; // kg
    const CONTAINER_VOLUME_CAPACITY = 56; // kg
    const SEGURO = .001; // %


    public function before()
    {
        parent::before();
    }

    // Main pricing action method
   public function action_index()
    {
        $shipment = $this->request->param('division');

        if (!$shipment) {
            throw new HTTP_Exception_400('Invalid product shipment');
        }


         // Get product inventory details
        $invoice = $this->get_invoice($shipment);
        $invoice['products'] = $this->get_next($shipment);
        // Get product inventory details
        // $inventory = $this->get_inventory($isbn);

        //s($invoice);

     //   die();
        if (!$invoice) {
            throw new HTTP_Exception_404('Product not found');
        }

        // Calculate pricing factors based on product data
        $data= $this->calculate_pricing_factors($invoice);
      //  s($data);
       // s($data);

        // Calculate different price ranges based on ICMS taxes
      //  $data['revendaFobValor'] = $this->calculate_revenda_fob($invoice, $data);

        // Use the view to render the table instead of jsonToTable
        $view = View::factory('invoice/index', ['invoice' => $data]);
        $this->response->body($view);
    }


    private function get_invoice($shipment)
    {
         $sql = "
            SELECT


                s.*
            FROM shipments s


            WHERE s.invoice = :invoice


            LIMIT 100";

        return DB::query(Database::SELECT, $sql)
                ->parameters([':invoice' => $shipment])
                ->execute()
                ->as_array()[0];
    }

    
    private function get_next($shipment)
    {
         $sql = "
            SELECT
                n.quant,n.fob, n.quant*n.fob as ammount,
                i.id as Id, i.modelo as modelo, i.nome as xProduct, i.marca as xBrand,
                i.qtestq as qStock, i.revenda as Revenda, i.fob as vFob, i.pesoset as PesoSet,
                i.volumeset as Volume, pr.outras, pr.ncm as NCM, pr.ii, pr.ipi

            FROM shipments s
            LEFT JOIN next n ON (n.shipment=s.id)
            LEFT JOIN inv i ON (i.id=n.isbn)
            LEFT JOIN produtos pr ON (pr.id = i.idcf)

            WHERE s.invoice = :invoice

            ORDER BY NCM ASC, modelo ASC
            LIMIT 1000";

        return DB::query(Database::SELECT, $sql)
                ->parameters([':invoice' => $shipment])
                ->execute()
                ->as_array();
    }

    /**
     * Fetches the product inventory and related information from the database
     */
    private function get_inventory($shipment)
    {
        $sql = "
            SELECT
                i.id as Id, i.modelo as lev2, i.nome as xProduct, i.marca as xBrand,
                i.qtestq as qStock, i.revenda as Revenda, i.fob as vFob, i.peso as Peso,
                i.volume as Volume, pr.outras, pr.ncm as NCM, pr.ii,
                (SELECT freight FROM next
                 RIGHT JOIN shipments ON shipments.id = next.shipment
                 WHERE month(shipments.status) > 0 AND next.isbn = i.id ORDER BY status DESC LIMIT 1) as UltimoFrete,
                (SELECT usd_real FROM next
                 RIGHT JOIN shipments ON shipments.id = next.shipment
                 WHERE month(shipments.status) > 0 AND next.isbn = i.id  ORDER BY status DESC LIMIT 1) as UltimoDolar,
                (SELECT fob FROM next
                 RIGHT JOIN shipments ON shipments.id = next.shipment
                 WHERE month(shipments.status) > 0 AND next.isbn = i.id ORDER BY status DESC  LIMIT 1) as UltimoFob,
                   (SELECT DOLAR FROM Vars) as Dolar,
                 SP.*
            FROM inv i
            LEFT JOIN produtos pr ON (pr.id = i.idcf)
            LEFT JOIN NFE.TributacaoSP SP ON (SP.ncm = pr.ncm AND SP.state='SP')
            WHERE i.id = :shipment
            LIMIT 10";

        return DB::query(Database::SELECT, $sql)
                ->parameters([':shipment' => $shipment])
                ->execute()
                ->as_array()[0];
    }

    /**
     * Calculate pricing-related factors, such as ValorCIFR price, FOB price, and freight percentage.
     */
    private function calculate_pricing_factors($invoice)
    {
        $data = [];
        $totalFobR = 0;
        $totalFreteR = 0;
        $totalSeguroR = 0;
        $totalIPI = 0;
        $totalCOFINS = 0;
        $totalPIS = 0;
        $totalII = 0;
        $ncmTotals = [];

        // Calcular totais por produto
        foreach ($invoice['products'] as $key => $product) {
            $ncm = $product['NCM'];
            
            // Inicializar array para novo NCM
            if (!isset($ncmTotals[$ncm])) {
                $ncmTotals[$ncm] = [
                    'fobR' => 0,
                    'freteR' => 0,
                    'seguroR' => 0,
                    'ipiR' => 0,
                    'cofinsR' => 0,
                    'pisR' => 0,
                    'iiR' => 0,
                    'icmsR' => 0,
                    'quantidade' => 0,
                    'peso' => 0,
                    'volume' => 0
                ];
            }
            
            //$sets = $this->calculate_sets($product['NCM'], $product['PesoSet'], $product['Volume']);
            $sets = $product['quant'];
            
            // Cálculos por produto
            $fobR = round($sets * $product['fob'] * $invoice['usd_real'], 2);
            $freteR = $invoice['freight'] * $invoice['usd_real'];
            $seguroR = round($fobR * self::SEGURO, 2);
            $cifR = $fobR + $freteR + $seguroR;
            
            // Impostos por produto
            $ipiR = round(((int)($product['ipi'] ?? 0)/100) * $cifR, 2);
            $cofinsR = round((self::COFINS_PERCENTAGE/100) * $cifR, 2);
            $pisR = round((self::PIS_PERCENTAGE/100) * $cifR, 2);
            $iiR = round(((int)$product['ii']/100) * $cifR, 2);
            $icmsR = round((1.4/100) * $cifR, 2);


            $invoice['products'][$key]['fobR'] = $fobR;
            $invoice['products'][$key]['freteR'] = $freteR;
            $invoice['products'][$key]['seguroR'] = $seguroR;
            $invoice['products'][$key]['ipiR'] = $ipiR;
            $invoice['products'][$key]['cofinsR'] = $cofinsR;
            $invoice['products'][$key]['pisR'] = $pisR;
            $invoice['products'][$key]['icmsR'] = $icmsR;
            $invoice['products'][$key]['iiR'] = $iiR;
            $invoice['products'][$key]['quantidade'] = $product['quant'];
            $invoice['products'][$key]['peso'] = ($product['PesoSet'] * $product['quant']);
            $invoice['products'][$key]['volume'] = ($product['Volume'] * $product['quant']);
            
            // Acumular totais por NCM
            $ncmTotals[$ncm]['fobR'] += $fobR;
            $ncmTotals[$ncm]['freteR'] += $freteR;
            $ncmTotals[$ncm]['seguroR'] += $seguroR;
            $ncmTotals[$ncm]['ipiR'] += $ipiR;
            $ncmTotals[$ncm]['cofinsR'] += $cofinsR;
            $ncmTotals[$ncm]['pisR'] += $pisR;
            $ncmTotals[$ncm]['iiR'] += $iiR;
            $ncmTotals[$ncm]['quantidade'] += $product['quant'];
            $ncmTotals[$ncm]['peso'] += ($product['PesoSet'] * $product['quant']);
            $ncmTotals[$ncm]['volume'] += ($product['Volume'] * $product['quant']);
            
            // Acumular totais gerais
            $totalFobR += $fobR;
            $totalFreteR += $freteR;
            $totalSeguroR += $seguroR;
            $totalIPI += $ipiR;
            $totalCOFINS += $cofinsR;
            $totalPIS += $pisR;
            $totalII += $iiR;
           
            
         //   $invoice['products'][$key]['calc'] = $prod;

        }
            //s($invoice);
        // Adicionar totais por NCM aos dados
        $data['TotaisPorNCM'] = $ncmTotals;

        // Dados gerais da invoice
        $data['ValorDolar'] = $invoice['usd_real'];
        $data['ValorFobR'] = $totalFobR;
        $data['ValorFreteR'] = $totalFreteR;
        $data['ValorSeguroR'] = $totalSeguroR;
        $data['ValorCIFR'] = $totalFobR + $totalFreteR + $totalSeguroR;

        // Impostos e taxas
        $data['ValorIPI'] = $totalIPI;
        $data['TaxaSiscomex'] = self::TAXA_SISCOMEX;
        $data['ValorCOFINS'] = $totalCOFINS;
        $data['ValorPIS'] = $totalPIS;
        $data['ValorII'] = $totalII;
        
        $data['ValorTotalTributosDespesasI'] = $totalIPI + self::TAXA_SISCOMEX + $totalCOFINS + $totalPIS + $totalII;

        // Outras despesas
        $data['ValorServicoDesembaracoAduaneiro'] = self::DESEMBARACO_ADUANEIRO;
        $data['ValorTaxaExpediente'] = 788.23;
        $data['Desconsolidacao'] = 1800;
        $data['ValorMarinhaMecante'] = $invoice['freight'] * 0.25 * $invoice['usd_real'];
        $data['ValorArmazenagem'] = 6000;
        $data['ValorIcmsTTD'] = round(($data['ValorCIFR'] + $data['ValorTotalTributosDespesasI']) * 0.014, 2);

        $data['ValorTotalTributosDespesasII'] = $data['ValorServicoDesembaracoAduaneiro'] +
                                               $data['ValorTaxaExpediente'] +
                                               $data['Desconsolidacao'] +
                                               $data['ValorMarinhaMecante'] +
                                               $data['ValorArmazenagem'] +
                                               $data['ValorIcmsTTD'];

        // Despesas extras
        $data['TransporteInternoPortoCD'] = 6000;
        $data['OutrosCustosDiretos'] = 2000;

        // Totais finais
        $data['TotalFreteSeguroTributosDespesas'] = round($data['ValorFreteR'] +
                                                         $data['ValorSeguroR'] +
                                                         $data['ValorTotalTributosDespesasI'] +
                                                         $data['ValorTotalTributosDespesasII'] +
                                                         $data['TransporteInternoPortoCD'] +
                                                         $data['OutrosCustosDiretos'], 2);

        $data['ValorTotalFobFreteSeguroTributosDespesas'] = round($data['TotalFreteSeguroTributosDespesas'] + $data['ValorFobR'], 2);
        $data['IndexTotalFobDespesas'] = round($data['ValorTotalFobFreteSeguroTributosDespesas'] / $data['ValorFobR'], 3);

        $invoice['calc'] = $data;
        return $invoice;
    }

    /**
     * Calculate the number of sets based on NCM and product weight
     */
    private function calculate_sets($ncm, $weight, $volume)
    {

        // Adjust container capacity based on NCM type
        $setsBasedOnVolume =0;
        $setsBasedOnWeight =0;

        if( $volume>0 )   $setsBasedOnVolume= self::CONTAINER_VOLUME_CAPACITY / ($volume);
        if( $weight>0 )   $setsBasedOnWeight= self::CONTAINER_WEIGHT_CAPACITY / ($weight);

        $numberOfSets = 1;
        if($setsBasedOnWeight> 0 ) $numberOfSets = $setsBasedOnWeight;
        if($setsBasedOnVolume> 0 ) $numberOfSets = $setsBasedOnVolume;

        if($setsBasedOnWeight> 0 and  $setsBasedOnVolume>0 ) $numberOfSets = min($setsBasedOnVolume, $setsBasedOnWeight);

        // Ensure the result is at least 1 (you can't have 0 sets)
        return max(round($numberOfSets,0), 1);


    }

    /**
     * Calculates the "revenda FOB" value based on ICMS rates and different markups.
     */
    private function calculate_revenda_fob($inventory, $data)
    {
        $revendaFobValues = [];

        $icmsRates = [self::ICMS_TAX_1, self::ICMS_TAX_2];

        foreach ($icmsRates as $icmsPerc) {
            $taxPerc = $data['AliquotaPIS'] + $data['AliquotaCOFINS'] + $icmsPerc;

            // Calculate FOB values for different markup percentages
            for ($markup = 17; $markup <= 30; $markup++) {
                $idx = round($data['setClearIdx'] / (1 - (($taxPerc + $markup) / 100)), 2);
                $revendaFobValues[$icmsPerc][$markup] = round($idx * $inventory['vFob'], 2);
            }
        }

        return $revendaFobValues;
    }

    /**
     * Converts a JSON structure to an HTML table for output.
     */
    private function jsonToTable($data)
    {
        $table = '<table class="table table-bordered">';
        foreach ($data as $key => $value) {
            $table .= '<tr>';
            $table .= '<th>' . htmlspecialchars($key) . '</th><td>';

            if (is_array($value)) {
                $table .= $this->jsonToTable($value); // Recursive call for nested arrays
            } else {
                $table .= htmlspecialchars($value);
            }

            $table .= '</td></tr>';
        }
        $table .= '</table>';
        return $table;
    }


}
