<?php

class Controller_Jeditables extends Controller_Website
{

   
   public function before()
    {
         parent::before();
    }


    public function action_index()
    {     
     
        $sql = sprintf("SELECT PrimaryRecordId AS _ISBN ,i.Modelo,j.id as _id,	TableName,	RecordName,	PrimaryRecordName,	PrimaryRecordId,	PreviousContent,	NewContent,	UserId,	Datetime
        FROM history.jeditable j , mak.inv i WHERE i.id=j.PrimaryRecordId AND  TableName='inv' AND RecordName='revenda' and PrimaryRecordId='100372' ORDER BY Datetime DESC LIMIT 10");
        
        
        $query = DB::query(Database::SELECT, $sql);
        
        $data = $query->execute()->as_array();	
        
        // return  $result;
        
        $view = parent::tablefy( $data );
        $response = parent::pagefy($view);
        
        $this->response->body($response);

     
        
    }


  
   
}