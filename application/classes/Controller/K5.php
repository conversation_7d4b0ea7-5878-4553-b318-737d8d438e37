<?php defined('SYSPATH') or die('No direct script access.');


class Controller_K5 extends Controller_Website {

  public function action_index() {
        // Carregar a view principal
        $view = View::factory('k5/index');
        $this->response->body($view);
    }

    public function action_load_dropdown() {
        // Simulação de carregamento dinâmico de conteúdo no dropdown
        $dropdownContent = '<ul class="bg-white shadow-md rounded-md py-2">
                                <li class="px-4 py-2 hover:bg-gray-200"><a href="#">Opção 1</a></li>
                                <li class="px-4 py-2 hover:bg-gray-200"><a href="#">Opção 2</a></li>
                                <li class="px-4 py-2 hover:bg-gray-200"><a href="#">Opção 3</a></li>
                            </ul>';
        $this->response->body($dropdownContent);
    }
}