<?php
class Controller_Leads extends Controller_Websession
{

    public function action_today()
    {
        $this->auto_render = false;

        $where = null;

        $response = array();

        if ( $this->request->param('division') )
        {
            $where.=sprintf(" AND u.segmento = '%s' ", $this->request->param('division') );
        }

        $sql= sprintf("SELECT count(DISTINCT h.id) as count, e.nome, u.segmento, u.nick
                       FROM crm.historico as h
                       LEFT JOIN crm.eventos as e ON ( e.id = h.evento_id )
                       LEFT JOIN mak.users u ON (u.id=h.user_id)
                       WHERE DATE(h.`data`) = DATE(CURDATE())
                       %s
                       GROUP BY user_id
                       ORDER BY count DESC
                       LIMIT 100", $where);

        $query = DB::query(Database::SELECT, $sql);
        $response = $query->execute()->as_array();

        //s($sql, $response);

        if ( isset( $response[0] ) )
        {
            return $this->response->body($response[0]['count'] );
        }

        return $response;
    }

}