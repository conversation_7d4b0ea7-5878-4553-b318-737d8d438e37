<?php
class Controller_Lmp extends Controller_Websession
{

    public function action_june()
    {
        $this->auto_render = false;

        $where = null;
        $total = 0;

        $response = array();

        if ( $this->request->param('division') )
        {
            // $where.=sprintf(" AND u.segmento = '%s' ", $this->request->param('division') );
        }

        $sql= sprintf("SELECT 

                            a.active_okr as objetivoAtivo, 
                            a.year_okr as objetivoAno, 
                            a.unity_okr as objetivoUnidadeID, 
                            b.Fantasia as objetivoUnidadeNome, 
                            a.department_okr as objetivoDepartamentoNome, 
                            a.segment_okr as objetivoSegmentoID, 
                            a.objective_okr as objetivoObjetivo, 
                            a.subtitle_okr as objetivoLegenda,
                            d.active_okr_kr as resultadoChaveAtivo, 
                            d.number_okr_kr as resultadoChaveNumero, 
                            d.name_okr_kr as resultadoChaveNome, 
                            d.subtitle_okr_kr as resultadoChaveLegenda, 
                            e.type_okr_targets as metaTipo,
                            e.june_okr_targets as metaJunho 
                        FROM LMP.okr_general as a
                        LEFT JOIN mak.Emitentes as b on (a.unity_okr=b.EmitentePOID)
                        LEFT JOIN LMP.okr_keyresults as d on (a.id_okr=d.fk_okr_general_id)
                        LEFT JOIN LMP.okr_targets as e on (d.id_okr_kr=e.fk_okr_kr_id)
                        WHERE 1=1
                        AND d.id_okr_kr = 6
                        %s
                        LIMIT 1 OFFSET 0", $where);

        $query = DB::query(Database::SELECT, $sql);
        $response = $query->execute('webteam')->as_array();

        //s($sql, $response);

        if ( isset( $response[0] ) )
        {
            $total=  $this->response->body($response[0]['metaJunho'] );
        }

        return $total;
    }

    public function action_june2()
    {
        $this->auto_render = false;

        $where = null;
        $total = 0;

        $response = array();

        if ( $this->request->param('division') )
        {
            // $where.=sprintf(" AND u.segmento = '%s' ", $this->request->param('division') );
        }

        $sql= sprintf("SELECT 

                            a.active_okr as objetivoAtivo, 
                            a.year_okr as objetivoAno, 
                            a.unity_okr as objetivoUnidadeID, 
                            b.Fantasia as objetivoUnidadeNome, 
                            a.department_okr as objetivoDepartamentoNome, 
                            a.segment_okr as objetivoSegmentoID, 
                            a.objective_okr as objetivoObjetivo, 
                            a.subtitle_okr as objetivoLegenda,
                            d.active_okr_kr as resultadoChaveAtivo, 
                            d.number_okr_kr as resultadoChaveNumero, 
                            d.name_okr_kr as resultadoChaveNome, 
                            d.subtitle_okr_kr as resultadoChaveLegenda, 
                            e.type_okr_targets as metaTipo,
                            e.june_okr_targets as metaJunho 
                        FROM LMP.okr_general as a
                        LEFT JOIN mak.Emitentes as b on (a.unity_okr=b.EmitentePOID)
                        LEFT JOIN LMP.okr_keyresults as d on (a.id_okr=d.fk_okr_general_id)
                        LEFT JOIN LMP.okr_targets as e on (d.id_okr_kr=e.fk_okr_kr_id)
                        WHERE 1=1
                        AND d.id_okr_kr = 6
                        %s
                        LIMIT 1 OFFSET 0", $where);

        $query = DB::query(Database::SELECT, $sql);
        $response = $query->execute('webteam')->as_array();

        //s($sql, $response);

        if ( isset( $response[0] ) )
        {
            $total=  $this->response->body($response[0]['metaJunho'] );
        }

        return $total;
    }


}