<?php

class Controller_Mercadolivre extends Controller_Website
{

   
   public function before()
    {
         parent::before();
    }


    public function action_index()
    {     

        $id=100500;
        
        if($this->request->param('division')) $id= $this->request->param('division');
        
        $ml =  self::get_ml($id);
        
        $resp = json_decode($ml,true);
        
        
        if (isset( $resp['price'])) 
        {
          echo $resp['price'];
        }else{
           echo 'Não encontrado!'; 
        }
        
        // $sql = sprintf("SELECT PrimaryRecordId AS _ISBN ,i.Modelo,j.id as _id,	TableName,	RecordName,	PrimaryRecordName,	PrimaryRecordId,	PreviousContent,	NewContent,	UserId,	Datetime
        // FROM history.jeditable j , mak.inv i WHERE i.id=j.PrimaryRecordId AND  TableName='inv' AND RecordName='revenda' and PrimaryRecordId='100372' ORDER BY Datetime DESC LIMIT 10");
        
        
        // $query = DB::query(Database::SELECT, $sql);
        
        // $data = $query->execute()->as_array();	
        
        // // return  $result;
        
        // $view = parent::tablefy( $data );
        // $response = parent::pagefy($view);
        
        // $this->response->body($response);

     
        
    }


    private function get_ml($_ISBN)
    {
        $url = ($_SERVER["HTTP_X_FORWARDED_PROTO"].'://'.$_SERVER["HTTP_X_FORWARDED_HOST"].'/nodered/mercadolivre/edit?id='.$_ISBN );
        
        $response = Request::factory( $url )
                ->headers('Authorization', 'Basic cm9ib21hazpHb3R0eTI1MTI5NCo=') 
                ->method('GET')
                ->execute()->body();
                
        return $response;
    }
  

      public function action_search()
    {     

        $q='Junta gol';
        
        if($this->request->query('q')) $q= $this->request->query('q');
        
        $ml =  self::search_ml($q);
        // die();
        $resp = json_decode($ml,true);
        // s($resp);
    
        foreach( $resp['results'] as $key => $val) 
        {
            $data[$key]['Id'] = $val['id'];
            $data[$key]['Title'] = $val['title'];
            $data[$key]['DomainId'] = $val['domain_id'];
            $data[$key]['Preço'] = number_format($val['price'],2,'.',',');
            $data[$key]['QuantidadeDisponivel'] = $val['available_quantity'];
            $data[$key]['QuantidadeVendida'] = $val['sold_quantity'];
            $data[$key]['Seller'] = $val['seller']['permalink'];
            $data[$key]['Anúncio'] = $val['permalink'];
            $frete='Não';
            if($val['shipping']['free_shipping']==true) $frete='Sim';
            $data[$key]['FreteGratis'] = $frete;
            $data[$key]['FreteTipo'] = $val['shipping']['logistic_type'];
            $data[$key]['Img'] = $val['thumbnail'];
            // $data[$key]['seller_reputation'] = '';
            // if(isset($val['seller_reputation']['power_seller_status'])) 
           
            $data[$key]['DataDeRegistro'] = substr($val['seller']['registration_date'],0,10);
            $data[$key]['SellerCidade'] = $val['seller_address']['city']['name'];
            $data[$key]['listing_type_id'] = $val['listing_type_id'];
            $data[$key]['SellerReputation'] = $val['seller']['seller_reputation']['power_seller_status'];
            $data[$key]['SellerSalesCompleted60Days'] = $val['seller']['seller_reputation']['metrics']['sales']['completed'];
            
            $data[$key]['Vezes'] = $val['installments']['quantity'];
            $data[$key]['Prestação'] = $val['installments']['amount'];
            $data[$key]['Taxa'] = $val['installments']['rate'];
            
            // s($val);
            
        }
        
        // s($resp);
        
        
        
        if(empty($data)) return false;
        
        $data['theme'] = 3;
        $view= parent::tablefy( $data);
        
        // s($data);
       
        
        $theme='light';
            
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);
        
        // if (isset( $resp['price'])) 
        // {
        //   echo $resp['price'];
        // }else{
        //   echo 'Não encontrado!'; 
        // }
        

     
        
    }

  
     private function search_ml($q)
    {
        $url = ($_SERVER["HTTP_X_FORWARDED_PROTO"].'://'.$_SERVER["HTTP_X_FORWARDED_HOST"].'/nodered/mercadolivre/search?&limit=50&offset=00&q='.$q );
        
        $response = Request::factory( $url )
                ->headers('Authorization', 'Basic cm9ib21hazpHb3R0eTI1MTI5NCo=') 
                ->method('GET')
                ->execute()->body();
                
        return $response;
    }
   
}