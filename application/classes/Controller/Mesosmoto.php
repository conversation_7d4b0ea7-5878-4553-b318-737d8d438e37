<?php

class Controller_MesosMoto extends Controller_Website
{

   
   public function before()
    {
         parent::before();
         $this->sales_nop_codes="27,28,51,76";
         
    }

   public function action_index()
    { 
        // self::mesos();
          $view = View::factory('mesos/home')
            ->set('menu', '')
           ;

        $this->response->body($view);
    }
 
 
    function action_meta()
    {
        $sql= sprintf("
            SELECT * 
            FROM  estatisticas.`TAB_MESOS` 
            WHERE Meta > 9 
            ORDER BY Meta desc
             ");
        
        
        $query = DB::query(Database::SELECT, $sql);
        $result= $query->execute()->as_array();
        // echo APPPATH;
        //   s($result);
        
        $start= date('Y-m-01');
        $today= date('Y-m-d');
       
        $result[0]['DaysTotal'] = self::GetUsefulDays();
        $result[0]['DaysTillNow'] =  self::number_of_working_days($start, $today);
        $result[0]['DaysMissing'] = $result[0]['DaysTotal']-$result[0]['DaysTillNow'];
        $result[0]['DaysPassedPerc'] =  round($result[0]['DaysTillNow']/$result[0]['DaysTotal']*100,0);
       
        $result[0]['Metas']=$result[0]['qAtingida']=$result[0]['tqDia'] = 0;
        
        $sales = self::day();
        $result[0]['tqDia']=   $sales['qDia'];
        $result[0]['qAtingida']+=   $sales['qMes'];
        
        foreach($result as $key => $value)
        {
           
            
            $meso = self::summary($value['Meso']);
            //S($meso);
            // die();
            if(empty($meso)) continue;
           
            $result[$key]['qClientes'] = $meso['qClientes'];
            $result[$key]['qCidades'] = $meso['qCidades'];
           
            $result[$key]['qMediaAnoPassado'] =  round($meso['qAnoPassado'],0);
            $result[$key]['Jan'] = $meso['Jan'];
            $result[$key]['Fev'] = $meso['Fev'];
            $result[$key]['qMes'] = $meso['qMes'];
            $result[$key]['qSemana'] = $meso['qSemana'];
            $result[$key]['qDia'] = $meso['qDia'];
            
            if($meso['qDia']==0) $result[$key]['qDia'] = '-';
            
            $result[$key]['Saldo'] =  $meso['Meta']-$meso['qMes'];
            $result[$key]['Perc'] =  round($meso['qMes']/$meso['Meta'],2)*100;
                    
            
            if($result[$key]['Perc']<$result[0]['DaysPassedPerc']) 
            {
                if($result[$key]['Perc']/$result[0]['DaysPassedPerc']<.75) 
                {
                    $result[$key]['class'] = '   text-red-500 font-bold	 ';
                    $result[$key]['tclass'] = ' text-red-700	';
                }else{
                    $result[$key]['class'] = '   text-blue-500 font-bold	 ';
                    $result[$key]['tclass'] = ' text-red-700	';
                }
            }else{
                $result[$key]['class'] = '   text-green-500 font-bold	';
                $result[$key]['tclass'] = ' text-green-700  	';
            }
            
            $result[0]['Metas']+=	$meso['Meta'];
           	$result[$key]['MesoMetaDia']= round($meso['Meta']/$result[0]['DaysTotal'],0);  
            $result[$key]['MetaSemana'] = round($meso['Meta']/$result[0]['DaysTotal']*5,0);
        	$result[0]['qMiss']=  	$result[0]['Metas']-$result[0]['qAtingida'];
        	$result[0]['qPerc'] =  round($result[0]['qAtingida']/$result[0]['Metas']*100,0);
        	$result[0]['Previsao'] =  round($result[0]['qAtingida']/$result[0]['DaysTillNow']*$result[0]['DaysTotal'],0);
        	
            $result[$key]['sUF'] = $meso['UF'];
            
            // $result[$key]['Cidades'] = self::city_links($meso['Cidades']);
        }
        
        $result[0]['qMedia'] =  round($result[0]['qAtingida']/$result[0]['DaysTillNow'],0) ;
        $result[0]['MetaDia'] = round($result[0]['Metas']/$result[0]['DaysTotal'],0);
        $result[0]['MetaTillNow'] = round($result[0]['Metas']/$result[0]['DaysTotal'],0)*$result[0]['DaysTillNow'];
        $result[0]['MediaDiariaAtingir'] = round($result[0]['qMiss']/$result[0]['DaysMissing'],0);
        
        
        $view = parent::mustache($result,'mesos/meta');
        
        $this->response->body($view);
        
       
    }
    
    function action_mesos()
    {
        $sql= sprintf("
            SELECT * 
            FROM  estatisticas.`TAB_MESOS` 
            WHERE Meta > 9 
            ORDER BY Meta desc
             ");
        
        
        $query = DB::query(Database::SELECT, $sql);
        $result= $query->execute()->as_array();
        // echo APPPATH;
        //   s($result);
        
        $start= date('Y-m-01');
        $today= date('Y-m-d');
       
        $result[0]['DaysTotal'] = self::GetUsefulDays();
        $result[0]['DaysTillNow'] =  self::number_of_working_days($start, $today);
        $result[0]['DaysMissing'] = $result[0]['DaysTotal']-$result[0]['DaysTillNow'];
        $result[0]['DaysPassedPerc'] =  round($result[0]['DaysTillNow']/$result[0]['DaysTotal']*100,0);
       
        $result[0]['Metas']=$result[0]['qAtingida']=$result[0]['tqDia'] = 0;
        
        $sales = self::day();
        $result[0]['tqDia']=   $sales['qDia'];
        $result[0]['qAtingida']+=   $sales['qMes'];
        
        foreach($result as $key => $value)
        {
           
            
            $meso = self::summary($value['Meso']);
            //S($meso);
            // die();
            if(empty($meso)) continue;
           
            $result[$key]['qClientes'] = $meso['qClientes'];
            $result[$key]['qCidades'] = $meso['qCidades'];
           
           $result[$key]['qMediaAnoPassado'] =  round($meso['qAnoPassado'],0);
            $result[$key]['Jan'] = $meso['Jan'];
            $result[$key]['Fev'] = $meso['Fev'];
            $result[$key]['qMes'] = $meso['qMes'];
             $result[$key]['qSemana'] = $meso['qSemana'];
            $result[$key]['qDia'] = $meso['qDia'];
            if($meso['qDia']==0) $result[$key]['qDia'] = '-';
            
            $result[$key]['Saldo'] =  $meso['Meta']-$meso['qMes'];
            $result[$key]['Perc'] =  round($meso['qMes']/$meso['Meta'],2)*100;
                    
            
            if($result[$key]['Perc']<$result[0]['DaysPassedPerc']) 
            {
                if($result[$key]['Perc']/$result[0]['DaysPassedPerc']<.75) 
                {
                    $result[$key]['class'] = '   text-red-500 font-bold	 ';
                    $result[$key]['tclass'] = ' text-red-700	';
                }else{
                    $result[$key]['class'] = '   text-blue-500 font-bold	 ';
                    $result[$key]['tclass'] = ' text-red-700	';
                }
            }else{
                $result[$key]['class'] = '   text-green-500 font-bold	';
                $result[$key]['tclass'] = ' text-green-700  	';
            }
            
            $result[0]['Metas']+=	$meso['Meta'];
           	$result[$key]['MesoMetaDia']= round($meso['Meta']/$result[0]['DaysTotal'],0);  
            $result[$key]['MetaSemana'] = round($meso['Meta']/$result[0]['DaysTotal']*5,0);
        	$result[0]['qMiss']=  	$result[0]['Metas']-$result[0]['qAtingida'];
        	$result[0]['qPerc'] =  round($result[0]['qAtingida']/$result[0]['Metas']*100,0);
        	$result[0]['Previsao'] =  round($result[0]['qAtingida']/$result[0]['DaysTillNow']*$result[0]['DaysTotal'],0);
        	
            $result[$key]['sUF'] = $meso['UF'];
            
            $result[$key]['Cidades'] = self::city_links($meso['Cidades']);
        }
        
        $result[0]['qMedia'] =  round($result[0]['qAtingida']/$result[0]['DaysTillNow'],0) ;
        $result[0]['MetaDia'] = round($result[0]['Metas']/$result[0]['DaysTotal'],0);
        
        $result[0]['MetaTillNow'] = round($result[0]['Metas']/$result[0]['DaysTotal'],0)*$result[0]['DaysTillNow'];
        
        $result[0]['MediaDiariaAtingir'] = round($result[0]['qMiss']/$result[0]['DaysMissing'],0);
        
        $view = parent::mustache($result,'mesos/mesos');
        
        $this->response->body($view);
        
       
    }
    
    public function summary($city='3501',$type=0)
  	{
	   // echo $meso = $this->request->param('xtras');
        //( SELECT  TO_DAYS(NOW()) - TO_DAYS(max(`clientes_paginas_data`))  FROM logs.`r3_clientes_paginas` WHERE `clientes_paginas_id`=c.id ) as LastAccess,	
	    $sql= sprintf("SELECT   mesos.Meta, h.UF, h.
								    segmento as Cat,
								    
								    GROUP_CONCAT(DISTINCT(NomeMunic) ORDER BY NomeMunic SEPARATOR ', ')  as Cidades,
								    
								    SUM(if( YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()) AND DAY(h.data)=DAY(NOW()), h.quant,0)) as qDia,
									SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=1,h.quant,0)) as Jan,
									SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=2,h.quant,0)) as Fev,
									SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()),h.quant,0)) as qMes,
										SUM(if(YEAR(h.data)=YEAR(NOW()) AND WEEK(h.data)=WEEK(NOW()),h.quant,0)) as qSemana,
									SUM(if(YEAR(h.data)=YEAR(NOW()),h.quant,0)) as qAno,
									SUM(if(YEAR(h.data)=YEAR(NOW())-1,h.quant,0))/11.3 as qAnoPassado,
									count(DISTINCT c.id) as qClientes,
									count(DISTINCT c.cidade) as qCidades
									

							FROM   mak.vPedidos h
							LEFT JOIN  clientes c ON (h.idcli=c.id ) 
							LEFT JOIN estatisticas.`TAB_MESOS` mesos ON (mesos.`Meso`=h.Meso  ) 
						
							WHERE  segmento='machines' AND   h.pedido> 1300000 AND h.Meso= '%s'
							GROUP BY h.Meso
						
				    	
				    		LIMIT 5000
					", addslashes($city),$this->sales_nop_codes );

		$query = DB::query(Database::SELECT, $sql);
        $result= $query->execute()->as_array();
        
        // s($result);
          
      
        
        // foreach($result as $key => $val)
        // {  
        //     $cidades = explode(', ',$val['Cidades']);
            
        //     foreach($cidades as $k => $cidade)
        //     {
        //         // $res[$cidade]= (int) self::cidade($cidade);
        //     }
        
        //     arsort($res);
            
        //     foreach($res as $cidade => $qtd)
        //     {
        //         $new[]= $cidade.'('.$qtd.')';
        //     }
            
        //     $result[$key]['Cidades'] = implode(', ',$new);
        // }    
          
        if(!empty($result)) return ($result[0]);
        // echo ($city).'<br>';
			  
	}	
  
    private function cidade($city)
    {
         
	 	 $sql= sprintf("SELECT  
								SUM(if(YEAR(h.data)=YEAR(NOW()),h.quant,0)) as qAno

							FROM   mak.vPedidos h 
							WHERE  segmento='machines' AND h.NomeMunic= '%s'  AND h.pedido>1300000  
							GROUP BY NomeMunic
							ORDER BY qAno desc
						
				    	
				    		LIMIT 1000
					", addslashes($city),$this->sales_nop_codes );

		 $result = $this->action_connect_SELECT($sql);
		 
// 		 s($result);
		 
		 return $result[0]['qAno'];
		 

 	}	

    private function city_links($str)
    {
        $cities = explode(', ',$str);
        $resp='';
        foreach($cities as $city)
        {
             $link[] = sprintf('<a color="#008000" width="%s" height="%s" class="winbox-iframe cursor-pointer text-sm" rel="/metrics/mesos/cidade/1/%s/" title="Clientes em %s ">%s</a>' , '50%','50%',$city, $city , $city );
        }
        
        $resp.= implode(', ' ,$link);  
        return $resp;
        // s($resp);
        
    }
  
     private function day()
  	{
	   // echo $meso = $this->request->param('xtras');
        //( SELECT  TO_DAYS(NOW()) - TO_DAYS(max(`clientes_paginas_data`))  FROM logs.`r3_clientes_paginas` WHERE `clientes_paginas_id`=c.id ) as LastAccess,	
	    $sql= sprintf("SELECT	    SUM(if( YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()) AND DAY(h.data)=DAY(NOW()), hist.quant,0)) as qDia,
									SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()),hist.quant,0)) as qMes,
									SUM(if(YEAR(h.data)=YEAR(NOW()),hist.quant,0)) as qAno,
									SUM(if(YEAR(h.data)=YEAR(NOW())-1,hist.quant,0)) as qAnoPassado

							FROM mak.hoje h 
							LEFT JOIN mak.hist ON (hist.pedido=h.id) 
							LEFT JOIN mak.inv ON (inv.id=hist.isbn) 
							LEFT JOIN mak.produtos p ON (p.id=inv.idcf)
						
							WHERE  p.segmento='machines' AND h.id> 1330000  AND   h.nop in (%s) and hist.valor>500
							
						
				    	
				    		LIMIT 1000
					",$this->sales_nop_codes );

		 $query = DB::query(Database::SELECT, $sql);
        $result= $query->execute()->as_array();
        // s($result);
        // s($sql);
        if(!empty($result)) return ($result[0]);
        // echo ($city).'<br>';
			  
	}	
	
 

   public function action_customer()
  	{
	   // echo $meso = $this->request->param('xtras');
        
		if( DBS == 'mak'){
			$dbsister='mak_playa';
		}else{
			$dbsister='mak';
		}
//( SELECT  TO_DAYS(NOW()) - TO_DAYS(max(`clientes_paginas_data`))  FROM logs.`r3_clientes_paginas` WHERE `clientes_paginas_id`=c.id ) as LastAccess,	
	 $sql= sprintf("SELECT  clientes.*,
			categoria.categoria AS Cat, 
			clientes.limite as Limite,
			categoria.Subcategoria,	 
			(SELECT  user   FROM users u WHERE u.id=clientes.vendedor) AS vendedor, 						
			(SELECT  SUM(h.valor) FROM hoje h WHERE h.idcli=clientes.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW())   GROUP BY h.idcli) AS Mes, 				
			(SELECT  SUM(h.valor) FROM hoje h WHERE h.idcli=clientes.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW()) GROUP BY h.idcli) AS Ano, 
			(SELECT  SUM(h.valor) FROM hoje h WHERE h.idcli=clientes.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW())-1 GROUP BY h.idcli) AS AnoP, 						
			(SELECT  SUM(c.valor) FROM cheques c WHERE c.idcli=clientes.id AND c.data < CURDATE()-3 AND ( ISNULL(c.datadep) OR MONTH(c.datadep)=0  ) GROUP BY c.idcli) AS Atrasados, 		
			(SELECT  SUM(c.valor) FROM cheques c WHERE c.idcli=clientes.id AND ( YEAR(datadep)=0 OR datadep> current_date ) GROUP BY c.idcli) AS Pendentes, 
			(SELECT  SUM(c.valor) FROM cheques c WHERE c.idcli=clientes.id AND MONTH(datadep)>0 GROUP BY c.idcli) AS Pagos, 
			(SELECT  SUM(if(h.usvale>0,h.usvale,0)) FROM hoje h WHERE h.idcli=clientes.id   and h.prazo<>15  AND h.nop IN (27) GROUP BY h.idcli) AS Vales, 				
			(SELECT  SUM(h.valor) FROM hoje h WHERE h.idcli=clientes.id   and h.prazo<>15  GROUP BY h.idcli) AS Total, 	


            (SELECT  SUM(valor)/COUNT(DISTInCT MONTH(datae)) FROM hoje h WHERE h.idcli=clientes.id   and (  h.nop = 27  OR h.nop = 28 )  GROUP BY h.idcli, YEAR(h.datae) ORDER BY YEAR(h.datae) DESC LIMIT 1 ) AS MediaMensal, 
            (SELECT  avg(valor) FROM hoje h WHERE h.idcli=clientes.id   and (  h.nop = 27  OR h.nop = 28 )  GROUP BY h.idcli  DESC LIMIT 1 ) AS FaturaMedia, 			

			(SELECT  max(h.valor) FROM hoje h WHERE h.idcli=clientes.id   and h.prazo<>15  GROUP BY h.idcli) AS Maior, 
			(SELECT estados.sintegra FROM estados WHERE estados.uf=clientes.estado ) AS Sintegra,
			TO_DAYS(NOW()) -(SELECT  TO_DAYS(max(h.data)) FROM hoje h WHERE h.idcli=clientes.id GROUP BY h.idcli) AS Compra,

			(SELECT  CONCAT(  MAX(TO_DAYS(c.datadep)-TO_DAYS(c.data)) , ' dias em ',DATE_FORMAT(c.data, '%%d-%%m-%%Y'), ' no valor de R$ ', c.valor ) as X FROM `cheques` c WHERE c.idcli=clientes.id group by clientes.id  limit 1) as MaiorAtraso,

	(SELECT  AVG(TO_DAYS(c.datadep)-TO_DAYS(c.data)) as X FROM `cheques` c WHERE c.idcli=clientes.id group by clientes.id limit 1) as AtrasoMedio 





			FROM clientes
			LEFT JOIN categoria  ON (categoria.id=clientes.categoria)
			WHERE   clientes.id=%s", $this->request->param('xtras'));

		    $query = DB::query(Database::SELECT, $sql);
        $result= $query->execute()->as_array();
        
         $view = parent::mustache($result,'mesos/customer');
        // s($result);
        $this->response->body($view);
        
        // 
        // if(!empty($result)) return ($result[0]);
        // return [];
			  
	}	

	public function action_meso($city='3501',$type=0)
  	{
	
       $meso = $this->request->param('xtras');
	  
		if( DBS == 'mak'){
			$dbsister='mak_playa';
		}else{
			$dbsister='mak';
		}
//( SELECT  TO_DAYS(NOW()) - TO_DAYS(max(`clientes_paginas_data`))  FROM logs.`r3_clientes_paginas` WHERE `clientes_paginas_id`=c.id ) as LastAccess,	
		 $sql= sprintf("SELECT   c.*,LCASE(c.nome) as nome,LCASE(c.ender) as ender,  mun.*, c.id as idcli, u.nick, mesos.Meta,
								    p.segmento as Cat,
								
									TO_DAYS(NOW())-TO_DAYS(max(h.data)) AS  LastPurchase,
									
									SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=1,hist.quant,0)) as Jan,
									SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=2,hist.quant,0)) as Fev,
									SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=3,hist.quant,0)) as Mar,
									SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()),hist.quant,0)) as qMes,
									SUM(if(YEAR(h.data)=YEAR(NOW()),hist.quant,0)) as qAno,
									SUM(if(YEAR(h.data)=YEAR(NOW())-1,hist.quant,0)) as qAnoPassado,
									SUM(if(YEAR(h.data)>0,hist.quant,0)) as qHist,
									
							        (SELECT  SUM( IF(YEAR(s.data) = 2023 AND (peso/volumes*2)>40,round(volumes/2,0),0)) from Analytics.ssw s WHERE s.cnpj=c.cnpj and s.seller='welttec' )	as ssw,	
							        
		  							(SELECT  SUM(h.valor) FROM hoje h WHERE h.idcli=c.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()) AND h.nop in (%s)    GROUP BY h.idcli) AS Mes,
									(SELECT  SUM(h.valor) FROM hoje h WHERE h.idcli=c.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW()) AND h.nop in (%s)  GROUP BY h.idcli) AS Ano, 
									(SELECT  SUM(h.valor) FROM hoje h WHERE h.idcli=c.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW())-1 AND h.nop in (%s)  GROUP BY h.idcli) AS AnoP, 			
									(SELECT  SUM(ch.valor) FROM cheques ch WHERE ch.idcli=c.id AND ch.data < CURDATE()-5 AND ( ISNULL(ch.datadep) OR MONTH(ch.datadep)=0  ) GROUP BY ch.idcli) AS Atrasados,
									(SELECT  SUM(h.valor) FROM hoje h WHERE h.idcli=c.id   and h.prazo<>15 AND h.nop in (%s)    GROUP BY h.idcli) AS TotalCompras,
								mesos.NomeMeso,
								mesos.Meta,
								u.nick
							
							FROM  estatisticas.`vTAB_MUNICIPIOS` mun
							LEFT JOIN  clientes c ON (mun.`NomeMunic`=c.cidade) 
							LEFT JOIN mak.hoje h  ON (h.idcli=c.id) 
							LEFT JOIN mak.hist ON (hist.pedido=h.id) 
							LEFT JOIN mak.inv ON (inv.id=hist.isbn) 
							LEFT JOIN mak.produtos p ON (p.id=inv.idcf)
							LEFT JOIN mak.rolemak_users u ON (u.id=c.vendedor)
							LEFT JOIN estatisticas.`TAB_MESOS` mesos ON (mesos.`Meso`=mun.Meso  )
							
						
							WHERE  p.segmento='machines' AND   h.id> 1200000 AND mun.Meso= '%s' AND   h.nop in (%s) and hist.valor>1000  
							GROUP BY c.id
							Having TotalCompras>0
				    		ORDER BY  qAnoPassado DESC
				    		LIMIT 1000
					",  $this->sales_nop_codes,$this->sales_nop_codes,$this->sales_nop_codes,$this->sales_nop_codes, addslashes($meso),$this->sales_nop_codes );

	    $query = DB::query(Database::SELECT, $sql);
        $result= $query->execute()->as_array();
       
        $start= date('Y-m-01');
        $today= date('Y-m-d');
       
        $result[0]['DaysTotal'] = self::GetUsefulDays();
        $result[0]['DaysTillNow'] =  self::number_of_working_days($start, $today);
        $result[0]['DaysMissing'] =  $result[0]['DaysTotal']-$result[0]['DaysTillNow'];
        $result[0]['DaysPassedPerc'] =  round($result[0]['DaysTillNow']/$result[0]['DaysTotal']*100,0);
        $result[0]['MetaDia'] = round($result[0]['Meta']/$result[0]['DaysTotal'],0);
       
         $result[0]['qAtingida'] = 0;
         //self::distance( $result[0]['cidade'], $result[0]['Meso']);
         $counter=0;
        foreach($result as $key => $value)
        {
        	$counter++;
        	$result[0]['qAtingida']+=  $result[$key]['qMes'];
        	$result[0]['qMiss']=  	$result[0]['Meta']-$result[0]['qAtingida'];
        	$result[0]['qPerc'] =  round($result[0]['qAtingida']/$result[0]['Meta']*100,0);
           $result[$key]['class'] = '';
           $result[$key]['rclass'] = '';
            if($value['qMes']>>0) $result[$key]['class'] = 'text-green-600 font-bold	';
            
           if($value['qMes']==0) $result[$key]['qMes'] = '-';
          
           if($value['qAno']==0) $result[$key]['qAno'] = '-';
           if($value['qAnoPassado']==0) $result[$key]['qAnoPassado'] = '-';
           if($value['qHist']==0) $result[$key]['qHist'] = '-';
           
           if($value['LastPurchase']>=0)
           {
           	 $result[$key]['LastPurchase'] = $value['LastPurchase']. ' dias ';
           	 $result[$key]['rclass'] = '  bg-lime-600 text-sky-50	';
           }
           //if($value['LastPurchase']>7)
           //{ 
           //	 $result[$key]['LastPurchase'] = $value['LastPurchase']. ' dias ';
           //	 $result[$key]['rclass'] = '  bg-blue-700 text-sky-50	';
           //}
           if($value['LastPurchase']>14)
           { 
           	 //$result[$key]['LastPurchase'] = round($value['LastPurchase']/7,0). ' semanas';
           	 $result[$key]['rclass'] = '  bg-blue-700 text-sky-50	';
           }
           if($value['LastPurchase']>31)
           {
           	// $result[$key]['LastPurchase'] = round($value['LastPurchase']/30,0). ' meses ';
           	 $result[$key]['rclass'] = '  bg-orange-500 text-sky-50	 	';
           }
            if($value['LastPurchase']>90)
           {
           	$result[$key]['LastPurchase'] = round($value['LastPurchase']/30,0). ' meses ';
           	 $result[$key]['rclass'] = '  bg-red-500 text-sky-50	 	';
           }
           if($value['LastPurchase']>365)
           {
           	$result[$key]['LastPurchase'] = round($value['LastPurchase']/365,0). ' ano ';
           	 $result[$key]['rclass'] = '  bg-zinc-400 text-sky-100	 	';
           }
           if($value['LastPurchase']>730) $result[$key]['LastPurchase'] = round($value['LastPurchase']/365,0). ' anos ';
           if($value['LastPurchase']==0) $result[$key]['LastPurchase'] = 'Hoje';
            // $result[$key]['Saldo'] =  $meso['Meta']-$meso['qMes'];
            // $result[$key]['Perc'] =  round($meso['qMes']/$meso['Meta'],2)*100;
            $result[$key]['counter'] = $counter;
        }
        
        
        $view = parent::mustache($result,'mesos/meso');
        
        $this->response->body($view);
         //s($result);
	
	}	

	function distance($city,$meso)
	{
			// $neighbors[$key]['Data']= $this->action_city($val['NomeMicro']);
				 $sql= sprintf(" SELECT lat,lng FROM google_markers gm, clientes c 
					WHERE c.id=gm.idcli and c.cidade='%s' 
					ORDER BY limite DESC
					LIMIT 1",
						 $city );

				 $query = DB::query(Database::SELECT, $sql);
        		 $marker= $query->execute()->as_array();
        		 s($marker);
			
			 $sql= sprintf(" SELECT cidade,estado,Meso, ( 3959 * acos( cos( radians(%s) ) * cos( radians( lat ) ) * cos( radians( lng ) - radians(%s) ) + sin( radians(%s) ) * sin( radians( lat ) ) ) ) AS distance 
						FROM google_markers gm, clientes c ,  estatisticas.`vTAB_MUNICIPIOS` mun  
						WHERE c.id=gm.idcli AND mun.`NomeMunic`=c.cidade and mun.`Meso`=%s
						GROUP BY cidade 
						HAVING distance < 100 
						ORDER BY distance 
						LIMIT 50	", 
		 				$marker[0]['lat'],$marker[0]['lng'],$marker[0]['lat'], $meso );

				 $query = DB::query(Database::SELECT, $sql);
        		 $result= $query->execute()->as_array();
        		 s($result);
		}
		
    function countDays($y, $m, $ignore = false) 
    {
        $result = 0;
        $loop = strtotime("$y-$m-01");
        do if(!$ignore or !in_array(strftime("%u",$loop),$ignore))
            $result++;
        while(strftime("%m",$loop = strtotime("+1 day",$loop))==$m);
        return $result;
    }

    function dias_feriados($ano = null)
    {

        if ($ano === null)
        {
            $ano = intval(date('Y'));
        }

        // $pascoa     = easter_date($ano); // Limite de 1970 ou após 2037 da easter_date PHP consulta http://www.php.net/manual/pt_BR/function.easter-date.php
        // $dia_pascoa = date('j', $pascoa);
        // $mes_pascoa = date('n', $pascoa);
        // $ano_pascoa = date('Y', $pascoa);

        $feriados = array(
            // Tatas Fixas dos feriados Nacionail Basileiras
            mktime(0, 0, 0, 1,  1,   $ano), // Confraternização Universal - Lei nº 662, de 06/04/49
            mktime(0, 0, 0, 4,  21,  $ano), // Tiradentes - Lei nº 662, de 06/04/49
            mktime(0, 0, 0, 5,  1,   $ano), // Dia do Trabalhador - Lei nº 662, de 06/04/49
            mktime(0, 0, 0, 9,  7,   $ano), // Dia da Independência - Lei nº 662, de 06/04/49
            mktime(0, 0, 0, 10,  12, $ano), // N. S. Aparecida - Lei nº 6802, de 30/06/80
            mktime(0, 0, 0, 11,  2,  $ano), // Todos os santos - Lei nº 662, de 06/04/49
            mktime(0, 0, 0, 11, 15,  $ano), // Proclamação da republica - Lei nº 662, de 06/04/49
            mktime(0, 0, 0, 12, 25,  $ano), // Natal - Lei nº 662, de 06/04/49

            // These days have a date depending on easter
            // mktime(0, 0, 0, $mes_pascoa, $dia_pascoa - 48,  $ano_pascoa),//2ºferia Carnaval
            // mktime(0, 0, 0, $mes_pascoa, $dia_pascoa - 47,  $ano_pascoa),//3ºferia Carnaval	
            // mktime(0, 0, 0, $mes_pascoa, $dia_pascoa - 2 ,  $ano_pascoa),//6ºfeira Santa  
            // mktime(0, 0, 0, $mes_pascoa, $dia_pascoa     ,  $ano_pascoa),//Pascoa
            // mktime(0, 0, 0, $mes_pascoa, $dia_pascoa + 60,  $ano_pascoa),//Corpus Cirist
        );

        sort($feriados);

        return $feriados;
    }

    public function GetUsefulDays() 
    {

        $ano_=date("Y");// $ano_='2010'; 
        foreach(
            self::dias_feriados($ano_) as $a)
        {
            if(!isset($holydays[date("m",$a) ])) $holydays[date("m",$a) ]=0;
            $holydays[date("m",$a) ]++;
            //echo date("d-M-Y",$a).'<br>';						 
        }

        //print_r($holydays);
        //die();
        $thismonth=date('n');
        //   return self::countDays(2019,2,array(6,7));//23

        $d=0;
        for ($i=1;$i<32;$i++) {
            $day = mktime (0, 0, 0, date("m")  ,$i, date("Y"));
            $date = getdate($day); 
            if ($date['mon']==$thismonth  and  $date['wday']<>0 and  $date['wday']<>6) {
                $d++;
            }   
        }

        // if(isset($holydays[date("m")])) $d = $d-$holydays[date("m")];
        return $d;
    }


    public function GetUsefulDaysIndex() 
    {
        $d=0;
        $useful = $this->GetUsefulDaysTillNow();	
        if ($useful>0) 
        {
            $d=$this->GetUsefulDays()/$this->GetUsefulDaysTillNow();
        }
        return $d;
    }


    public function GetUsefulDaysTillNow() 
    {

        $ano_=date("Y");// $ano_='2010'; 
        foreach(
            self::dias_feriados($ano_) as $a)
        {
            if(!isset($holydays[date("m",$a) ])) $holydays[date("m",$a) ]=0;
            $holydays[date("m",$a) ]++;
            //echo date("d-M-Y",$a).'<br>';						 
        }

        $thismonth=date('n');
        $d=1;

        for ($i=1;$i<date('d');$i++) {
            $day = mktime (0, 0, 0, date("m")  ,$i, date("Y"));
            $date = getdate($day); 
            if ($date['mon']==$thismonth  and  $date['wday']<>0 and  $date['wday']<>6) {
                $d++;
            }   
        }
        // if(isset($holydays[date("m")])) $d = $d-$holydays[date("m")];
        return $d;

    }

    function number_of_working_days($from, $to) {
    $workingDays = [1, 2, 3, 4, 5]; # date format = N (1 = Monday, ...)
    $holidayDays = ['*-12-25', '*-01-01', '2013-12-23']; # variable and fixed holidays

    $from = new DateTime($from);
    $to = new DateTime($to);
    $to->modify('+1 day');
    $interval = new DateInterval('P1D');
    $periods = new DatePeriod($from, $interval, $to);

    $days = 0;
    foreach ($periods as $period) {
        if (!in_array($period->format('N'), $workingDays)) continue;
        if (in_array($period->format('Y-m-d'), $holidayDays)) continue;
        if (in_array($period->format('*-m-d'), $holidayDays)) continue;
        $days++;
    }
    return $days;
}

    public function action_tab1()
    {
        echo 'tab1';
    }
      public function action_tab2()
    {
        echo 'tab2';
    }
      public function action_tab3()
    {
        echo 'tab3';
    }
    
    public function action_distance($city='3501',$type=0)
    {
	    $city = $this->request->param('xtras');
		$sql= sprintf(" SELECT *
						FROM estatisticas.`vTAB_MUNICIPIOS` mun 
						WHERE mun.`Meso`='%s' 
						GROUP BY NomeMicro
						",
						$city);

		$result = $this->action_connect_SELECT($sql);
		
// 		s($result);
		
		foreach($result as $key => $val) {
			// $neighbors[$key]['Data']= $this->action_city($val['NomeMicro']);
				 $sql= sprintf(" SELECT lat,lng FROM google_markers gm, clientes c 
					WHERE c.id=gm.idcli and c.cidade='%s' 
					ORDER BY limite DESC
					LIMIT 1",
						 $val['NomeMicro'] );

				 $marker = $this->action_connect_SELECT($sql);
				 
				 if(!empty($marker)) break;
		}


         $sql= sprintf(" SELECT h.NomeMunic,
                                h.UF,
                                Meso, 
                                NomeMicro,
                                ROUND(( 3959 * acos( cos( radians(%s) ) * cos( radians( lat ) ) * cos( radians( lng ) - radians(%s) ) + sin( radians(%s) ) * sin( radians( lat ) ) ) ),0) AS distance,
                                	TO_DAYS(NOW())-TO_DAYS(max(h.data)) AS  LastPurchase,
                                SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=1,h.quant,0)) as Jan,
								SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=2,h.quant,0)) as Fev,
								SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=3,h.quant,0)) as Mar,
								
								SUM(if(YEAR(h.data)=YEAR(NOW()) AND WEEK(h.data)=WEEK(NOW()),h.quant,0)) as qSemana,
								
                            	SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()),h.quant,0)) as qMes,
								SUM(if(YEAR(h.data)=YEAR(NOW()),h.quant,0)) as qAno,
								SUM(if(YEAR(h.data)=YEAR(NOW())-1,h.quant,0)) as qAnoPassado,
								COUNT(DISTINCT h.idcli) as clientes,
								 (SELECT  SUM( IF(YEAR(s.data) = 2023 AND (peso/volumes*2)>40,round(volumes/2,0),0)) from Analytics.ssw s WHERE s.cnpj=c.cnpj and s.seller='welttec' )	as ssw	
                                
						FROM  mak.vPedidos h  
					
						LEFT JOIN clientes c ON (h.idcli=c.id ) 
						LEFT JOIN google_markers gm  ON (c.id=gm.idcli) 
						
						WHERE h.segmento='machines'  and h.`Meso`=%s  
						GROUP BY  NomeMunic
					
						ORDER BY NomeMicro,distance 
						LIMIT 500	", 
		 				$marker[0]['lat'],$marker[0]['lng'],$marker[0]['lat'], $city );

		 $result = $this->action_connect_SELECT($sql);
// 		 s($neighbors);
        foreach($result as $key => $value)
        {
        	
        
           $result[$key]['class'] = '';
           $result[$key]['rclass'] = '';
            if($value['qMes']>>0) $result[$key]['class'] = 'text-green-600 font-bold	';
            
             if($value['Jan']==0) $result[$key]['Jan'] = '-';
              if($value['Fev']==0) $result[$key]['Fev'] = '-';
               if($value['Mar']==0) $result[$key]['Mar'] = '-';
               
           if($value['qMes']==0) $result[$key]['qMes'] = '-';
           if($value['qAno']==0) $result[$key]['qAno'] = '-';
           
           
           if($value['qAnoPassado']==0) $result[$key]['qAnoPassado'] = '-';
         
           
           if($value['LastPurchase']>=0)
           {
           	 $result[$key]['LastPurchase'] = $value['LastPurchase']. ' dias ';
           	 $result[$key]['rclass'] = '  bg-lime-600 text-sky-50	';
           }
           //if($value['LastPurchase']>7)
           //{ 
           //	 $result[$key]['LastPurchase'] = $value['LastPurchase']. ' dias ';
           //	 $result[$key]['rclass'] = '  bg-blue-700 text-sky-50	';
           //}
           if($value['LastPurchase']>14)
           { 
           	 //$result[$key]['LastPurchase'] = round($value['LastPurchase']/7,0). ' semanas';
           	 $result[$key]['rclass'] = '  bg-blue-700 text-sky-50	';
           }
           if($value['LastPurchase']>31)
           {
           	// $result[$key]['LastPurchase'] = round($value['LastPurchase']/30,0). ' meses ';
           	 $result[$key]['rclass'] = '  bg-orange-500 text-sky-50	 	';
           }
            if($value['LastPurchase']>90)
           {
           	$result[$key]['LastPurchase'] = round($value['LastPurchase']/30,0). ' meses ';
           	 $result[$key]['rclass'] = '  bg-red-500 text-sky-50	 	';
           }
           if($value['LastPurchase']>365)
           {
           	$result[$key]['LastPurchase'] = round($value['LastPurchase']/365,0). ' ano ';
           	 $result[$key]['rclass'] = '  bg-zinc-400 text-sky-100	 	';
           }
           if($value['LastPurchase']>730) $result[$key]['LastPurchase'] = round($value['LastPurchase']/365,0). ' anos ';
           if($value['LastPurchase']==0) $result[$key]['LastPurchase'] = 'Hoje';
            // $result[$key]['Saldo'] =  $meso['Meta']-$meso['qMes'];
            // $result[$key]['Perc'] =  round($meso['qMes']/$meso['Meta'],2)*100;
           
        }
     
		  $view = parent::mustache($result,'mesos/distance');
        
        $this->response->body($view);
}

    public function action_cidade()
    {
         $var = explode('(',$this->request->param('xtras'));
         $city= $var[0];
         
	 	 $sql= sprintf("SELECT  mesos.Meta, c.estado, c.cidade,
		                        mesos.NomeUF,
		                        mesos.Maps,
		                        mesos.Meso,
		                        mesos.NomeMeso,
		                        h.NomeMunic,
								segmento as Cat,
								TO_DAYS(NOW())-TO_DAYS(max(h.data)) AS  LastPurchase,
								SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=1,h.quant,0)) as Jan,
								SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=2,h.quant,0)) as Fev,
								SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=3,h.quant,0)) as Mar,
							    SUM(if( YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()) AND DAY(h.data)=DAY(NOW()), h.quant,0)) as qDia,
								SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()),h.quant,0)) as qMes,
								SUM(if(YEAR(h.data)=YEAR(NOW()),h.quant,0)) as qAno,
								SUM(if(YEAR(h.data)=YEAR(NOW())-1,h.quant,0)) as qAnoPassado,
								count(DISTINCT c.id) as qClientes,
								count(DISTINCT c.cidade) as qCidades
									

							FROM  estatisticas.`vTAB_MUNICIPIOS` mun
							LEFT JOIN  clientes c ON (mun.`NomeMunic`=c.cidade) 
							LEFT JOIN mak.vPedidos h  ON (h.idcli=c.id ) 
						
							LEFT JOIN estatisticas.`TAB_MESOS` mesos ON (mesos.`Meso`=mun.Meso  ) 
						
							WHERE  segmento='machines'     AND mun.NomeMunic= '%s'   
							GROUP BY NomeMunic
							ORDER BY qAnoPassado DESC, qAno desc
						
				    	
				    		LIMIT 1000
					", addslashes($city),$this->sales_nop_codes );

		 $result = $this->action_connect_SELECT($sql);
		 
// 		 s($result);
		 
		       foreach($result as $key => $value)
        {
        	
        
           $result[$key]['class'] = '';
           $result[$key]['rclass'] = '';
            if($value['qMes']>>0) $result[$key]['class'] = 'text-green-600 font-bold	';
            
            if($value['Jan']==0) $result[$key]['Jan'] = '-';
            if($value['Fev']==0) $result[$key]['Fev'] = '-';
            if($value['Mar']==0) $result[$key]['Mar'] = '-';
               
           if($value['qMes']==0) $result[$key]['qMes'] = '-';
           if($value['qAno']==0) $result[$key]['qAno'] = '-';
           
           
           if($value['qAnoPassado']==0) $result[$key]['qAnoPassado'] = '-';
         
           
           if($value['LastPurchase']>=0)
           {
           	 $result[$key]['LastPurchase'] = $value['LastPurchase']. ' dias ';
           	 $result[$key]['rclass'] = '  bg-lime-600 text-sky-50	';
           }
           //if($value['LastPurchase']>7)
           //{ 
           //	 $result[$key]['LastPurchase'] = $value['LastPurchase']. ' dias ';
           //	 $result[$key]['rclass'] = '  bg-blue-700 text-sky-50	';
           //}
           if($value['LastPurchase']>14)
           { 
           	 //$result[$key]['LastPurchase'] = round($value['LastPurchase']/7,0). ' semanas';
           	 $result[$key]['rclass'] = '  bg-blue-700 text-sky-50	';
           }
           if($value['LastPurchase']>31)
           {
           	// $result[$key]['LastPurchase'] = round($value['LastPurchase']/30,0). ' meses ';
           	 $result[$key]['rclass'] = '  bg-orange-500 text-sky-50	 	';
           }
            if($value['LastPurchase']>90)
           {
           	$result[$key]['LastPurchase'] = round($value['LastPurchase']/30,0). ' meses ';
           	 $result[$key]['rclass'] = '  bg-red-500 text-sky-50	 	';
           }
           if($value['LastPurchase']>365)
           {
           	$result[$key]['LastPurchase'] = round($value['LastPurchase']/365,0). ' ano ';
           	 $result[$key]['rclass'] = '  bg-zinc-400 text-sky-100	 	';
           }
           if($value['LastPurchase']>730) $result[$key]['LastPurchase'] = round($value['LastPurchase']/365,0). ' anos ';
           if($value['LastPurchase']==0) $result[$key]['LastPurchase'] = 'Hoje';
            // $result[$key]['Saldo'] =  $meso['Meta']-$meso['qMes'];
            // $result[$key]['Perc'] =  round($meso['qMes']/$meso['Meta'],2)*100;
           
        }
  

					
		foreach($result as $key => $val) {
			$result[$key]['Data']= $this->action_city($val['cidade']);
// 			$result[$key]['Clientes']= $this->customers($val['cidade']);
				list($result[$key]['Clientes'], $result[$key]['TT']) = $this->customers($val['cidade']);
			$result[$key]['cidade'] = ucwords($val[ 'cidade']);
		}
		
  	 //s($result);//
  	 $view = parent::mustache($result,'mesos/cidade');
        
        $this->response->body($view);
 	}	


    public function action_cidades($city='3501',$type=0)
    {
	    $city = $this->request->param('xtras');
		$sql= sprintf(" SELECT *
						FROM estatisticas.`vTAB_MUNICIPIOS` mun 
						WHERE mun.`Meso`='%s' 
						GROUP BY NomeMicro
						",
						$city);

		$result = $this->action_connect_SELECT($sql);
		
// 		s($result);
		
		foreach($result as $key => $val) {
			// $neighbors[$key]['Data']= $this->action_city($val['NomeMicro']);
				 $sql= sprintf(" SELECT lat,lng FROM google_markers gm, clientes c 
					WHERE c.id=gm.idcli and c.cidade='%s' 
					ORDER BY limite DESC
					LIMIT 1",
						 $val['NomeMicro'] );

				 $marker = $this->action_connect_SELECT($sql);
				 
				 if(!empty($marker)) break;
		}


		
		
		 				 
		 $sql= sprintf("SELECT  c.id as idcli, nick, mesos.Meta, c.estado, c.cidade,
		                        mesos.NomeUF,
		                        mesos.Maps,
		                        mesos.Meso,
		                        mesos.NomeMeso,
		                        h.NomeMunic,
								segmento as Cat,
								TO_DAYS(NOW())-TO_DAYS(max(h.data)) AS  LastPurchase,
								SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=1,h.quant,0)) as Jan,
								SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=2,h.quant,0)) as Fev,
								SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=3,h.quant,0)) as Mar,
							    SUM(if( YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()) AND DAY(h.data)=DAY(NOW()), h.quant,0)) as qDia,
								SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()),h.quant,0)) as qMes,
								SUM(if(YEAR(h.data)=YEAR(NOW()),h.quant,0)) as qAno,
								SUM(if(YEAR(h.data)=YEAR(NOW())-1,h.quant,0)) as qAnoPassado,
								count(DISTINCT c.id) as qClientes,
								count(DISTINCT c.cidade) as qCidades
									

							FROM  estatisticas.`vTAB_MUNICIPIOS` mun
							LEFT JOIN  clientes c ON (mun.`NomeMunic`=c.cidade) 
							LEFT JOIN mak.vPedidos h  ON (h.idcli=c.id ) 
						
							LEFT JOIN estatisticas.`TAB_MESOS` mesos ON (mesos.`Meso`=mun.Meso  ) 
						
							WHERE  segmento='machines'     AND mun.Meso= '%s'   
							GROUP BY NomeMunic
							ORDER BY qAnoPassado DESC, qAno desc
						
				    	
				    		LIMIT 1000
					", addslashes($city),$this->sales_nop_codes );

		 $result = $this->action_connect_SELECT($sql);
		 
		       foreach($result as $key => $value)
        {
        	
        
           $result[$key]['class'] = '';
           $result[$key]['rclass'] = '';
            if($value['qMes']>>0) $result[$key]['class'] = 'text-green-600 font-bold	';
            
             if($value['Jan']==0) $result[$key]['Jan'] = '-';
              if($value['Fev']==0) $result[$key]['Fev'] = '-';
               if($value['Mar']==0) $result[$key]['Mar'] = '-';
               
           if($value['qMes']==0) $result[$key]['qMes'] = '-';
           if($value['qAno']==0) $result[$key]['qAno'] = '-';
           
           
           if($value['qAnoPassado']==0) $result[$key]['qAnoPassado'] = '-';
         
           
           if($value['LastPurchase']>=0)
           {
           	 $result[$key]['LastPurchase'] = $value['LastPurchase']. ' dias ';
           	 $result[$key]['rclass'] = '  bg-lime-600 text-sky-50	';
           }
           //if($value['LastPurchase']>7)
           //{ 
           //	 $result[$key]['LastPurchase'] = $value['LastPurchase']. ' dias ';
           //	 $result[$key]['rclass'] = '  bg-blue-700 text-sky-50	';
           //}
           if($value['LastPurchase']>14)
           { 
           	 //$result[$key]['LastPurchase'] = round($value['LastPurchase']/7,0). ' semanas';
           	 $result[$key]['rclass'] = '  bg-blue-700 text-sky-50	';
           }
           if($value['LastPurchase']>31)
           {
           	// $result[$key]['LastPurchase'] = round($value['LastPurchase']/30,0). ' meses ';
           	 $result[$key]['rclass'] = '  bg-orange-500 text-sky-50	 	';
           }
            if($value['LastPurchase']>90)
           {
           	$result[$key]['LastPurchase'] = round($value['LastPurchase']/30,0). ' meses ';
           	 $result[$key]['rclass'] = '  bg-red-500 text-sky-50	 	';
           }
           if($value['LastPurchase']>365)
           {
           	$result[$key]['LastPurchase'] = round($value['LastPurchase']/365,0). ' ano ';
           	 $result[$key]['rclass'] = '  bg-zinc-400 text-sky-100	 	';
           }
           if($value['LastPurchase']>730) $result[$key]['LastPurchase'] = round($value['LastPurchase']/365,0). ' anos ';
           if($value['LastPurchase']==0) $result[$key]['LastPurchase'] = 'Hoje';
            // $result[$key]['Saldo'] =  $meso['Meta']-$meso['qMes'];
            // $result[$key]['Perc'] =  round($meso['qMes']/$meso['Meta'],2)*100;
           
        }
  

					
		foreach($result as $key => $val) {
			$result[$key]['Data']= $this->action_city($val['cidade']);
			list($result[$key]['Clientes'], $result[$key]['TT']) = $this->customers($val['cidade']);
			$result[$key]['cidade'] = ucwords($val[ 'cidade']);
		}
		
  	 //s($result);
  	 $view = parent::mustache($result,'mesos/cidades');
        
        $this->response->body($view);
 	}	


	public function action_city($city='',$date='',$business=1)
	{
	
		
			$sql= sprintf("SELECT  SUM(if(YEAR(hoje.data)=YEAR(NOW()) AND MONTH(hoje.data)=MONTH(NOW()),hist.quant,0)) as qMes,
			                    sum(hist.valor_base*hist.quant) as tValor,
		                       clientes.estado as estado,
							   LCASE(clientes.cidade) as cidade,
							   e.regiao,
							   sum(hist.quant) as qValor,
							   count(distinct hist.idcli) as qClientes,
							     sum(if( inv.classe<2, hist.valor_base*hist.quant ,0)) as vClass1,
							   sum(if( inv.classe=2, hist.valor_base*hist.quant ,0)) as vClass2,
							   sum(if( inv.classe=3, hist.valor_base*hist.quant ,0)) as vClass3,
							   sum(if( inv.classe>3, hist.valor_base*hist.quant ,0)) as vClass4,
							   
		                       DATE(hoje.data) as date
							
						FROM  hist,inv,produtos,users,hoje,clientes 	
						LEFT JOIN estados e ON ( e.uf=clientes.estado)
						WHERE   hoje.nop in (%s)  AND
						       	
						  		clientes.id=hoje.idcli AND 								
						 		users.id = hoje.vendedor AND							
						 		hist.pedido=hoje.id  AND
        						inv.id=hist.isbn AND 
       							 produtos.id=inv.idcf AND
								 hist.idcli <>707602 AND
								 
								 hoje.id > %s AND
								 produtos.segmento = '%s' AND
								 clientes.cidade='%s' AND 
								 hist.valor_base>0
						GROUP BY  clientes.cidade		 
				    	ORDER BY tValor  Desc 
				    	
					",$this->sales_nop_codes,
						 
					000000, 
						$this->segs[$business]['Name'],
						addslashes($city) );

		$result = $this->action_connect_SELECT($sql);
		$vtotal=0;
// 		s($result);
		if(empty($result)) return [];
		foreach ( $result as $k => $v) {
			$vtotal+=$v['tValor'];
		}
			
	    return $result;		
		

	}	
	
	private function customers($city='campinas')
  	{
	
       
		 $sql= sprintf("SELECT   c.*,LCASE(c.nome) as nome,LCASE(c.ender) as ender,  c.id as idcli, u.nick, c.cnpj,
								    p.segmento as Cat,
								
									TO_DAYS(NOW())-TO_DAYS(max(h.data)) AS  LastPurchase,
									
									SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=1,hist.quant,0)) as Jan,
									SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=2,hist.quant,0)) as Fev,
									SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=3,hist.quant,0)) as Mar,
									SUM(if(YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()),hist.quant,0)) as qMes,
									SUM(if(YEAR(h.data)=YEAR(NOW()),hist.quant,0)) as qAno,
									SUM(if(YEAR(h.data)=YEAR(NOW())-1,hist.quant,0)) as qAnoPassado,
									SUM(if(YEAR(h.data)>0,hist.quant,0)) as qHist,
									
							        (SELECT  SUM( IF(YEAR(s.data) = 2023 AND (peso/volumes*2)>40,round(volumes/2,0),0)) from Analytics.ssw s WHERE s.cnpj=c.cnpj and s.seller='welttec' )	as ssw,	
							        (SELECT  SUM( IF(YEAR(s.data) = 2022 AND (peso/volumes*2)>40,round(volumes/2,0),0)) from Analytics.ssw s WHERE s.cnpj=c.cnpj and s.seller='welttec' )	as sswPassado,	
							        (SELECT  SUM( IF(YEAR(s.data) = 2023 AND MONTH(s.data)=MONTH(NOW()) AND (peso/volumes*2)>40,round(volumes/2,0),0)) from Analytics.ssw s WHERE s.cnpj=c.cnpj and s.seller='welttec' )	as sswMar,
							        (SELECT  SUM( IF(YEAR(s.data) = 2023 AND MONTH(s.data)=MONTH(NOW())-1 AND (peso/volumes*2)>40,round(volumes/2,0),0)) from Analytics.ssw s WHERE s.cnpj=c.cnpj and s.seller='welttec' )	as sswFev,
							        (SELECT  SUM( IF(YEAR(s.data) = 2023 AND MONTH(s.data)=MONTH(NOW())-2 AND (peso/volumes*2)>40,round(volumes/2,0),0)) from Analytics.ssw s WHERE s.cnpj=c.cnpj and s.seller='welttec' )	as sswJan,
							        
		  							(SELECT  SUM(h.valor) FROM hoje h WHERE h.idcli=c.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(NOW()) AND h.nop in (%s)    GROUP BY h.idcli) AS Mes,
									(SELECT  SUM(h.valor) FROM hoje h WHERE h.idcli=c.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW()) AND h.nop in (%s)  GROUP BY h.idcli) AS Ano, 
									(SELECT  SUM(h.valor) FROM hoje h WHERE h.idcli=c.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW())-1 AND h.nop in (%s)  GROUP BY h.idcli) AS AnoP, 			
									(SELECT  SUM(ch.valor) FROM cheques ch WHERE ch.idcli=c.id AND ch.data < CURDATE()-5 AND ( ISNULL(ch.datadep) OR MONTH(ch.datadep)=0  ) GROUP BY ch.idcli) AS Atrasados,
									(SELECT  SUM(h.valor) FROM hoje h WHERE h.idcli=c.id   and h.prazo<>15 AND h.nop in (%s)    GROUP BY h.idcli) AS TotalCompras,
								u.nick
							
							FROM   clientes c
							LEFT JOIN mak.hoje h  ON (h.idcli=c.id) 
							LEFT JOIN mak.hist ON (hist.pedido=h.id) 
							LEFT JOIN mak.inv ON (inv.id=hist.isbn) 
							LEFT JOIN mak.produtos p ON (p.id=inv.idcf)
							LEFT JOIN mak.rolemak_users u ON (u.id=c.vendedor)
							
						
							WHERE  p.segmento='machines' AND   h.id> 00000 AND c.cidade= '%s' AND   h.nop in (%s) and hist.valor>1000  AND c.vendedor <> 9
							GROUP BY c.id
							Having TotalCompras>0
				    		ORDER BY  qAnoPassado DESC
				    		LIMIT 1000
					",  $this->sales_nop_codes,$this->sales_nop_codes,$this->sales_nop_codes,$this->sales_nop_codes, addslashes($city),$this->sales_nop_codes );

	    $query = DB::query(Database::SELECT, $sql);
        $result= $query->execute()->as_array();
        // s($result);
         $tt['jan']= $tt['fev']=$tt['mar']=$counter=0;
        
        foreach($result as $key => $value)
        {
            $counter++;
               $result[$key]['counter'] = $counter; 
            
            $tt['jan']+=$value['Jan'];
            $tt['fev']+=$value['Fev'];
            $tt['mar']+=$value['Mar'];
            
           $pcz=0;    
           $heart='';
           $qtt= $value['qAno']+$value['ssw'];
           if($qtt>20)
           {
               
              
               if($qtt>0) $pcz= round($value['ssw']/$qtt,2)*10;
            //   s($pcz);
               if($pcz > 0.0 and $pcz < 1.0) $pcz=1;
               
               for($x=1; $x <= $pcz; $x++)
               {
                    $heart.= '💙';
               }
               for($x=1; $x <= 10-$pcz; $x++)
               {
                    $heart.= '💚';
               }
           }
           
           if($qtt>20) $heart.=' ('.$qtt.')';
           
           $result[$key]['hearts'] = $heart;
           
        //   s($pcz);
        //   echo '<br>'.$heart;
        
           
            
            
            $result[$key]['class'] = '';
            $result[$key]['rclass'] = '';
            
            if($value['qMes']>>0) $result[$key]['class'] = 'text-green-600 font-bold	';
            
            if($value['Jan']==0) $result[$key]['Jan'] = '-';
            if($value['Fev']==0) $result[$key]['Fev'] = '-';
            if($value['Mar']==0) $result[$key]['Mar'] = '-';
            if($value['qMes']==0) $result[$key]['qMes'] = '-';
            if($value['qAno']==0) $result[$key]['qAno'] = '-';
            
            

           
           if($value['qAnoPassado']==0) $result[$key]['qAnoPassado'] = '-';
         
           
           if($value['LastPurchase']>=0)
           {
           	 $result[$key]['LastPurchase'] = $value['LastPurchase']. ' dias ';
           	 $result[$key]['rclass'] = '  bg-lime-600 text-sky-50	';
           }
           //if($value['LastPurchase']>7)
           //{ 
           //	 $result[$key]['LastPurchase'] = $value['LastPurchase']. ' dias ';
           //	 $result[$key]['rclass'] = '  bg-blue-700 text-sky-50	';
           //}
           if($value['LastPurchase']>14)
           { 
           	 //$result[$key]['LastPurchase'] = round($value['LastPurchase']/7,0). ' semanas';
           	 $result[$key]['rclass'] = '  bg-blue-700 text-sky-50	';
           }
           if($value['LastPurchase']>31)
           {
           	// $result[$key]['LastPurchase'] = round($value['LastPurchase']/30,0). ' meses ';
           	 $result[$key]['rclass'] = '  bg-orange-500 text-sky-50	 	';
           }
            if($value['LastPurchase']>90)
           {
           	$result[$key]['LastPurchase'] = round($value['LastPurchase']/30,0). ' meses ';
           	 $result[$key]['rclass'] = '  bg-red-500 text-sky-50	 	';
           }
           if($value['LastPurchase']>365)
           {
           	$result[$key]['LastPurchase'] = round($value['LastPurchase']/365,0). ' ano ';
           	 $result[$key]['rclass'] = '  bg-zinc-400 text-sky-100	 	';
           }
           if($value['LastPurchase']>730) $result[$key]['LastPurchase'] = round($value['LastPurchase']/365,0). ' anos ';
           if($value['LastPurchase']==0) $result[$key]['LastPurchase'] = 'Hoje';
            // $result[$key]['Saldo'] =  $meso['Meta']-$meso['qMes'];
            // $result[$key]['Perc'] =  round($meso['qMes']/$meso['Meta'],2)*100;
           
        }
    // s($result);
        return [$result, $tt];
       
        //
	
	}	

}