<?php

class Controller_Monitor extends Controller
{

   
   public function before()
    {
         parent::before();
    }


    public function action_index()
    {     
     
        $vars =  $this->request->post();
        
        // s($vars);
 
        $sql= sprintf("INSERT INTO estatisticas.`vallery_kohana_monitor` (`id`, `userid`, `host`, `endpoint`, `acao`, `controlador`, `diretorio`, `post`, `query`) 
                        VALUES (NULL, %s, '%s', '%s', '%s', '%s', '%s', '%s', '%s')", 
                        $vars[0], 
                        $vars[1], 
                        $vars[2], 
                        $vars[3], 
                        $vars[4], 
                        $vars[5], 
                        $vars[6], 
                        $vars[7]
                    ) ;
        $query = DB::query(Database::INSERT, $sql);
        $result = $query->execute();
        
        exit;
       
    }


  
   
}