
<?php

class Controller_Nakata extends Controller_Website
{

   
   public function before()
    {
         parent::before();
         
    }

    public function action_index()
    {
        $segid = $this->request->param('division');
        $brand = $this->request->param('xtras');

       
            $data = self::get();
            // $data['caption']= self::get();
    

        $theme='datatables';
        $view = '';
        
      
        $color['Diff'][0]['class'] = ' text-red-400 text-lg font-bold';
        $color['Diff'][0]['rule']  = ' < 1 ';
        $color['Diff'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['Diff'][1]['rule']  = ' > 1 ';
        // $color['Diff'][2]['class'] = ' text-blue-600 text-md  font-bold';
        // $color['Diff'][2]['rule']  = ' > 75';
        // $color['Diff'][3]['class'] = ' text-green-700 text-md  font-bold';
        // $color['Diff'][3]['rule']  = ' > 100 ';
        
        
        $color['Modelo']['class'] = ' text-blue-500 text-lg font-bold';
        $color['M17']['class'] = ' text-green-600 text-lg font-bold';
        $color['M16']['class'] = ' text-blue-500 text-lg font-bold';
        $color['M15']['class'] = ' text-blue-400 text-lg font-bold';
        $color['M14']['class'] = ' text-orange-500 text-base font-bold';
        $color['M13']['class'] = ' text-orange-400 text-base font-bold';
        $color['M12']['class'] = ' text-orange-300 text-base font-bold';
        $color['M11']['class'] = ' text-orange-200 text-base font-bold';
        $color['M10']['class'] = ' text-orange-600 text-base font-bold';
        $color['M9']['class'] = ' text-red-700 text-sm font-bold';
        $color['M8']['class'] = ' text-red-600 text-xs font-bold';
        $color['M7']['class'] = ' text-red-500 text-xs font-bold';
        $color['M6']['class'] = ' text-red-400 text-xs font-bold';
        $color['M5']['class'] = ' text-red-300 text-xs font-bold';
        // $color['Modelo'][0]['rule']  = ' = 0 ';
    //     $color['PercMês'][1]['class'] = ' text-red-400 text-md font-bold';
    //     $color['PercMês'][1]['rule']  = ' > 5 ';
    //     $color['PercMês'][2]['class'] = ' text-blue-600 text-md  font-bold';
    //     $color['PercMês'][2]['rule']  = ' > 10';
    //     $color['PercMês'][3]['class'] = ' text-green-700 text-md  font-bold';
    //     $color['PercMês'][3]['rule']  = ' > 20 ';
        
        $color['Subtotal'][0]['class'] = ' text-red-400 text-xs font-bold';
        $color['Subtotal'][0]['rule']  = ' > 10000 ';
        $color['Subtotal'][1]['class'] = ' text-yellow-600 text-sm font-bold';
        $color['Subtotal'][1]['rule']  = ' > 100000 ';
        $color['Subtotal'][2]['class'] = ' text-blue-600 text-base  font-bold';
        $color['Subtotal'][2]['rule']  = ' > 500000';
        $color['Subtotal'][3]['class'] = ' text-green-700 text-lg  font-bold';
        $color['Subtotal'][3]['rule']  = ' > 1000000 ';
        
        $data['color'] = $color;
        
        //// Editable
         
         $edt['Nome'] = 'mak.inv|nome|id|_ISBN';
         $edt['Revenda'] = 'mak.inv|revenda|id|_ISBN';
         $edt['Idx'] = 'mak.inv|costindex|id|_ISBN';
         $data['editable'] = $edt;
        
        // $idven =  $this->request->query('idven');
        // $baseurl='/metrics/customers/index';
        
        
            
        // s( $this->pagination);
        // $data['pagination'] = $this->pagination;
        $data['theme'] = 3;
        
        // $fmt['V']= $fmt['V22']= $fmt['V21']= $fmt['V20']= $fmt['V19']= $fmt['V18']= $fmt['V17']= $fmt['V16'] = '0ms';
         $fmt['M17']=  $fmt['V13']= $fmt['V12']= $fmt['V11']= $fmt['Subtotal'] = '0br';
        // $fmt['V9']= $fmt['V8']= $fmt['V7']= $fmt['V6']= $fmt['V5']= $fmt['V4'] = '0ms';
        // $fmt['AGE'] = '0us'; 
         $data['format'] = $fmt;
        
        
    //   $data['found_rows'] = $found_rows;
        // $data['pager']['pagination'] = $this->pagination;
        // // die();
        
        
       
        
        $view.= parent::tablefy( $data );
        
        // s($data['pagination']);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);
        // s($data);

        // echo 'index '. $this->request->param('id');
        // $inv = json_decode(self::get_product(), true);

        //s($inv);
        // $solr = self::get_solr($code);
        //  s($solr);
        //  die();
        // $this->product = array_merge($inv, $solr);
        //s($this->product );
        //if($this->product['produtoComprasSugeridoUnidades'] < 0)  die('Quantidade sugerida menor que 0 . Favor vericar se já existe pedidos de compra suficientes para este produto.');

        //json_decode(self::get_product(),true);

        // $view = View::factory('history')
        //     ->set('menu', '')
        //     ->set('array', $this->product)
        //     ->set('factories', $factories)
        //     ->set('next', $next);

        // $this->response->body($view);
    }
  

    private function get()
    {
        $having="";
        $where="";
        $this->group="hi.isbn";
        
        // if($this->request->query('not')) $where.= sprintf(" AND %s ", $this->request->query('not'));
        // echo $where;
        // die();
        
        if($this->request->query('blue')) $where.= sprintf(" AND  r.blue > %s", $this->request->query('blue'));
        
        if($this->request->query('rrh')) $where.= sprintf(" AND  r.rolemak_ranking_history >0 AND r.rolemak_ranking_history <= %s ", $this->request->query('rrh'));
        if($this->request->query('rrn')) $where.= sprintf(" AND  r.rolemak_ranking_now >0 AND r.rolemak_ranking_now <= %s ", $this->request->query('rrn'));
        if($this->request->query('gr')) $where.= sprintf(" AND  r.global_ranking >0 AND r.global_ranking <= %s ", $this->request->query('gr'));
        if($this->request->query('meso')) $where.= sprintf(" AND meso LIKE '%s' ", $this->request->query('meso').'%');
        if($this->request->query('estado')) $where.= sprintf(" AND estado='%s' ", $this->request->query('estado'));
        if($this->request->query('regiao')) $where.= sprintf(" AND regiao='%s' ", $this->request->query('regiao'));
        if($this->request->query('cidade'))
        {
            if(substr($this->request->query('cidade'),0,1)=='!')
            {
                $where.= sprintf(" AND cidade != '%s' ",  ltrim($this->request->query('cidade'), '!'));
            }else{
                $where.= sprintf(" AND cidade='%s' ", $this->request->query('cidade'));
            }
        }
        if($this->request->query('nome')) $where.= sprintf(" AND c.nome LIKE '%s' ", '%'.$this->request->query('nome').'%');
        if($this->request->query('desc')) $where.= sprintf(" AND i.nome LIKE '%s' ", '%'.$this->request->query('desc').'%');
        if($this->request->query('nfe')) $where.= sprintf(" AND nfe LIKE '%s' ", $this->request->query('nfe').'%');
        if($this->request->query('nick')) $where.= sprintf(" AND u.nick LIKE '%s' ", $this->request->query('nick').'%');
        if($this->request->query('marca')) $where.= sprintf(" AND i.marca LIKE '%s' ", $this->request->query('marca').'%');
        if($this->request->query('modelo')) $where.= sprintf(" AND i.modelo LIKE '%s' ", '%'.$this->request->query('modelo').'%');
        if($this->request->query('data')) $where.= sprintf(" AND h.data LIKE '%s' ", $this->request->query('data').'%');
        if($this->request->query('cep')) $where.= sprintf(" AND c.cep LIKE '%s' ", $this->request->query('cep').'%');
        if($this->request->query('group')) $this->group= sprintf(" %s ", $this->request->query('group'));
        // if($this->request->query('vols')) $having= sprintf(" Having vols %s ", $this->request->query('vols'));
        if($this->request->query('recencia')) $having= sprintf(" Having `Dias` BETWEEN %s ", $this->request->query('recencia'));
        if($this->request->query('blue')) $having= sprintf(" Having `IdxBlue` BETWEEN %s ", $this->request->query('blue'));
        if($this->request->query('a2021')) $having= sprintf(" Having `QtA2022` < 1 ");
   						       
         $sql = sprintf("SELECT i.id as _ISBN, 
                                Nakata, 
                                JW, %, 
                                Curv,
                                Total as TotalAno, 
                                round(total/12,0) as MédiaMês, 
                                i.Modelo,
                                i.Revenda,
                                round(total*.05,0) as Target,
                                Fob,
                                
                                ( (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE i.id=e1.`ProdutoPOID`) + (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE i.id=e3.`ProdutoPOID`) + (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE i.id=e5.`ProdutoPOID`) + (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE i.id=e6.`ProdutoPOID`) + (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE i.id=e6t.`ProdutoPOID`) ) as Estoque,
                                
                                 (SELECT   sum(n.quant) 
						        FROM next n
                                LEFT JOIN  shipments sh on (sh.id=n.shipment)	
                                LEFT JOIN  next_stage ns on (ns.id=n.stage)		  
				                WHERE   n.quant>0 AND
                                    n.state <>9 AND 
                                    MONTH( sh.status)=0 AND
                                    n.isbn = i.id AND
                                    ns.stage='shipping'
                                GROUP BY ns.stage) as EmTransito,
                                
                                   (SELECT SUM(n.quant)  FROM next n ,shipments s WHERE s.id=n.shipment AND month(s.status)=0  
						    and  n.isbn =i.id and n.state<>9 GROUP BY n.isbn) AS ComprasColocadas ,   
                               
                               (SELECT SUM(hist.quant) as qProd FROM hist,hoje WHERE TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 30 AND hoje.nop in (27,28,51,76) AND hist.isbn=i.id AND hist.pedido=hoje.id AND hist.idcli <>707602 AND hist.valor_base>0 GROUP BY hist.isbn) AS V30, 
                                                
                                ROUND( ( ( (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE i.id=e1.`ProdutoPOID`) + (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE i.id=e3.`ProdutoPOID`) + (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE i.id=e5.`ProdutoPOID`) + (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE i.id=e6.`ProdutoPOID`) + (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE i.id=e6t.`ProdutoPOID`) ) / round((SELECT SUM(hist.quant) as qProd FROM hist,hoje WHERE TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 90 AND hoje.nop in (27,28,51,76) AND hist.isbn=i.id AND hist.pedido=hoje.id AND hist.idcli <>707602 AND hist.valor_base>0 GROUP BY hist.isbn)/3,0) ), 0) AS VidaEstoqueMeses, 
        
                                (SELECT SUM(hist.quant) as qProd FROM hist,hoje WHERE TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 365 AND hoje.nop in (27,28,51) AND hist.isbn=i.id AND hist.pedido=hoje.id AND hist.idcli <>707602 AND hist.valor_base>0 GROUP BY hist.isbn) AS V365,
            
 
                                (SELECT TO_DAYS(now()) - TO_DAYS(shipments.status)+1 as dias FROM next RIGHT JOIN shipments ON shipments.id=next.shipment WHERE month(shipments.status)>0 and next.isbn=i.id and next.state <>9 and next.quant >0 ORDER BY dias asc LIMIT 1) as IdadeDias,
            
                                (SELECT TO_DAYS(NOW()) - TO_DAYS(hoje.data)+1 FROM hoje,hist WHERE hoje.id=hist.pedido AND hist.isbn =i.id AND hoje.nop in (27,28,51) GROUP BY hoje.data ORDER BY hist.id DESC LIMIT 1 ) UltimaVendaDias
                                
                                FROM Analytics.nakata n
                                
                                LEFT JOIN mak.inv i ON (n.mak=i.modelo)
                                
                                LIMIT 500" ,  $where, $this->group  );
                                
                                // die($sql);
   
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute()->as_array();
           
            $vt = 0;
            
            
            
            // s($result);
            
            
            // $rv = self::chart_prepare($result);
            //  //s($rv);
            //  //die();
            // return $rv;
            // // $rv = self::classify($result);
            
            
            //  die();
            
         return $result ;
        
    }
  


    
    function classify($result)
    {
        $v  = array_column($result, 'V22');
        array_multisort($v, SORT_DESC, $result);
        
        // for ($i=1; $i<=12;$i++)
        // {
        //     $func = 'X'.$i;  
            
        //     foreach ($result as $key => $row) 
        //     {    
        //         $rank = $row[$func];
        //         if($row[$func]==0) $rank='-';
        //          $rv[$row['_ISBN']]['X'.$i] = $rank;
        //          $q[$row['_ISBN']][]=$row[$func];
                 
        //     }
    
        // }
        
        // foreach ($result as $key => $row) 
        // {
        //     $rv[$row['_ISBN']]['X2021'] =  ($row['Q21']<1) ? '-' : $row['Q21'];
        // }


         
        foreach ($result as $key => $row) 
        {
           
          
            $rv[$row['_ISBN']]['_ISBN'] = $row['_ISBN'];
            $rv[$row['_ISBN']]['FOB'] = $row['fob'];
            $rv[$row['_ISBN']]['Estoque'] = $row['Estoque'];
            $rv[$row['_ISBN']]['Subtotal'] = $row['Estoque']*$row['fob']*_DOLAR*$row['costindex'];
            $fob = $row['fob'];
            $idx = $row['costindex'];
            $dolar = _DOLAR;
            $costa =  round($fob*$idx*$dolar,0);
            
            $costb = $costa/.85;
            $TaxEven = round($costb,0);
            
            
            $rv[$row['_ISBN']]['Marca'] = $row['marca'];
            $rv[$row['_ISBN']]['Modelo'] = $row['modelo'];
            $rv[$row['_ISBN']]['Nome'] = $row['nome'];
            $rv[$row['_ISBN']]['Revenda'] = $row['revenda'];
            $rv[$row['_ISBN']]['10%Avista'] = $row['revenda']*.85;
            $rv[$row['_ISBN']]['5%Em-5x'] = $row['revenda']*.90;
            $rv[$row['_ISBN']]['Diff'] = 0;
            for ($i=20; $i>=17;$i--)
            {
                $rv[$row['_ISBN']]['M'.$i] = round($costb/((100-$i)/100),-1);
            }
            
            if ($rv[$row['_ISBN']]['M17']>0) $rv[$row['_ISBN']]['Diff'] = round($row['revenda']/$rv[$row['_ISBN']]['M17'],2);

            $rv[$row['_ISBN']]['Clientes'] = $row['idclis'];
            
            $rv[$row['_ISBN']]['Q2022'] =  ($row['Q22']<1) ? '-' : $row['Q22'];
            
            $rv[$row['_ISBN']]['V'] =  round($row['V']/1000,0).'K';
            // $rv[$row['_ISBN']]['V22'] = ($row['V22']< 1) ? '-' :  round($row['V22']/1000,0).'K';
            // $rv[$row['_ISBN']]['PC'] =  ($row['PC']<= 0) ? '-' : round($row['PC'],1).'%';
            
            $rv[$row['_ISBN']]['Idx'] = $row['costindex'];
        }

           
        // for ($i=1; $i<=10;$i++)
        // {
        //     $func = 'Q'.$i;  
            
        //     foreach ($result as $key => $row) 
        //     {    
        //         $rank = $row[$func];
        //         if($row[$func]==0) $rank='-';
        //          $rv[$row['_ISBN']]['Q'.$i] = $rank;
        //          $q[$row['_ISBN']][]=$row[$func];
                 
        //     }
    
        // }
        
        // foreach ($q as $key => $row) 
        // {
        //     $rv[$key]['Mean']      = round(Phpml\Math\Statistic\Mean::arithmetic($row),0); 
        //     $rv[$key]['Deviation']= round(Phpml\Math\Statistic\StandardDeviation::population($row),0); 
        // }
 
          
        // for ($i=22; $i>=19;$i--)
        // {
        //     $func = 'V'.$i;  
        //     $var  = array_column($result, $func);
        //     array_multisort($var, SORT_DESC, $result);
        //     foreach ($result as $key => $row) 
        //     {    
        //         $rank = $key+1;
        //         if($row[$func]==0) $rank='-';
        //          $rv[$row['_ISBN']]['A'.$i] = $rank;
        //         //  $info['rankh'] = $rv[$row['_ISBN']]['rTotal'];
        //          $info['rankn'] = $rv[$row['_ISBN']]['A22'];
        //          $info['idcli'] = $row['_ISBN'];
        //         //  self::store($info);
        //     }
            
        // }
 
  foreach ($rv as $key => $row) 
        {
            $resp[] = $row; 
            
        }
        
        // d($rv);
        // die();
        return $resp;
        
    }

}