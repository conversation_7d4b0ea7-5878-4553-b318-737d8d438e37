<?php

class Controller_Nfe extends Controller_Website
{

   
   public function before()
    {
         parent::before();
         
    }

    public function action_product()
    {
        $unity  = $this->request->param('division');
        $nnfe  = $this->request->param('xtras');


        $data = self::nfe_product($unity,$nnfe);
        
        // s($data);
        // $factories = self::factories();
        $data['theme'] = 3;
        
        // field color    
        $color['CFOP'][0]['class'] = ' text-blue-700 text-xl font-bold ';
        $color['CFOP'][0]['rule']  = ' == 5102 ';
        $color['CFOP'][1]['class'] = ' text-red-700 text-xl font-bold ';
        $color['CFOP'][1]['rule']  = ' == 6102 ';
        $color['CFOP'][2]['class'] = ' text-gray-300 text-xl font-bold ';
        $color['CFOP'][2]['rule']  = ' == 6912 ';
        $color['CFOP'][3]['class'] = ' text-gray-300 text-xl font-bold ';
        $color['CFOP'][3]['rule']  = ' == 5912 ';
        
        
        $color['AlíquotaIcms'][0]['class'] = ' text-purple-700 text-3xl font-bold ';
        $color['AlíquotaIcms'][0]['rule']  = ' == 0 ';
        $color['AlíquotaIcms'][1]['class'] = ' text-green-700 text-2xl font-bold ';
        $color['AlíquotaIcms'][1]['rule']  = ' == 4 ';
        $color['AlíquotaIcms'][2]['class'] = ' text-blue-700 text-xl font-bold ';
        $color['AlíquotaIcms'][2]['rule']  = ' == 8.8 ';
        $color['AlíquotaIcms'][3]['class'] = ' text-red-700 text-xl font-bold ';
        $color['AlíquotaIcms'][3]['rule']  = ' > 16 ';

        // $color['CFOP'][2]['class'] = ' text-red-700 text-1xl font-bold ';
        // $color['CFOP'][2]['rule']  = ' UltimaVendaDias > IdadeDias ';
          $data['color'] = $color;
        $view= parent::tablefy( $data );
        
        // s($data);
       $theme='light';
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);
        

       

    }

    private function nfe_product($unity=8,$id)
    {
        // s($unity,$id);
         $cnpj = '01103171000109_NFeProdutos';
        switch ($unity) {
            case "1":
                $cnpj = '01103171000109_NFeProdutos';
                break;
            case "3":
                $cnpj = '01103171000370_NFeProdutos';
                break;
            case "6":
                $cnpj = '01103171000613_NFeProdutos';
                break;
            case "8":
                $cnpj = '01103171000885_NFeProdutos';
                break;
    
        }
       
        // s($cnpj);
        // echo $sql = sprintf('SELECT * FROM NFE.`01103171000885_NFeProdutos` WHERE cProd = %s ORDER BY `nNF` DESC
        //                 LIMIT 10', $id);
        $sql = sprintf('SELECT  c.id as _IDCLI,c.Nome as Cliente,c.Estado, EmissorPOID as Unidade, cProd as _ISBN, Modelo, Marca, nNF as Nfe,CFOP,impostos,xProd as Descrição,dEmi as DataEmissão, qCom as Quant, vUnCom as Valor, vProd as Subtotal
                        FROM NFE.%s n 
                        LEFT JOIN mak.hoje h ON (h.id=n.xPed)
                        LEFT JOIN mak.inv i ON (i.id=n.cProd)
                        LEFT JOIN mak.clientes c ON (c.id=h.idcli)
                        WHERE cProd = %s ORDER BY `nNF` DESC
                        LIMIT 20', $cnpj, $id);
                        

        $result = $this->action_connect_SELECT($sql);
        
        // s($result);
        foreach( $result as $key => $val) 
        {
            $taxes = json_decode($val['impostos'],true);
            //  s($taxes);
            //$result[$key]['vIcms']='';
            $result[$key]['AlíquotaIcms']=0;
            $result[$key]['AlíquotaIcmsST']=0;
            //$result[$key]['vIPI']=0;
            $result[$key]['AlíquotaIpi']=0;
            foreach( $taxes as $ke => $ve) 
            {
                
                if(is_array($ve) and key($ve) =='ICMS20')
                {
                    $data[0]= $ve['ICMS20'];
                    $data['theme'] = 2;
                    $theme='light';
                  //  $result[$key]['vIcms']= $ve['ICMS20']['vICMS'] ;
                    $result[$key]['AlíquotaIcms']= round($ve['ICMS20']['vICMS']/$val['Subtotal']*100,2) ;
                    $result[$key]['Icms']= parent::tablefy( $data );
                   
                }
                
                if(is_array($ve) and key($ve) =='ICMS00')
                {
                    $data[0]= $ve['ICMS00'];
                    $data['theme'] = 2;
                    $theme='light';
                   // $result[$key]['vIcms']= $ve['ICMS00']['vICMS'] ;
                    $result[$key]['AlíquotaIcms']= round($ve['ICMS00']['vICMS']/$val['Subtotal']*100,2) ;
                    $result[$key]['Icms']= parent::tablefy( $data );
                   
                }
                
                if(is_array($ve) and key($ve) =='ICMS10')
                {
                    $data[0]= $ve['ICMS10'];
                    $data['theme'] = 2;
                    $theme='light';
                    //$result[$key]['vIcms']= $ve['ICMS10']['vICMS'] ;
                    $result[$key]['AlíquotaIcms']= round($ve['ICMS10']['vICMS']/$val['Subtotal']*100,2) ;
                    
                    $result[$key]['AlíquotaIcmsST']= round($ve['ICMS10']['vICMSST']/$val['Subtotal']*100,2) ;
                    
                    
                    $result[$key]['Icms']= parent::tablefy( $data );
                    
             
                   
                }
                if(is_array($ve) and isset($ve['IPITrib']) )
                {
                    $data[0]= $ve['IPITrib'];
                    $data['theme'] = 2;
                    $theme='light';
                    //$result[$key]['vIPI']= $ve['IPITrib']['vIPI'] ;
                    $result[$key]['AlíquotaIpi']= round($ve['IPITrib']['vIPI']/$val['Subtotal']*100,2) ;
                    $result[$key]['Ipi']= parent::tablefy( $data );
                   
                }
            }

             unset($result[$key]['impostos']);
        }
        
        return $result;
        
    }


    public function action_products()
    {
        $unity  = $this->request->param('division');
        $nnfe  = $this->request->param('xtras');


        $data = self::nfe_products($nnfe);
        
        // s($data);
        // $factories = self::factories();
         $data['theme'] = 2;
         $view= parent::tablefy( $data );
        
        // s($data);
       $theme='dark';
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);
        

       

    }

    private function nfe_products($id)
    {
        // 'nNF' => string (4) "9778"
        // 'nItemPed' => string (4) "0001"
        // 'dEmi' => string (10) "2021-08-10"
        // 'cProd' => string (7) "1650182"
        // 'cEAN' => string (8) "SEM GTIN"
        // 'xProd' => string (60) "B9500-13 - ZOJE - Maq Cost Desmontada Motor e Acess-Overloqu"
        // 'NCM' => string (8) "84522929"
        // 'CEST' => string (0) ""
        // 'indEscala' => string (0) ""
        // 'CFOP' => string (4) "5102"
        // 'uCom' => string (4) "unid"
        // 'qCom' => string (1) "1"
        // 'vUnCom' => string (8) "2938.000"
        // 'vProd' => string (8) "2938.000"
        // 'cEANTrib' => string (8) "SEM GTIN"
        // 'uTrib' => string (4) "unid"
        // 'qTrib' => string (1) "1"
        // 'vUnTrib' => string (10) "2938.00000"
        // 'indTot' => string (1) "1"
        // 'xPed' => string (7) "1280135"
        // 'vFrete' => string (5) "0.000"
        // 'impostos' => string (398) "{"IPI": {"cEnq": "321", "IPINT": {"CST": "52"}}, "PIS": {"PISAliq": {"CST": "01", "vBC": "2938.00", "pPIS": "0.6500", "vPIS": "19.10"}}, "ICMS": {"ICMS20": {"CST": "20", "vBC": "2154.73", "orig": "6", "modBC": "3", "pICMS": "12.0000", "vICMS": "258.57", "pRedBC": "26.6600"}}, "COFINS": {"COFINSAliq": {"CST": "01", "vBC": "2938.00", "pCOFINS": "3.0000", "vCOFINS": "88.14"}}, "vTotTrib": "365.81"}"
        // 'vSeg' => string (5) "0.000"
        // 'vOutro' => string (5) "0.000"
        // 'DI' => string (4) "null"
        
        $sql = sprintf('SELECT n.*,cProd as _ISBN, Modelo 
                        FROM NFE.`01103171000885_NFeProdutos` n 
                        LEFT JOIN mak.inv i ON (i.id=n.cProd)
                        WHERE nNF = %s ORDER BY `nNF` DESC
                        LIMIT 550', $id);

        $result = $this->action_connect_SELECT($sql);
        
        foreach( $result as $key => $val) 
        {
            $taxes = json_decode($val['impostos'],true);
            // s($taxes);
            foreach( $taxes as $ke => $ve) 
            {
                if(is_array($ve) and key($ve) =='ICMS20')
                {
                    $data[0]= $ve['ICMS20'];
                    $data['theme'] = 3;
                    $theme='light';
                    $result[$key]['Icms']= parent::tablefy( $data );
                    unset($result[$key]['impostos']);
                }
            }

            
        }
        
        return $result;
        
    }

    public function action_index()
    {     
        $segid      = $this->request->param('division');
        $data       = self::getStockAmount($segid);
        $tot        = self::getTotalAmount($segid);
        
        $theme='light';
        $view = '';
        
        // s($data);
        if($this->request->query('data')=='json') 
        {
            $view  = json_encode($data);
        }
        
        // if($this->request->query('data')=='array') 
        // {
        //      $view  = s($data);
        // }
        
        //// Format Tot
        // $fmt['ValorTotal'] = $fmt['PesoTotal'] = '1br'; 
        
        // $tot['format'] = $fmt;
        $tot['theme'] = 3;
        $tot['caption'][] = array('link'=>'#', 'title' => 'Totais em estoque de '.$segid) ;
        $view = parent::tablefy( $tot );
        $response = parent::pagefy($view);
        
        //// Format Data
        $fmt['Sugerido'] = $fmt['Revenda'] = $fmt['Diff'] = $fmt['ValorMedioAno'] = '2us'; 
        $fmt['Fob'] = $fmt['LastFob'] = '3us';
        
        if($segid==1)
            $fmt['Fob'] = $fmt['LastFob'] = $fmt['Sugerido'] = $fmt['Revenda'] = $fmt['ValorMedioAno'] = '0br'; 
            
        $fmt['Subtotal'] = $fmt['Acumulado']  = '1kb';
        $fmt['AGE'] = '0us'; 
        $data['format'] = $fmt;
        
        // field color    
        $color['UltimaVendaDias'][0]['class'] = ' text-blue-700 text-xl font-bold ';
        $color['UltimaVendaDias'][0]['rule']  = ' < 31 ';
        $color['UltimaVendaDias'][1]['class'] = ' text-red-700 text-2xl font-bold ';
        $color['UltimaVendaDias'][1]['rule']  = ' > 30 ';
        $color['UltimaVendaDias'][2]['class'] = ' text-red-700 text-1xl font-bold ';
        $color['UltimaVendaDias'][2]['rule']  = ' UltimaVendaDias > IdadeDias ';
        
        // $color['IdadeDias']['class'] = ' text-red-500 text-1xl  font-semibold';
        // $color['IdadeDias']['rule']  = ' > 90 ';
        
        
        $color['IdadeDias'][0]['class'] = ' text-red-400 text-xl  font-bold';
        $color['IdadeDias'][0]['rule']  = ' > 90 ';
        $color['IdadeDias'][1]['class'] = ' text-red-600 text-2xl  font-semibold';
        $color['IdadeDias'][1]['rule']  = ' > 180';
        $color['IdadeDias'][2]['class'] = ' text-red-700 text-2xl  font-bold';
        $color['IdadeDias'][2]['rule']  = ' > 365 ';
        
        
        $color['Subtotal'][0]['class'] = ' text-indigo-500 text-xl  font-bold';
        $color['Subtotal'][0]['rule']  = ' > 10000 ';
        $color['Subtotal'][1]['class'] = ' text-indigo-600 text-2xl  font-bold';
        $color['Subtotal'][1]['rule']  = ' > 100000 ';
        $color['Subtotal'][2]['class'] = ' text-indigo-700 text-3xl  font-bold';
        $color['Subtotal'][2]['rule']  = ' > 1000000 ';
        
        $color['V30']['class'] = ' text-red-700 text-xl  font-bold';
        $color['V30']['rule']  = ' == 0 ';
        $color['V60']['class'] = ' text-red-700 text-2xl  font-bold';
        $color['V60']['rule']  = ' == 0 ';
        $color['V90']['class'] = ' text-red-700 text-3xl  font-bold';
        $color['V90']['rule']  = ' == 0 ';
        
        // $color['VidaEstoqueMeses']['class'] = ' text-red-500 text-3xl  font-bold';
        // $color['VidaEstoqueMeses']['rule']  = ' > 6 ';
        
        $color['VidaEstoqueMeses'][0]['class'] = ' text-red-400 text-xl  font-bold';
        $color['VidaEstoqueMeses'][0]['rule']  = ' > 6 ';
        $color['VidaEstoqueMeses'][1]['class'] = ' text-red-600 text-2xl  font-semibold';
        $color['VidaEstoqueMeses'][1]['rule']  = ' > 12';
        $color['VidaEstoqueMeses'][2]['class'] = ' text-red-700 text-3xl  font-bold';
        $color['VidaEstoqueMeses'][2]['rule']  = ' > 24 ';
        
        
        
        $color['VidaEstoque+EmTransidoMeses']['class'] = ' text-red-300 text-2xl  font-bold';
        $color['VidaEstoque+EmTransidoMeses']['rule']  = ' > 6 ';
        $color['EmTransito']['class'] = ' text-blue-700 text-xl font-semibold ';
        $color['EmTransito']['rule']  = ' > 0 ';
          // field color    
        $color['Estoque'][0]['class'] = ' text-green-700 text-xl font-bold ';
        $color['Estoque'][0]['rule']  = ' > 0 ';
        $color['Estoque'][1]['class'] = ' text-red-700 text-xl font-bold ';
        $color['Estoque'][1]['rule']  = ' Estoque < StkMin ';
        $color['Estoque'][2]['class'] = ' text-purple-700 text-xl font-bold ';
        $color['Estoque'][2]['rule']  = ' Estoque > StkMax ';
        
        $color['Revenda']['class'] = 'text-red-600 text-xl font-semibold ';
        $color['Revenda']['rule']  = ' Sugerido > Revenda ';
        
        
        $data['color'] = $color;

        
        //// Editable
          $edt['Juros'] = 'mak.inv|juros|id|_ISBN';
        $edt['Vezes'] = 'mak.inv|vezes|id|_ISBN';
        $edt['Fob'] = 'mak.inv|fob|id|_ISBN';
        $edt['Revenda'] = 'mak.inv|revenda|id|_ISBN';
        $edt['Revenda2'] = 'mak.inv|revenda2|id|_ISBN';
        $edt['Peso'] = 'mak.inv|peso|id|_ISBN';
        $edt['Volume'] = 'mak.inv|volume|id|_ISBN';
        $edt['VolumeSet'] = 'mak.inv|volumeset|id|_ISBN';
        $data['editable'] = $edt;
        
        // s( $this->pagination);
        $data['pagination'] = $this->pagination;
        $data['theme'] = 1;
        
        $baseurl='/metrics/stocks/index';
        if($segid==1)
        {
            $data['caption'][] = array('link'=>$baseurl.'/1/?brand=zoje', 'title' => 'Zoje') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?brand=sewpower', 'title' => 'SewPower') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?brand=mak prime', 'title' => 'MakPrime') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name=reta', 'title' => 'Retas') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name=Reta Eletrônica', 'title' => 'Reta Eletrônica') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name=reta com parada', 'title' => 'Reta Parada') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name=overloque', 'title' => 'Overloques') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name='.urlencode('%').'3 fios', 'title' => 'Overloques 3 fios') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name='.urlencode('%').'cadeia', 'title' => 'Cadeia') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name=interloq', 'title' => 'Interloques') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name=galon', 'title' => 'Galoneiras') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name=filigr', 'title' => 'Filigranas') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name=zig', 'title' => 'ZigZagues') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name=travet', 'title' => 'Travetes') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name=casea', 'title' => 'Caseadeiras') ;
            //  $data['caption'][] = array('link'=>$baseurl.'/1/?name=casea', 'title' => 'Casear <img src=https://img.rolemak.com.br/imagefly/h600/media/images/caseadeira.jpg?version=7.73 class="rounded-full h-36 w-36  object-cover" />') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?name=pespon', 'title' => 'Pespontadeiras') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?brand=playa', 'title' => 'Playa') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?brand=joyee', 'title' => 'Joyee') ;
            // $data['caption'][] = array('link'=>$baseurl.'/1/?brand=bsm', 'title' => 'Bsm') ;
            $data['caption'][] = array('link'=>$baseurl.'/1/?brand=pfaff', 'title' => 'Pfaff') ;
            
            $theme='light';
            $data['theme'] = 3;
        }
        
        if($segid==2)
        {
            $data['caption'][] = array('link'=>$baseurl.'/2/?brand=ppk', 'title' => 'PPK') ;
            $data['caption'][] = array('link'=>$baseurl.'/2/?brand=mak', 'title' => 'MAK') ;
            $data['caption'][] = array('link'=>$baseurl.'/2/?brand=dtb', 'title' => 'DTB') ;
            $data['caption'][] = array('link'=>$baseurl.'/2/?brand=mak automotive', 'title' => 'MAK AUTOMOTIVE') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=CS', 'title' => 'CS') ;
            $data['caption'][] = array('link'=>$baseurl.'/2/?line=600', 'title' => '6000') ;
            $data['caption'][] = array('link'=>$baseurl.'/2/?line=620', 'title' => '6200') ;
            $data['caption'][] = array('link'=>$baseurl.'/2/?line=630', 'title' => '6300') ;
            $data['caption'][] = array('link'=>$baseurl.'/2/?line=302', 'title' => '30200') ;
            $data['caption'][] = array('link'=>$baseurl.'/2/?line=322', 'title' => '32200') ;
            $data['caption'][] = array('link'=>$baseurl.'/2/?line=UC', 'title' => 'UC') ;
            $data['caption'][] = array('link'=>$baseurl.'/2/?line=UCP', 'title' => 'UCP') ;
            $data['caption'][] = array('link'=>$baseurl.'/2/?line=222', 'title' => '222XX') ;
            $data['caption'][] = array('link'=>$baseurl.'/2/?line=12', 'title' => '12XX') ;
            
            $data['caption'][] = array('link'=>$baseurl.'/2/?name=', 'title' => 'Agulhas') ;
            
            $data['theme'] = 2;
            $theme='light';
          
        }

        if($segid==3)
        {
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=zoje', 'title' => 'Zoje') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=jec', 'title' => 'JEC') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=pufei', 'title' => 'Pufei') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=messer', 'title' => 'Messer') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=kamel', 'title' => 'Kamel') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=cerliani', 'title' => 'Cerliani') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=hirose', 'title' => 'Hirose') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=tesoura', 'title' => 'Tesoura') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=lanc', 'title' => 'Lançadeiras') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=looper', 'title' => 'Loopers') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=chapa', 'title' => 'Chapas') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=dente', 'title' => 'Dentes') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=calcado', 'title' => 'Calcadores') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=maq', 'title' => 'Máquinas') ;
            
            $data['theme'] = 3;
            $theme='light';
        }

        if($segid==5)
        {
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mtr-cv', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/junta-homocinetica.svg?version=7.73" width="35" alt="Junta Homocinética" title="Junta Homocinética">Juntas') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=msu-bj', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/terminal-de-direcao.svg?version=7.73" width="35" alt="Terminal de Direção" title="Terminal de Direção">Pivôs') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=msu-bj-f2', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/terminal-de-direcao.svg?version=7.73" width="35" alt="Terminal de Direção" title="Terminal de Direção">Pivôs Ford') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-wh', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/cubo-de-roda.svg?version=7.73" width="35" alt="Cubo de Roda" title="Cubo de Roda">Cubos') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=msu-ca', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/bandeja.svg?version=7.73" width="35" alt="Bandeja" title="Bandeja">Bandejas') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=men-tk', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/kit-corrente-de-comando.svg?version=7.73" width="35" alt="Kit Corrente de Comando" title="Kit Corrente de Comando">Kit Corrente') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mpp', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/bomba-d-agua.svg?version=7.73" width="35" alt="Bomba d Agua" title="Bomba d Agua">Bombas') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=msr-ax', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/barra-axial.svg?version=7.73" width="35" alt="Barra Axial" title="Barra Axial">Barras') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mac-wb', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/palheta.svg?version=7.73" width="35" alt="Palheta" title="Palheta">Palhetas') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mcl-rd', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/radiador.svg?version=7.73" width="35" alt="Radiador" title="Radiador">Radiadores') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mtr-ds', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/semieixo.svg?version=7.73" width="35" alt="Semieixo" title="Semieixo">Semi-Eixos') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-te', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/tensor-e-polia.svg?version=7.73" width="35" alt="Tensor e Polia" title="Tensor e Polia">Tensores') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mtr-tp', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/trizeta.svg?version=7.73" width="35" alt="Trizeta" title="Trizeta">Trizetas') ;
            
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-kt', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/rolamentos.svg?version=7.73" width="35" alt="Rolamentos" title="Rolamentos">Kits de Rolamentos') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-wb', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/rolamentos.svg?version=7.73" width="35" alt="Rolamentos" title="Rolamentos">Duplo de Rodas') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-sb', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/rolamentos.svg?version=7.73" width="35" alt="Rolamentos" title="Rolamentos">Esferas') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-tr', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/rolamentos.svg?version=7.73" width="35" alt="Rolamentos" title="Rolamentos">Capa&Cones') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-ac', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/rolamentos.svg?version=7.73" width="35" alt="Rolamentos" title="Rolamentos">Ar Condicionado') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-al', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/rolamentos.svg?version=7.73" width="35" alt="Rolamentos" title="Rolamentos">Alternadores') ;
            
            $data['theme'] = 2;
            $theme='light';
        }
        
        
        $view.= parent::tablefy( $data );
        
        // s($data);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);

    }

    private function getStockAmount($segment=1)
    {
        // $order ='Sub DESC';
        $this->segment = $this->segments[$segment]['Name'];
        $order =' Subtotal DESC'; 
       
        if($this->request->query('sort')=='age') $order =' Age DESC'; 
        if($this->request->query('sort')=='last') $order =' UltimaVenda DESC'; 
        
        $this->order = $order; 
        
        // s( $this->pagination);
        $this->pagination = Pagination::factory(array(
            'total_items'    => 100,
            'items_per_page' => 10,
        ));
        
        $this->idx=1.54;
        $this->mkp=1.42;
        
        $rows=20;
        if($segment==1) {
            //$order =' V60 DESC, Subtotal DESC'; 
            $this->idx=1.34;
            $mkp=1.40;
            if($this->request->query('name')) $rows = 30;
            
            $this->pagination = Pagination::factory(array(
                'total_items'    => 500,
                'items_per_page' => $rows,
            ));
        }
        if($segment==2) {
            $this->idx=1.60;
            $mkp=1.6;
            $this->pagination = Pagination::factory(array(
                'total_items'    => 200,
                'items_per_page' => 10,
            ));
        }
        
        if($segment==3) {
            $this->idx=1.60;
            $mkp=1.6;
            $this->pagination = Pagination::factory(array(
                'total_items'    => 200,
                'items_per_page' => 20,
            ));
        }
        if($segment==5) {
            $this->idx=1.60;
            $mkp=1.60;
            if($this->request->query('line')) $rows = 20;
             
           $this->pagination = Pagination::factory(array(
                'total_items'    => 500,
                'items_per_page' => $rows,
            ));
        }
        //if($segment==3) $this->idx=30;
        
        $w="";
        if(isset($_GET['filter'])) {
            $w.=sprintf(" AND inv.modelo LIKE '%s' ", $_GET['filter'].'%' );
             $this->pagination = Pagination::factory(array(
            'total_items'    => 200,
            'items_per_page' => 5,
        ));
        }
        
        if(isset($_GET['nome'])) {
            $w.=sprintf(" AND inv.nome LIKE '%s' ", $_GET['nome'].'%' );
             $this->pagination = Pagination::factory(array(
            'total_items'    => 200,
            'items_per_page' => 5,
        ));
        }

        if(isset($_GET['catalog'])) {
            $w.=sprintf(" AND inv.edescricao LIKE '%s' ", '%'.$_GET['catalog'].'%' );
             $this->pagination = Pagination::factory(array(
            'total_items'    => 200,
            'items_per_page' => 5,
        ));
        }
        // $cache_id = 'main'.$this->segment.DBS;
        // $cache = Cache::instance('memcache');
        // $cache->delete($cache_id );
        // if ( ! $result = $cache->get($cache_id) )
        // {

        // s($this->idx,
        //                   _DOLAR,
        //                   $this->idx,
        //                   _DOLAR,
        //                   $this->idx,
        //                   _DOLAR,
        //                   $mkp,

        //                   $this->segments,
        //                   $w,
                           
        //                   $order,
        //                   $this->pagination->offset,
        //                   $this->pagination->items_per_page);
        if($this->request->query('brand')) $w.=sprintf(" AND inv.marca = '%s' ",     $this->request->query('brand'));
        if($this->request->query('line'))  $w.=sprintf(" AND inv.modelo LIKE '%s' ", $this->request->query('line').'%' ); 
        if($this->request->query('model'))  $w.=sprintf(" AND inv.modelo = '%s' ",   $this->request->query('model') ); 
        if($this->request->query('name'))  $w.=sprintf(" AND inv.nome LIKE '%s' ",   $this->request->query('name').'%' );
        
        // ,
							   
							 //  	(SELECT TO_DAYS(now()) - TO_DAYS(shipments.status)+1 as dias
								//   FROM   next 
								//   RIGHT  JOIN shipments ON shipments.id=next.shipment 
								//   WHERE  month(shipments.status)>0 and next.isbn=inv.id and next.state <>9 and next.quant >0
								//   ORDER BY dias asc 
								//   LIMIT 1) as Idade,
								//   (SELECT TO_DAYS(NOW()) - TO_DAYS(hoje.data)+1
					   //         FROM  hoje,hist 
						  //      WHERE hoje.id=hist.pedido AND  hist.isbn =inv.id  AND hoje.nop in (27,28,51)
						  //      GROUP BY hoje.data 
						  //      ORDER BY hist.id DESC 
						  //      LIMIT 1 ) UltimaVenda,
						  //       (
							 //  (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							 //  +
							 //  (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							 //  +
							 //  (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							 //  +
							 //  (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							 //  +
							 //  (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

						  // ) as Estoque
							  
        
        
        $sql = sprintf("
						SELECT
						 inv.id as _ISBN,
						        							 
							   (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

							   )
							   *fob*%s*%s AS Subtotal,
							           (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

						   ) as Estoque

						FROM 	produtos,inv	 
						WHERE   
								produtos.id=inv.idcf AND
								produtos.segmento='%s' AND
								(
								inv.modelo NOT LIKE  '%%DOL%%'  AND
								inv.modelo NOT LIKE  '%%TS%%' AND
								inv.modelo NOT LIKE  '%%WR%%' AND
								inv.modelo NOT LIKE  '%%HVP%%'  AND
								inv.modelo NOT LIKE  '%%YL%%' 
								)
								%s
					    HAVING Estoque > 0
						ORDER  BY %s 
						
						LIMIT %s,%s", 
                         
                           $this->idx,
                           _DOLAR,
                           
                           $this->segment,
                           
                           $w,
                           
                           $order,
                           $this->pagination->offset+$this->pagination->current_page-1,
                           $this->pagination->items_per_page);

            // $result = $this->action_connect_SELECT($sql);	

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();	
        
        $ids = [];
            foreach( $result as $key => $val) 
            {
                
                $ids[]= $val['_ISBN'];
                
                foreach( $val as $k => $v) 
                {
                    // if($v['Next'] < 0 or is_null($v['Next'])) 
                    // {
                    //     $result[$k]['Next'] = 'x';
                    // }else{
                    //   $result[$k]['Next'] = 'y';
                    
                    if($v < 0 or is_null($v))  $result[$key][$k] = 0;
                    if($k=='Subtotal')  $result[$key][$k] = number_format($v,2,'.','');
                    // }
                }
            //     // $resp[$v['_ISBN']]= $v;
            // //$resp[$v['id']]['STKT'] = $v['STK1']+$v['STK2']+$v['STK3']+$v['STK5']+$v['STK6'];
            //     //$resp[$v['id']]['Subtotal'] = $resp[$v['id']]['STK']["STKT"]*$v['Subtotal'];
            //     //if($vtotal>0)	$resp[$v['product_id']]['share']= ($v['Subtotaltotal']/$vtotal)*100;
            //     //	$resp[$v['id']]['Age'] = Request::factory('produto/age/index/'.$this->segment_id.'/'.$v['id'])->execute()->response;	
            //     //	$resp[$v['id']]['30'] = Request::factory('produtos/30/'.$v['id'])->execute()->response;	
            //   // $resp[$v['id']]['costs'] = Request::factory('produto/cost_calc/'.$v['id'])->execute()->response;				
            //     // $resp[$v['id']]['V30'] = $this->action_V30D($v['id']);	
            //     // $resp[$v['id']]['V90'] = $this->action_V90D($v['id']);
            //     // $resp[$v['id']]['comprar'] = ($v['V60'] / 2 * 3) - ($v['next1'] + $v['qty']);

            }


        
        if(empty($ids)) return []; //die('<div class="h-screen bg-gray-900 text-white" >Sem Registros</div>');
        
        $products = implode(',',$ids);
            //s($ids,$products);
            //arsort($resp);

            // Cache $products for seconds
        //     $cache->set($cache_id, $resp, 60*60*1);
        // }

        //		print('<pre>');
        //		print_r($resp);
        //		exit;
        
         $data  = self::getDetails($segment,$products,$result);
       

         return  $data;

        // d($view);
        // $this->response->body($view);
        

        // $view='main';
        // if(isset($_GET['view']))  $view=$_GET['view'];
        // $response =  View::factory('products/'.$view)
        //     ->set('title',$this->segment)
        //     ->set('pagination',$this->pagination )
        //     ->set('produtos', $resp)
        //     ->set('mkp', $mkp)
        //     ->set('idx', $this->idx)
        //     ;

        // $this->request->response =$response;
    }

    private function getDetails($segment=1,$products=[],$data)
    {
    
            // $this->segment = $this->segments[$segment]['Name'];
        $order = $this->order; 
        
        $this->pagination = Pagination::factory(array(
            'total_items'    => 100,
            'items_per_page' => 10,
        ));
        
        $this->idx=1.54;
        $mkp=1.42;
        
        
        if($segment==1) {
            //$order =' V60 DESC, Sub DESC'; 
            $this->idx=1.34;
            $mkp=1.40;
            $this->pagination = Pagination::factory(array(
                'total_items'    => 500,
                'items_per_page' => 10,
            ));
        }
        if($segment==2) {
            $this->idx=1.60;
            $mkp=1.6;
            $this->pagination = Pagination::factory(array(
                'total_items'    => 200,
                'items_per_page' => 10,
            ));
        }
          if($segment==5) {
            $this->idx=1.60;
            $mkp=1.60;
            $this->pagination = Pagination::factory(array(
                'total_items'    => 200,
                'items_per_page' => 10,
            ));
        }
        //if($segment==3) $this->idx=30;
        
        $w="";
        if(isset($_GET['filter'])) {
            $w.=sprintf(" AND inv.modelo LIKE '%s' ", $_GET['filter'].'%' );
             $this->pagination = Pagination::factory(array(
            'total_items'    => 200,
            'items_per_page' => 5,
        ));
        }
        
        if(isset($_GET['nome'])) {
            $w.=sprintf(" AND inv.nome LIKE '%s' ", $_GET['nome'].'%' );
             $this->pagination = Pagination::factory(array(
            'total_items'    => 200,
            'items_per_page' => 5,
        ));
        }

        if(isset($_GET['catalog'])) {
            $w.=sprintf(" AND inv.edescricao LIKE '%s' ", '%'.$_GET['catalog'].'%' );
             $this->pagination = Pagination::factory(array(
            'total_items'    => 200,
            'items_per_page' => 5,
        ));
        }
  
        $sql = sprintf("
						SELECT
						    inv.id as id,
						    inv.id as _ISBN,
						    inv.modelo as Modelo,
						    packing.packing as Embalagem,
						    inv.marca as Marca,
						    inv.juros  as Juros,
						    inv.vezes as Vezes,
						    inv.nome as _Nome,
						    inv.eDescricao as _Descricao,
					        inv.mARCA AS _Marca,
                            inv.rfm as _RFM,
                            inv.abc as _ABC,
							if(inv.Embalagem=1,'caixinha','industrial') as _Embalagem,
						
							 (SELECT  
							   	sum(hist.valor_base*hist.quant)/sum(hist.quant)
								FROM  hist,hoje	
								WHERE 	hoje.nop in (27,28,51)  AND				
						 		 		hist.pedido=hoje.id  AND
        						 		inv.id=hist.isbn AND YEAR(hoje.data)=YEAR(NOW()) 
								GROUP BY hist.isbn	 
				    	
				    		) AS _ValorMedioAno ,
							(  inv.revenda/(fob*%s*%s*%s) ) as _Diff,
							inv.peso as _Peso,
							inv.volume as _Volume,							   
							inv.volumeset as _VolumeSet,
							
							(
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

							)*fob*%s*%s AS Subtotal,
						       
						     
						     (SELECT 	SUM(hist.quant) as qProd 
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 0 AND
                        				  	hoje.nop in (27,28,51)  AND
                        					hist.isbn=inv.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                        	GROUP BY hist.isbn) AS _V1,
                        	
                        	
						     (SELECT 	SUM(hist.quant) as qProd 
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 7 AND
                        				  	hoje.nop in (27,28,51)  AND
                        					hist.isbn=inv.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                        	GROUP BY hist.isbn) AS _V7,
                        	
						     (SELECT 	SUM(hist.quant) as qProd 
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 30 AND
                        				  	hoje.nop in (27,28,51)  AND
                        					hist.isbn=inv.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                        	GROUP BY hist.isbn) AS V30,
					
				            (SELECT 	SUM(hist.quant) as qProd 
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 60 AND
                        				  	hoje.nop in (27,28,51)  AND
                        					hist.isbn=inv.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                			GROUP BY hist.isbn) AS V60,
                        			
                	        (SELECT 	SUM(hist.quant) as qProd 
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 90 AND
                        				  	hoje.nop in (27,28,51)  AND
                        					hist.isbn=inv.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                			GROUP BY hist.isbn) AS V90,
                			
                			 (SELECT 	SUM(hist.quant) as qProd 
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 365 AND
                        				  	hoje.nop in (27,28,51)  AND
                        					hist.isbn=inv.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                			GROUP BY hist.isbn) AS V365,
							  
						   (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

						   ) as Estoque,
						   
						 
						   
						  
                                
						   (SELECT   sum(n.quant) 
						        FROM next n
                                LEFT JOIN  shipments sh on (sh.id=n.shipment)	
                                LEFT JOIN  next_stage ns on (ns.id=n.stage)		  
				                WHERE   n.quant>0 AND
                                    n.state <>9 AND 
                                    MONTH( sh.status)=0 AND
                                    n.isbn = inv.id AND
                                    ns.stage='shipping'
                                GROUP BY ns.stage) as EmTransito,
                                
                            (SELECT   sum(n.quant) 
						        FROM next n
                                LEFT JOIN  shipments sh on (sh.id=n.shipment)	
                                LEFT JOIN  next_stage ns on (ns.id=n.stage)		  
				                WHERE   n.quant>0 AND
                                    n.state <>9 AND 
                                    MONTH( sh.status)=0 AND
                                    n.isbn = inv.id AND
                                    ns.stage='factory'
                                GROUP BY ns.stage) as _PrograMados,  
                            
                            (SELECT SUM(n.quant)  FROM next n ,shipments s WHERE s.id=n.shipment AND month(s.status)=0  
						    and  n.isbn =inv.id and n.state<>9 GROUP BY n.isbn) AS ComprasColocadas ,   
                                
                            (SELECT TO_DAYS(NOW()) - TO_DAYS(hoje.data)+1
					            FROM  hoje,hist 
						        WHERE hoje.id=hist.pedido AND  hist.isbn =inv.id  AND hoje.nop in (27,28,51)
						        GROUP BY hoje.data 
						        ORDER BY hist.id DESC 
						        LIMIT 1 ) UltimaVendaDias,
				    		
				    		
				    		(SELECT TO_DAYS(now()) - TO_DAYS(shipments.status)+1 as dias
								  FROM   next 
								  RIGHT  JOIN shipments ON shipments.id=next.shipment 
								  WHERE  month(shipments.status)>0 and next.isbn=inv.id and next.state <>9 and next.quant >0
								  ORDER BY dias asc 
								  LIMIT 1) as IdadeDias,
								  
								  	inv.fob AS 'Fob',
							  (SELECT  n.fob 
						        FROM next n
                                LEFT JOIN  shipments sh on (sh.id=n.shipment)	
                                LEFT JOIN  next_stage ns on (ns.id=n.stage)		  
				                WHERE   n.quant>0 AND
                                    n.state <>9 AND 
                                    MONTH( sh.status)>0 AND
                                    n.isbn = inv.id 
                                GROUP BY ns.stage 
                            ORDER BY sh.status DESC LIMIT 1) as _LastFob,
                                
							(fob*%s*%s) AS _Custo,
							(fob*%s*%s*%s) AS Sugerido,
							inv.revenda as Revenda,
							inv.revenda2 as _Revenda2,
							  inv.estoque_min AS EstoqueMínimo,
						   inv.estoque_max AS EstoqueMáximo,
						   inv.estoque_protecao AS EstoqueDeProteção,
						   inv.estoque_transito AS EstoqueDeTrânsito
							   
						  

						FROM 	inv	 

                           LEFT JOIN Catalogo.packing on (packing.id=inv.embalagem)
						WHERE   inv.id in (%s)
						ORDER  BY %s
								
 	                 
						", 
						$this->idx,
                           _DOLAR,
                           
                           $this->idx,
                           _DOLAR,
                           $mkp,
                           
                           $this->idx,
                           _DOLAR,
                           $mkp,
                           
                           $this->idx,
                           _DOLAR,
                           
                           
                             $products, 
                           $order
                          
                        );

            // $result = $this->action_connect_SELECT($sql);	

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();	
        
            foreach( $result as $key => $val) 
            {
                 if(isset($val['Revenda2']) and $segment==5 ) $result[$key]['MercadoLivre'] = $val['Revenda2'] ;
                 $result[$key]['VidaEstoqueMeses'] = $result[$key]['VidaEstoque+EmTransidoMeses'] = 0;
                 if($val['V30']>0) $result[$key]['VidaEstoque+EmTransidoMeses']    = round((($val['Estoque']+$val['EmTransito'])/$val['V30'])*1,1);
                 if($val['V30']>0) $result[$key]['VidaEstoqueMeses'] = round(($val['Estoque']/$val['V30'])*1,1);
                 
                 $result[$key]['StkMin']  = round(max($val['V30'],$val['V60']/2,$val['V90']/3,$val['V365']/12),0) ;
                 $result[$key]['StkMax']  = $result[$key]['StkMin'] *4;
                 
                foreach( $val as $k => $v) 
                {
                    if($v < 0 or is_null($v))  $result[$key][$k] = 0;
                    
                    $result[$key][$k] = $result[$key][$k];
                }
                
                // 	inv.revenda2 as MercadoLivre,
                
            //	$resp[$v['id']]['Age'] = Request::factory('produto/age/index/'.$this->segment_id.'/'.$v['id'])->execute()->response;	
            //  $resp[$v['id']]['costs'] = Request::factory('produto/cost_calc/'.$v['id'])->execute()->response;				

            }

        return  $result;

        d($view);
    }

    public function action_nextStages($productId=1649975)
    {
        $sql = sprintf(        
                "SELECT
                        ns.stage            as stage,
                        sum(n.quant)             as Qty,
                        n.fob               as Price

                 FROM 	produtos p,next n
                 LEFT JOIN  shipments sh on (sh.id=n.shipment)	
                 LEFT JOIN  next_stage ns on (ns.id=n.stage)		  
                 LEFT JOIN  inv i ON (i.id=n.isbn)
				 WHERE  p.id=i.idcf AND
                        n.quant>0 AND
                        n.state <>9 AND 
                        MONTH( sh.status)=0 AND
                        i.id = %s
                 GROUP BY stage
                 ORDER BY stage
                 LIMIT 15
            ", $productId);
            
        $result  =  $this->action_connect_SELECT($sql);
        s($result);
}
   
    public function action_next_all($productId=1649975)
    {
        $sql = sprintf(        
                "SELECT
                        n.data              as Date_Request,
                        n.timestamp         as Timestamp,
                        UCASE(p.segmento)   as Division,
                        n.excel             as File,
                        n.excel             as QuotationID,
                        n.order_number      as OrderID,
                        UCASE(s.brand)      as SupplierName,
                        s.id                as SupplierId,
                        COUNT(n.id)         as Itens,
                        ns.stage            as stage,
                        ns.dsc_chinese      as dsc_chinese,
                        ns.color            as color,
                        n.stage             as stageID,
                        sh.invoice          as Invoice,
                        n.id                as ID,
                        i.id                as ISBN,
                        i.revenda           as Revenda,
                        n.shipment          as SHID,
                        i.modelo            as Model,
                        i.nome              as Name,
                        UCASE(n.brand)      as Brand,
                        i.peso              as Weight,
                        n.quant             as Qty,
                        n.fob               as Price,
                        n.myfob             as Target,
                        n.qualidade         as Quality,
                        n.blindagem         as Shield,
                        pk.description      as Packing,
                        i.codebar           as Barcode,
                        n.obs               as Requirement,
                        n.etd               as ETD,
                        pq.quality          as ps_quality,
                        pq.description      as ps_name,
                        ps.id               as PSID,
                        (SELECT nick FROM `users` u WHERE  u.id=n.user) as user
                        
                 FROM 	produtos p,next n
                 LEFT JOIN  shipments sh on (sh.id=n.shipment)	
			     LEFT JOIN  supplier s on (s.id=n.supplier)		  
                 LEFT JOIN  next_stage ns on (ns.id=n.stage)		  
                 LEFT JOIN  inv i ON (i.id=n.isbn)
                 LEFT JOIN  packing pk ON (pk.id=i.embalagem)
                 LEFT JOIN  products_specifications ps ON (ps.id=n.isbn)
                 LEFT JOIN  product_quality pq ON (pq.id=ps.quality_id)

				 WHERE  p.id=i.idcf AND
                        n.quant>0 AND
                        n.state <>9 AND 
                        MONTH( sh.status)=0 AND
                        i.id = %s
                          
                 GROUP BY n.id
                 ORDER BY Model, Requirement asc
                 LIMIT 1
            ", $productId);
            
        $result  =  $this->action_connect_SELECT($sql);
        s($result);
}
   
    private function getTotalAmount($segment=1)
    {
        $sql = sprintf("
						SELECT
						 
						        							 
						SUM(	   (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

							   )
							   *fob*%s*%s
						) AS ValorTotal,
						SUM(	   (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

							   )
							   *peso
						) AS PesoTotal,
							SUM(	   (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

							   )
							   *volume
						) AS VolumeTotal
						
							  

						FROM 	produtos,inv	 
						WHERE   
								produtos.id=inv.idcf AND
								produtos.segmento='%s' AND
								(
								inv.modelo NOT LIKE  '%%DOL%%'  AND
								inv.modelo NOT LIKE  '%%TS%%' AND
								inv.modelo NOT LIKE  '%%WR%%' AND
								inv.modelo NOT LIKE  '%%HVP%%'  AND
								inv.modelo NOT LIKE  '%%YL%%' 
								)
								
							
					   ", 
                         
                           $this->idx,
                           _DOLAR,
                           $this->segment
                           
                           
                          );
                           
                $query = DB::query(Database::SELECT, $sql);
                $result = $query->execute()->as_array();	
                
            foreach( $result as $key => $val) 
            {
                
                if($this->segment=='machines') $result[$key]['Containers'] =  round($val['VolumeTotal']/56,2);
                if($this->segment=='bearings') $result[$key]['Containers'] = round($val['PesoTotal']/25000,2);
                if($this->segment=='parts') $result[$key]['Containers'] =  round($val['VolumeTotal']/56,2);
                if($this->segment=='faucets') $result[$key]['Containers'] = round($val['PesoTotal']/15000,2);
                if($this->segment=='auto') $result[$key]['Containers'] = round($val['PesoTotal']/25000,2);
                
                
                
                foreach( $val as $k => $v) 
                {
                    if($v < 0 or is_null($v))  $result[$key][$k] = 0;
                    $result[$key][$k] = number_format($v,2,'.',',');
                    

                }
            }
                
                return $result;
                           
    }


    public function action_moto()
    {
        define('AUTH', base64_encode('Rolteam:test'));
        
        
        // AND catalogoTipoMaquina:["" TO *]
        $cats[5] = '%20AND%20produtoModelo:63*';
        $cats[6] = '%20AND%20produtoModelo:62*';
        $cats[7] = '%20AND%20produtoModelo:60*';
        $cats[8] = '%20AND%20produtoModelo:K*';
        $cats[9] = '%20AND%20produtoModelo:HK*';
        $cats[10] = '%20AND%20produtoModelo:F*';
        $cats[11] = '%20AND%20produtoModelo:68*';
        $cats[12] = '%20AND%20produtoModelo:69*';
        $cats[13] = '%20AND%20produtoModelo:32*';
        $cats[14] = '%20AND%20 ( produtoPOID:15015%20OR%20produtoPOID:30039 ) ';

        $cats[1] = '%20AND%20categoriaNome:%20"Rolamentos%20para%20Moto"';
        $cats[2] = '%20AND%20categoriaNome:%20"Caixa%20de%20Direção"';
        $cats[3] = '%20AND%20categoriaNome:%20"Pedal%20de%20Partida"';
        $cats[4] = '%20AND%20categoriaNome:%20"Pedal%20de%20Câmbio"';
        
        // if($this->request->query('brand')) $w.=sprintf(" AND inv.marca = '%s' ",     $this->request->query('brand'));
        // if($this->request->query('line'))  $w.=sprintf(" AND inv.modelo LIKE '%s' ", $this->request->query('line').'%' ); 
        // if($this->request->query('name'))  $w.=sprintf(" AND inv.nome LIKE '%s' ",   $this->request->query('name').'%' ); 
        
        
        
        if($this->request->param('division') and $this->request->param('division')>0 and $this->request->param('division')<20)
        {
            $cat[] = $cats[$this->request->param('division')];
        }else{
            $cat = $cats;   
        }
        
        // s($cat);
        
        
        $view='';
        $theme = 3;
        foreach($cat as $k => $v)
        {
            $view.=self::scan($v,$theme);
            $theme++;
            if($theme>3) $theme=1;
            
        }
        
        $response = parent::pagefy($view );
        // s($_SERVER);
        
       
        $this->response->body($response);

        
    }

    public function scan($cat,$theme=1)
    {
      
        $json = self::solr_moto($cat);
        
        $arr = json_decode($json,true);
        
        $group = $counter=0;
        foreach($arr['response'][ 'docs'] as $k => $v)
        {
            $ids[] = $v['produtoPOID'];
        }
        
        $segid = 6;
        $products = implode(',',$ids);
        $this->order = ' Subtotal DESC '; 
        $details = self::getDetails(6,$products,[]);
        
         foreach($details as $k => $v)
        {
            if($k>50) continue;
            $data[$k] = $v;
            
        }
        
        //// Format Data
        $fmt['Sugerido'] = $fmt['Revenda'] = $fmt['Diff'] = $fmt['ValorMedioAno'] = '2us'; 
        $fmt['Fob'] = $fmt['LastFob'] = '3us';
        
        if($segid==1)
            $fmt['Fob'] = $fmt['LastFob'] = $fmt['Sugerido'] = $fmt['Revenda'] = $fmt['ValorMedioAno'] = '0br'; 
            
        $fmt['Subtotal'] = $fmt['Acumulado']  = '1kb';
        $fmt['AGE'] = '0us'; 
        
        $data['format'] = $fmt;
        
        //// Editable
        					
        $edt['Juros'] = 'mak.inv|juros|id|_ISBN';
        $edt['Vezes'] = 'mak.inv|vezes|id|_ISBN';
        $edt['EstoqueMínimo'] = 'mak.inv|estoque_min|id|_ISBN';
        $edt['EstoqueMáximo'] = 'mak.inv|estoque_max|id|_ISBN';
        $edt['EstoqueDeProteção'] = 'mak.inv|estoque_protecao|id|_ISBN';
        $edt['EstoqueDeTrânsito'] = 'mak.inv|estoque_transito|id|_ISBN';
        $data['editable'] = $edt;
        
        // s( $this->pagination);
        $data['pagination'] = $this->pagination;
        $data['theme'] = $theme;
        
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/2', 'title' => 'Caixas de Direção') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/3', 'title' => 'Pedal de Partida') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/4', 'title' => 'Pedal de Câmbio') ;
        // $data['caption'][] = array('link'=>'/metrics/stocks/moto/1', 'title' => 'Rolamentos') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/5', 'title' => '6300') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/6', 'title' => '6200') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/7', 'title' => '6000') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/11', 'title' => '6800') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/12', 'title' => '6900') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/13', 'title' => '32000') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/8', 'title' => 'K') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/9', 'title' => 'HK') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/10', 'title' => 'F') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/14', 'title' => 'Virabrequim') ;
        
      
        $color['UltimaVendaDias']['class'] = ' text-red-700 text-2xl font-bold ';
        $color['UltimaVendaDias']['rule']  = ' > 30 ';
        $color['IdadeDias']['class'] = ' text-red-500 text-1xl  font-semibold';
        $color['IdadeDias']['rule']  = ' > 90 ';
        
        
        // $color['Subtotal'][0]['class'] = ' text-green-400 text-1xl  font-bold';
        // $color['Subtotal'][0]['rule']  = ' > 10000 ';

        // $color['Subtotal'][1]['class'] = ' text-green-600 text-2xl  font-bold';
        // $color['Subtotal'][1]['rule']  = ' > 100000 ';

        // $color['Subtotal'][2]['class'] = ' text-green-900 text-3xl  font-bold';
        // $color['Subtotal'][2]['rule']  = ' > 1000000 ';
        
        
        $color['V30']['class'] = ' text-red-700 text-1xl  font-bold';
        $color['V30']['rule']  = ' == 0 ';
        $color['V60']['class'] = ' text-red-700 text-2xl  font-bold';
        $color['V60']['rule']  = ' == 0 ';
        $color['V90']['class'] = ' text-red-700 text-3xl  font-bold';
        $color['V90']['rule']  = ' == 0 ';
        
         // field color    
        $color['Estoque'][0]['class'] = ' text-green-700 text-xl font-bold ';
        $color['Estoque'][0]['rule']  = ' > 0 ';
        $color['Estoque'][1]['class'] = ' text-red-700 text-xl font-bold ';
        $color['Estoque'][1]['rule']  = ' Estoque < StkMin ';
        $color['Estoque'][2]['class'] = ' text-purple-700 text-xl font-bold ';
        $color['Estoque'][2]['rule']  = ' Estoque > StkMax ';
       
        
        
        $data['color'] = $color;
    
        $view= parent::tablefy( $data );
        
        return $view;
        // s($data);
        // exit;
        
    }
    public function solr_moto($cat)
    {
        
        
        
        $url = ('https://solr.rolemak.com.br/solr/catalogo/select?indent=on&q=*:*&wt=json&fq=segmentoPOID:6%20AND%20produtoAtivo:1'.$cat .'&fl=produtoPOID&start=0&rows=300' );
        $moto = Request::factory( $url )
                ->headers('Authorization', 'Basic '.AUTH)
                ->method('GET')
                ->execute()->body();
                
        return $moto;
    }
    
       
    public  function action_jeditable()
    {
        $w="";
        
        $date = date("Y-m-d");
        $date1 = str_replace('-', '/', $date);
         $day = date('Y-m-d',strtotime($date1 . "-1 days"));
        
        if($this->request->query('user')) $w.=sprintf(" AND j.UserId='%s' ", $this->request->query('user') );
        if($this->request->query('day')) $w.=sprintf(" AND Datetime LIKE '%s' ", $day.'%' );
        
        $sql = sprintf("SELECT `j`.`id`,   
                                    i.id as _ISBN  , 
                                    i.modelo as Modelo, 
                                    i.marca as Marca,
                                    RecordName as Field,
                                    u.nick as 'By', 
                                    Datetime as 'On',
                                    PreviousContent as 'From' , 
                                    NewContent as 'To'
                                    
                            FROM history.`jeditable` j 
                            LEFT JOIN mak.inv i ON (i.id=j.`PrimaryRecordId`) 
                            LEFT JOIN mak.users u ON (u.id=j.UserId) 
                            
                        WHERE (`TableName` = 'inv' or `TableName` = 'mak.inv' )  AND i.id=%s %s
                        ORDER BY j.id DESC 
                        LIMIT 50",$this->request->param('division'),$w);

        $result = $this->action_connect_SELECT($sql);
        
          
        
        
        //// Editable
        
        
        $data = $result; 
        
        $edt['Peso'] = 'mak.inv|peso|id|_ISBN';
        $edt['Volume'] = 'mak.inv|volume|id|_ISBN';
        $edt['VolumeSet'] = 'mak.inv|volumeset|id|_ISBN';
        $data['editable'] = $edt;
        
        $theme='light';
        $data['theme'] = 2;
        
            
        $view= parent::tablefy( $data );
        
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);


        // s($result);
    }
    
    public function action_margem(  )
    {
        $get  = $this->request->query();
      
        $get['unity']=1;
        $resp[$get['unity']] = self::margem($get);
        
        $get['unity']=3;
        $resp[$get['unity']] = self::margem($get);
        
        $get['unity']=6;
        $resp[$get['unity']] = self::margem($get);
        
        $get['unity']=8;
        $resp[$get['unity']] = self::margem($get);
        
  
            
        foreach($resp as $key => $dre)
        {
            foreach($dre as $k => $v)
            {
                if(!isset($res[$k])) $res[$k]=0;
                $res[$k] += $v;
            }
        }
        // $get['unity']=3;
        // $resp[$get['unity']] = self::margem($get);
        
        echo '<img src="https://media.treasy.com.br/media/2013/07/dre-demonstra%C3%A7%C3%A3o-de-resultados-do-exerc%C3%ADcio.png" width=500px />';
        
        echo '<br><img src="https://facil123.com.br/wp-content/uploads/dre-01-visao-geral-v3.png" width=500px />';
        
        // s($resp,$res);
        // array_merge()
        
        
        foreach($resp as $key => $val) 
        {
            foreach($val as $k => $v) 
            {
                $arr[$k][$key] =$v   ;
            } 
          
         }
          
        
        // s($arr);
        
        
        $response = View::factory('resultado')
                ->set('data', $arr);
                // ->set('lines', $this->tt);
         $this->response->body($response);
    
       
    
      
            
    }
    
    public function margem( $get )
    {
        $where    = null;
        $day      = null;
        $product  = null;
        $division = null;
        $segment  = null;
        $source   = null;
        $array    = array();
        
         if (isset($get['product']))
            $product = $get['product'];
            
        if (isset($get['division']))
            $division = $get['division'];
            
        if (isset($get['segment']))
            $segment = $get['segment'];
            
        if (isset($get['source']))
            $source = $get['source'];
          
        if (isset($get['day']))
            $day = $get['day'];
        

    
        $month   = $get['month'];
        $year    = $get['year'];
        $unity   = $get['unity'];
        
         $costs = self::costs($unity,$month);
         
        //  s($costs);
        
        if ( $year )
        {
            $where.= sprintf(" AND year(nfe_prod.dEmi) = %s ", $year);
        }

        if ( $month )
        {
            $where.= sprintf(" AND month(nfe_prod.dEmi) = %s ", $month);
        }

        if ( $day )
        {
            $where.= sprintf(" AND day(nfe_prod.dEmi) = %s ", $day);
        }
        
        if ( $unity )
        {
            $where.= sprintf(" AND hoje.UnidadeLogistica = %s ", $unity);
        }
        
        if ( $division )
        {
            $where.= sprintf(" AND p.segmento_id  = %s ", $division);
        }
        
        if ( $segment )
        {
            $where.= sprintf(" AND p.segmento_id = %s ", $segment);
        }
        
        if ( $source )
        {
            if( 1 == $source )
            {
                $where.= sprintf(" AND hoje.source in ( %s ) ", "3,4,5,6,7,8" );
            }
            
            if( 2 == $source )
            {
                $where.= sprintf(" AND hoje.source in ( %s ) ", "0,1" );
            }
        }
        
        
        //Define Tables by Unity
        if ( 1 == $unity )
        {
           $table = '01103171000109';
        }

        if ( 8 == $unity )
        {
           $table = '01103171000885';
        }

        if ( 3 == $unity )
        {
           $table = '01103171000370';
        }

        if ( 6 == $unity )
        {
           $table = '01103171000613';
        }
        
        
        $dolar=5.2;
        $idx = 1.45*1.07*$dolar;
        if ( 1 == $division )
        {
           $idx = 1.2*1.07*$dolar;
        }

        
        $sql= sprintf("SELECT clientes.nome,inv.modelo,cProd,
                        ROUND( (nfe_prod.impostos->>'$.vTotTrib') , 2) as vTotTrib,
                        ROUND( (nfe_prod.vProd) , 2) as vProd,
                        ROUND( (nfe_prod.qTrib), 2) as qTrib,
                        (nfe_prod.qTrib) as qTrib,
                        inv.fob, 
                        (nfe_prod.qTrib)*(inv.fob*%s) as CustoProduto,
                        (nfe_prod.vProd) as vProd,
                        1- (( (nfe_prod.qTrib)*(inv.fob*%s))/(nfe_prod.vProd)) as vMargem,
                        nfe_prod.nNF,
                        nfe_prod.dEmi, 
                        nfe.infNFe, 
                        nfe.StatusNfe, 
                        nfe.TipoNFe,
                        
                       
                        
                        nfe_prod.impostos->>'$.IPI.IPITrib.pIPI' as pIPI,
                        ROUND( SUM(nfe_prod.impostos->>'$.IPI.IPITrib.vIPI') , 2)  as vIPI, 
                        nfe_prod.impostos->>'$.PIS.PISAliq.pPIS' as pPIS, 
                        ROUND( SUM( nfe_prod.impostos->>'$.PIS.PISAliq.vPIS') , 2) as vPIS,
                        nfe_prod.impostos->>'$.COFINS.COFINSAliq.pCOFINS' as pCOFINS, 
                        ROUND( SUM( nfe_prod.impostos->>'$.COFINS.COFINSAliq.vCOFINS') , 2) as vCOFINS,
                        SUM( nfe_prod.impostos->>'$.ICMS.ICMS00.vICMS' ) as vICMS00, 
                        SUM( nfe_prod.impostos->>'$.ICMS.ICMS10.vICMS') as vICMS10, 
                        SUM( nfe_prod.impostos->>'$.ICMS.ICMS20.vICMS') as vICMS20, 
                        SUM( nfe_prod.impostos->>'$.ICMS.ICMS30.vICMS') as vICMS30, 
                        SUM( nfe_prod.impostos->>'$.ICMS.ICMS40.vICMS') as vICMS40, 
                        SUM( nfe_prod.impostos->>'$.ICMS.ICMS10.vICMSST') as vICMSST10, 
                        SUM( nfe_prod.impostos->>'$.ICMS.ICMS20.vICMSST') as vICMSST20, 
                        SUM( nfe_prod.impostos->>'$.ICMS.ICMS30.vICMSST') as vICMSST30, 
                        SUM( nfe_prod.impostos->>'$.ICMS.ICMS40.vICMSST') as vICMSST40, 
                        nfe_prod.impostos->>'$.ICMST' as ICMSST,
                        Emitentes.Fantasia, Emitentes.Abrev, novo_segmento.segmento as clienteDivisao,
                        nop.nop_nfe as naturezaOperacaoNotaFiscaL, nop.MovimentaEstoque, nop.tipo,
                        hoje.id as pedidoID, hoje.nop as pedidoNOP, p.segmento as produtoSegmento, p.segmento_id as produtoSegmentoID
    		            
    		            FROM NFE.%s_NFeProdutos as nfe_prod
    		            LEFT JOIN inv on (nfe_prod.cProd=inv.id)
    		            LEFT JOIN produtos as p on (p.id=inv.idcf)
    		            LEFT JOIN hoje on (hoje.id=nfe_prod.xPed)
    		            LEFT JOIN Emitentes on (hoje.UnidadeLogistica=Emitentes.EmitentePOID)
    		            LEFT JOIN clientes on (hoje.idcli=clientes.id)
    		            LEFT JOIN novo_segmento on (novo_segmento.id=clientes.novo_segmento)
    		            LEFT JOIN NFE.%s_NFe as nfe on ( nfe.NFePOID=nfe_prod.nNF )
    		            LEFT JOIN nop on (nop.id_nop=hoje.nop)
    		            
    		            WHERE 
    		            ( nop.nop_nfe='Venda'  %s )
    		            
    		            GROUP BY nfe_prod.nNF, nfe_prod.nItemPed
    		            
    		            LIMIT 50000", $idx, $idx, $table, $table, $where);
        //s($sql);
        // nop.nop_nfe='Venda'
        //die();

        $query = DB::query(Database::SELECT, $sql);
        $resp = $response = $query->execute()->as_array();
       
         d($response);
        //die();
        
        $total                   = array();
        $total['vIPI']           = 0;
        $total['vICMS']          = 0;
        $total['vICMSST']        = 0;
        $total['vCOFINS']        = 0;
        $total['vPIS']           = 0;
        $total['vProd']          = 0;
        $total['vProdBruto']     = 0;
        $total['vProdLiquido']   = 0;
        $total['vProdCSLL']      = 0;
        $total['vProdIRPJ']      = 0;
        $total['vMarketplace']   = 0;
        $total['vCustos']        = 0;
        $total['vFrete']         = 0;
        $total['vMargemLiquido'] = 0;
         $total['COGS']  = 0;

        $count                  = 0;
        
        if ( count($response) > 0 )
        {
            $i   = 0;
            $len = count($response);
           
            $check = 0;
            
            foreach ( $response as $k => $v )
            {
                $response[$k]['nfCount'] = $count + 1;
                
                $vICMS   = $v['vICMS00']   + $v['vICMS10']   + $v['vICMS20']   + $v['vICMS30']  + $v['vICMS40'];
                $vICMSST = $v['vICMSST10'] + $v['vICMSST20'] + $v['vICMSST30'] + $v['vICMSST40'];
                
                $response[$k]['vICMS']   = $vICMS;
                $response[$k]['vICMSST'] = $vICMSST;
                
                /////////////////////////////////////////////////////////////////////////
                ////////// Total de Vendas 
                ///////// 27 Venda - Pedidos Venda
                ///////// 66 Venda - Pedidos Consolidados
                /////////////////////////////////////////////////////////////////////////
                if ( 27 == $v['pedidoNOP'] or 66 == $v['pedidoNOP'] )
                {
                    $total['vIPI']    += $v['vIPI'];
                    $total['vICMS']   += $vICMS;
                    $total['vICMSST'] += $vICMSST;
                    $total['vCOFINS'] += $v['vCOFINS'];
                    $total['vPIS']    += $v['vPIS'];
                    $total['vProd']   += $v['vProd'];
                    $total['COGS']   += $v['CustoProduto'];
                    
                    if($v['CustoProduto'] == 0) s($v);
                    
                }
                
                //total
                $response[$k]['vTotalBruto'] = $total['vProd'] + $total['vIPI'];
                
                /////////////////////////////////////////////////////////////////////////
                ////////// Checar a sequência de números na emissão da NF-e
                /////////////////////////////////////////////////////////////////////////
                if ( $i == 0 )
                {
                    $check = $v['nNF'];
                }else{
                    $check++;
                }
                
                //s($i, $check , $v['nNF'] );
                //////////////////////////////////////////////////////////////////////////////////////
                ////////// Cria uma classe para mostrar na tela a quebra da sequência
                //////////////////////////////////////////////////////////////////////////////////////
                if ( $check == $v['nNF'] )
                {
                    $response[$k]['nfClass'] = 'font-weight-bolder';
                }else{
                    $response[$k]['nfClass'] = 'font-weight-bolder';
                }
               
                $i++;
                $count++;
            }
        }
        
        // d($total);
        
        $total['count']      = $count;
        $total['vTotal']     = $total['vIPI'] + $total['vProd'];
        
        //Calculo IRPJ 15% mais 10% acima 20 mil
        $irpj    = 0;
        $irpjadd = 0;
        $irpj = $total['vTotal'] * ( 8 / 100 );
        
        if ( $irpj > 10000 )
        {
            $irpjadd = ($irpj - 20000 ) * ( 10 / 100 ) ;  
            $irpjadd = round( $irpjadd * ( 10 / 100 ) , 0);  
        }
        
        $irpj = $irpj * ( 15 / 100 );
        $total['vProdIRPJ'] = $irpj + $irpjadd;
        $total['aProdIRPJ'] = 8;
        
        //Calculo CSLL produto * 9% 
        $csll = 0;
        $csll = $total['vTotal'] * ( 12 / 100 );
        $csll = round( $csll * ( 9 / 100 ) , 0);
        $total['vProdCSLL'] = $csll;
        $total['aProdCSLL'] = 12;
        
        //Total produto
        $total['vProdBruto']   = $total['vProd'] + ( $total['vIPI'] + $total['vICMSST'] );
        $total['vImpostosSobreVendas'] = ( $total['vICMS'] +  $total['vPIS'] +  $total['vCOFINS']   );
        $total['vProdLiquido'] = $total['vProd'] - $total['vImpostosSobreVendas'];
        $total['vMargemLiquida'] = $total['vProdLiquido'] - $total['COGS'];
        $total['aImpostosSobreVendas']=$total['aMargemLiquida'] =0;
        if($total['vProd']>0) 
        {
            $total['aImpostosSobreVendas'] =  ($total['vImpostosSobreVendas']/$total['vProd'])*100 ;
            $total['aMargemLiquida'] = ($total['vMargemLiquida']/$total['vProd'])*100;
        }
        
        
        $dre['+ReceitadeVendas'] = $total['vProd'] ;
        $dre['-DeduçõesEImpostos'] = $total['vImpostosSobreVendas'] ;
        $dre['=ReceitaLíquida'] = $dre['+ReceitadeVendas'] - $dre['-DeduçõesEImpostos'] ;
        
        $dre['-CMV_CustoDeMercadioriasVendida'] =  $total['COGS'];
        $dre['=MargemBruta'] = $dre['=ReceitaLíquida'] -$dre['-CMV_CustoDeMercadioriasVendida'];
        
        // s($unity,$division);
        $dre['-DespesasVariáveis'] = $costs[$unity][$division]['-DespesasVariáveis'];
        
        $dre['=MargemDeContribuição'] = $dre['=MargemBruta'] - $dre['-DespesasVariáveis'];
        
        $dre['-GastosComPessoal'] = $costs[$unity][$division]['-GastosComPessoal'];
        $dre['-DespesasOperacionais'] =  $costs[$unity][$division]['-DespesasOperacionais'];
        
        $dre['=EBITDA'] =  $dre['=MargemDeContribuição'] - $dre['-GastosComPessoal'] - $dre['-DespesasOperacionais'] ;
        
        
        $dre['-DepreciaçãoAmortizaçãoOuExaustão'] = 0;
        $dre['-OutrasReceitasEDespesas'] = 0;
        $dre['=ResultadosOperacionalAntesDoIRPJeCSLL'] = $dre['=EBITDA'] - $dre['-DepreciaçãoAmortizaçãoOuExaustão'] -  $dre['-OutrasReceitasEDespesas'] ;
        
        
        $dre['-TributosIRPJeCSLL'] = ( $total['vProdIRPJ'] + $total['vProdCSLL']  );
        $dre['=ResultadoLíquido'] = $dre['=ResultadosOperacionalAntesDoIRPJeCSLL'] -$dre['-TributosIRPJeCSLL'];
        
        
        $total['=Productivity']=0;
        if($dre['-GastosComPessoal'] > 0) $total['=Productivity'] = ($dre['=MargemBruta']/$dre['-GastosComPessoal']);
        
        
        //Tarifa de venda Marketplace
        //R$ 13.835,79
        //Tarifa de envio extra ou intermunicipal
        //R$ 9.921,30
        //Campanhas de publicidade - Product Ads
        //R$ 3.478,38
        //Tarifa do Mercado Envios
        //R$ 300,16
        //Estornadas neste período
        //- R$ 1.471,94
        //Marketplace
        if ( $source )
        {
             $total['vMarketplace'] = $total['vTotal'] * ( 17 / 100 );
             $total['aMarketplace'] = 17;
             $total['vCustos']      = $total['vTotal'] * ( 4 / 100 ) + $total['vTotal'] * ( 12 / 100 );
             $total['aCustos']      = 16;
        }
        
        $total['vMargemLiquido'] = $total['vProdLiquido'] - ( $total['vMarketplace'] + $total['vCustos'] )  ;
  
        //total de Linha
        $array['rows']   = $response;
        $array['total']  = $this->formatPrice($total);
        
        //s($response);
        //die();
        
        if ( isset($get['print']) )
        {
           s($array); 
        }
        
        
        // $view= parent::tablefy( $resp );
        // $theme='light';
        // $response = parent::pagefy($view,$theme);
        // $this->response->body($response);
         return $dre; //$array['total'];
        // $this->response->body( json_encode( $array['total'] ));
    }
    
    private function costs()
    {
        $arr[1][1]['-DespesasVariáveis']    = 0 ;
        $arr[1][2]['-DespesasVariáveis']    = 30000 ;
        $arr[1][3]['-DespesasVariáveis']    = 0 ;
        $arr[1][4]['-DespesasVariáveis']    = 0 ;
        $arr[1][5]['-DespesasVariáveis']    = 0 ;

        $arr[3][1]['-DespesasVariáveis']    = 18000 ;
        $arr[3][2]['-DespesasVariáveis']    = 1 ;
        $arr[3][3]['-DespesasVariáveis']    = 1 ;
        $arr[3][4]['-DespesasVariáveis']    = 1 ;
        $arr[3][5]['-DespesasVariáveis']    = 1 ;

        $arr[6][1]['-DespesasVariáveis']    = 5000 ;
        $arr[6][2]['-DespesasVariáveis']    = 0 ;
        $arr[6][3]['-DespesasVariáveis']    = 0 ;
        $arr[6][4]['-DespesasVariáveis']    = 0 ;
        $arr[6][5]['-DespesasVariáveis']    = 0 ;

        $arr[8][1]['-DespesasVariáveis']    = 7000 ;
        $arr[8][2]['-DespesasVariáveis']    = 0 ;
        $arr[8][3]['-DespesasVariáveis']    = 0 ;
        $arr[8][4]['-DespesasVariáveis']    = 0 ;
        $arr[8][5]['-DespesasVariáveis']    = 0 ;



        $arr[1][1]['-GastosComPessoal']    = 10000 ;
        $arr[1][2]['-GastosComPessoal']    = 212000 ;
        $arr[1][3]['-GastosComPessoal']    = 5000 ;
        $arr[1][4]['-GastosComPessoal']    = 0 ;
        $arr[1][5]['-GastosComPessoal']    = 0 ;

        $arr[3][1]['-GastosComPessoal']    = 130000 ;
        $arr[3][2]['-GastosComPessoal']    = 0 ;
        $arr[3][3]['-GastosComPessoal']    = 15000 ;
        $arr[3][4]['-GastosComPessoal']    = 0 ;
        $arr[3][5]['-GastosComPessoal']    = 0 ;

        $arr[6][1]['-GastosComPessoal']    = 25000 ;
        $arr[6][2]['-GastosComPessoal']    = 0 ;
        $arr[6][3]['-GastosComPessoal']    = 0 ;
        $arr[6][4]['-GastosComPessoal']    = 0 ;
        $arr[6][5]['-GastosComPessoal']    = 5000 ;

        $arr[8][1]['-GastosComPessoal']    = 125000 ;
        $arr[8][2]['-GastosComPessoal']    = 0 ;
        $arr[8][3]['-GastosComPessoal']    = 0 ;
        $arr[8][4]['-GastosComPessoal']    = 0 ;
        $arr[8][5]['-GastosComPessoal']    = 40000 ;
        
      

        $arr[1][1]['-DespesasOperacionais']    = 0 ;
        $arr[1][2]['-DespesasOperacionais']    = 72000 ;
        $arr[1][3]['-DespesasOperacionais']    = 0 ;
        $arr[1][4]['-DespesasOperacionais']    = 0 ;
        $arr[1][5]['-DespesasOperacionais']    = 0 ;

        $arr[3][1]['-DespesasOperacionais']    = 50000 ;
        $arr[3][2]['-DespesasOperacionais']    = 0 ;
        $arr[3][3]['-DespesasOperacionais']    = 0 ;
        $arr[3][4]['-DespesasOperacionais']    = 0 ;
        $arr[3][5]['-DespesasOperacionais']    = 0 ;

        $arr[6][1]['-DespesasOperacionais']    = 30000 ;
        $arr[6][2]['-DespesasOperacionais']    = 0 ;
        $arr[6][3]['-DespesasOperacionais']    = 0 ;
        $arr[6][4]['-DespesasOperacionais']    = 0 ;
        $arr[6][5]['-DespesasOperacionais']    = 0 ;

        $arr[8][1]['-DespesasOperacionais']    = 10000 ;
        $arr[8][2]['-DespesasOperacionais']    = 0 ;
        $arr[8][3]['-DespesasOperacionais']    = 0 ;
        $arr[8][4]['-DespesasOperacionais']    = 0 ;
        $arr[8][5]['-DespesasOperacionais']    = 0 ;

        
        return $arr;

    }
    public function formatPrice( $array )
    {
        
        foreach( $array as $k => $v )
        {
            $array[$k] = number_format( $v, 2, ',', '.');
        }
        
        return $array;
    }

}