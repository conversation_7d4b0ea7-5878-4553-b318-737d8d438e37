<?php

class Controller_Index extends Controller_Websession
{

    public function action_pedidos()
    {      


      


        $view =  View::factory("pedidos-stream");
        $this->response->body($view);
    }


    public function action_index()
    {      
        $view =  View::factory("index");
        $this->response->body($view);
    }

    public function action_vendas_hoje($account="")
    {

        $sql = sprintf("	SELECT SUM(valor) as valor FROM Analytics.`vendas_hoje`  ");

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();	

        $view = $result[0]['valor'];

        $this->response->body($view);
    }

    public function action_start()
    {

        $view = '<div  hx-trigger="load  delay:2s" hx-get="/metrics/index/step1">Start</div>';

        $this->response->body($view);

    }

    public function action_step1()
    {

        $view = '<div  hx-trigger="load  delay:2s" hx-get="/metrics/index/step2">Step1</div>';

        $this->response->body($view);

    }

    public function action_step2()
    {

        $view = '<div  hx-trigger="load  delay:2s" hx-get="/metrics/index/end">Step2</div>';

        $this->response->body($view);

    }

    public function action_end()
    {

        $view = '<div  hx-trigger="click  delay:1s" hx-get="/metrics/index/start">End</div>';

        $this->response->body($view);

    }


}