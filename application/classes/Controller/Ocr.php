
<?php

class Controller_Ocr extends Controller_Website
{

   
   public function before()
    {
         parent::before();
         
    }

    public function action_index()
    {
    //  use thiagoalessio\TesseractOCR\TesseractOCR;
        // $text=  (new thiagoalessio\TesseractOCR\TesseractOCR('p228-1.png'))->run();
        // $text=  (new thiagoalessio\TesseractOCR\TesseractOCR('p228-1.png'))->psm(6)->run();
echo (new TesseractOCR('img.png'))
    ->hocr()
    ->run();
    
    die();
       
        $text=  (new thiagoalessio\TesseractOCR\TesseractOCR('p224.jpeg'))->psm(4)->run();
        
         $text=preg_replace( "/\r|\n/", " ", $text );
         
         $text=str_replace( "'", "x", $text );
          $text=str_replace( "‘", "x", $text );
          $text=str_replace( "´", "x", $text );

        
         $array = explode(' ',$text);
         
         $vars = array_slice($array, 32, -10); 
         
         $vars = array_filter( $vars, 'strlen' );
         
         foreach($vars as $val)
         {
          
            // $mode = next($vars);  
           
            $mystring = $val;
            $findme   = 'P';
            $pos = strpos($mystring, $findme);
            
            // Note our use of ===.  Simply == would not work as expected
            // because the position of 'a' was the 0th (first) character.
            // $mode = current($transport); // $mode = 'foot';
            // $mode = next($vars);    // $mode = 'bike';
            // $mode = next($transport);    // $mode = 'car';
            // $mode = prev($transport);    // $mode = 'bike';
            // $mode = end($transport);     // $mode = 'plane';
            if ($pos === false) {
                // echo "The string '$findme' was not found in the string '$mystring'";
                
                $new[]=$val;
            } else {
                // echo "The string '$findme' was found in the string '$mystring'";
                // echo " and exists at position $pos";
                s($mystring);
                
            }
            // prev($vars); 
        }
         
        // $groups = array_chunk($vars, 3); 
        
        // foreach($groups as  $group)
        // {
          
        //         $sql = sprintf('INSERT INTO parts.parts_h_strong (code,page,line) VALUES("%s","%s","%s")',
        //                         $group[0], $group[1], $group[2]
        //                         );
        //         $query = DB::query(Database::INSERT, $sql);
        //         $result = $query->execute();
         
        // }
            
        //  s($groups, $vars);
     
    }
    

}