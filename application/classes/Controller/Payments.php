<?php

class Controller_Payments extends Controller_Website
{

   
    public function before()
    {
         parent::before();
         
    }

    public function action_index()
    {     
        $tipo      = $this->request->param('division');
        $data       = self::get($tipo);
        $theme='light';
        $view = '';
        //// Editable
        
        $edt['TipoDespesa'] = 'mak.fornecedores|TipoDespesa|id|_IDFORN';
        $data['editable'] = $edt;
        
        // s( $this->pagination);
        $data['pagination'] = $this->pagination;
        $data['theme'] = 2;
        
        $color['Fornecedor']['class'] = 'text-blue-600 text-ml font-semibold ';
        // $color['Fornecedor']['rule']  = ' > 0 ' ;
        
        
        $data['color'] = $color;

        
        
        $view.= parent::tablefy( $data );
        
        // s($data);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);

    }

    private function get($tipo=1)
    {
    
        $where = '';
        $year=date('Y');
        $month=date('m');
        
        if($this->request->query('date'));
        {
            $year= substr($this->request->query('date'),0,4);
            $month= substr($this->request->query('date'),5,2);
            $day= substr($this->request->query('date'),8,2);
            if($day) $where.= sprintf(" AND DAY(data_vencimento)=%s ", $day);
        }
        
        
        
        $sql = sprintf("
						SELECT f.id AS _IDFORN, p.id as IDPAG, t.descricao as Descrição, p. `data_vencimento` as DataVencimento , p.`documento` as Documento, p.`obs` as Obs, p.`valor_cobrado` as ValorCobrado, f.nome as Fornecedor,f.fantasia as _Fantasia,f.estado as _Estado,f.TipoDespesa,f.planodecontaPOID,f.crt 
						FROM finance.`pagamentos` p 
						LEFT JOIN mak.fornecedores f ON (f.id=p.fornecedor) 
						LEFT JOIN finance.despesas_tipos t ON (t.id=f.TipoDespesa) 
						WHERE MONTH(data_vencimento)=%s AND 
						        YEAR(data_vencimento)=%s AND 
						        f.TipoDespesa = '%s' AND p.valor_cobrado > 0 
						        %s
						  ORDER BY `p`.`valor_cobrado` DESC
 	                    LIMIT 100
						", $month, $year, $tipo, $where
					
                        );

            // $result = $this->action_connect_SELECT($sql);	

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();	
        
        foreach($result as $key => $val)
        {
                $result[$key]['Rateio_Fornecedor'] = self::select_rateio_fornecedor($val['_IDFORN']);
                $result[$key]['Rateio_Pagamento'] = self::select_rateio_pagamentos($val['IDPAG']);
                $result[$key]['Deletar'] = sprintf('<i hx-get="/sage/banks/audit/dpay/?pay_id=%s" class="text-pink-700  cursor-pointer fas fa-minus-circle"></i>',$val['IDPAG']);
                // $result[$key]['Banco'] = self::select_banco($val['banco']);
                // $result[$key]['contas'] = self::select_contas($val['id']);
        }
        
        
        //s($result);
        return $result;
        

    }

    function select_rateio_fornecedor($fornecedor)
    {
        $sql =  sprintf("   SELECT  
                                    u.Fantasia Unidade, 
                                    s.nome Segmento, 
                                    d.nome Departamento, 
                                    r.Valor Cota  
                            FROM finance.fornecedores_rateio r
                            LEFT JOIN mak.fornecedores f  ON f.id=r.fornecedor 
                            LEFT JOIN finance.cc_unidades u ON u.id=r.unidade 
                            LEFT JOIN finance.cc_segmentos s ON s.id=r.segmento 
                            LEFT JOIN finance.cc_departamentos d ON d.id=r.departamento 
                            WHERE f.id=%s ", $fornecedor);
        $result = $this->action_connect_SELECT($sql);
        
        if(empty($result)) return 'sem rateio definido ';
        
        return $result;
        d($sql, $result);
       
        
        
    }
    
    function select_rateio_pagamentos($fornecedor)
    {
        $sql =  sprintf("   SELECT  
                                    u.Fantasia Unidade, 
                                    s.nome Segmento, 
                                    d.nome Departamento, 
                                    r.Valor Cota
                            FROM finance.pagamentos_rateio r
                            LEFT JOIN finance.pagamentos f  ON f.id=r.pagamento 
                            LEFT JOIN finance.cc_unidades u ON u.id=r.unidade 
                            LEFT JOIN finance.cc_segmentos s ON s.id=r.segmento 
                            LEFT JOIN finance.cc_departamentos d ON d.id=r.departamento 
                            WHERE f.id=%s ", $fornecedor);
        $result = $this->action_connect_SELECT($sql);
        
        // s($result);
        return $result;
        d($sql, $result);
    }

  
    public function action_sum()
    {
        
        
        $view='';    
        
        $times = $this->request->param('division');
        for ( $x=0; $x<$times; $x++ )
        {
            $year=date('Y');
            $month=date('m')-$x;
            // ($this->request->param('division')
            
            $data=self::sum($year,$month);
            $view.=self::sum_view($data);
        }
       
        $theme='light'; 
        $response = parent::pagefy($view,$theme);
        
        $this->response->body($response);
    }
  
    public function sum_view($data)
    {
        $fmt['Subtotal'] = '1kb';
        $data['format'] = $fmt;
        
        $sum['Subtotal'] = '1';
        $data['sum'] = $sum;
        
        $color['Subtotal'][0]['class'] = ' text-red-600 font-bold ';
        $color['Subtotal'][0]['rule']  = ' Operacional > Investimento ';
        $color['Subtotal'][1]['class'] = ' text-green-600 font-bold ';
        $color['Subtotal'][1]['rule']  = ' Investimento > Operacional ';
        $color['Subtotal'][2]['class'] = ' text-yellow-400 font-bold ';
        $color['Subtotal'][2]['rule']  = ' TipoDespesa == 6 ';
        $data['color'] = $color;
        
        $data['theme'] = 3;
        return parent::tablefy( $data );
    }
    
    private function sum($year,$month,$operacional=1)
    {
       
        
        // if($this->request->query('date'));
        // {
        //     $year= substr($this->request->query('date'),0,4);
        //     $month= substr($this->request->query('date'),5,2);
        //     $day= substr($this->request->query('date'),8,2);
        //     if($day) $where.= sprintf(" AND DAY(data_vencimento)=%s ", $day);
        // }
        
        $sql = sprintf("
						SELECT EXTRACT(YEAR_MONTH FROM data_vencimento) as AnoMês, t.descricao,   SUM(p.`valor_cobrado`) AS Subtotal,Operacional,Investimento,f.TipoDespesa
						FROM finance.`pagamentos` p 
						LEFT JOIN mak.fornecedores f ON (f.id=p.fornecedor) 
						LEFT JOIN finance.despesas_tipos t ON (t.id=f.TipoDespesa) 
						WHERE MONTH(data_vencimento)=%s AND 
						      YEAR(data_vencimento)=%s AND 
						      valor_cobrado > 0 AND
						      TipoDespesa <> 0 AND 
						      operacional = %s
						GROUP BY  TipoDespesa    
						ORDER BY `p`.`valor_cobrado` DESC
 	                    LIMIT 80
						",
						 $month, $year, $operacional
					
                        );

            // $result = $this->action_connect_SELECT($sql);	

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();	
        
        $total = 0;
        foreach($result as $k => $v )
        {
            $total+=$v['Subtotal'];
        }
        foreach($result as $k => $v )
        {
            $result[$k]['%']=round(($v['Subtotal']/$total)*100,1);
        }
        
        return $result;
    }
    
    public function action_forn()
    {
         $tipo      = $this->request->param('division');
        $data       = self::forn($tipo);
        $theme='light';
        $view = '';
        //// Editable
        
        $edt['TipoDespesa'] = 'mak.fornecedores|TipoDespesa|id|_IDFORN';
        $data['editable'] = $edt;
        
        // s( $this->pagination);
        $data['pagination'] = $this->pagination;
        $data['theme'] = 2;
        
        $color['Fornecedor']['class'] = 'text-blue-600 text-ml font-semibold ';
        // $color['Fornecedor']['rule']  = ' > 0 ' ;
        
        
        $data['color'] = $color;

        
        
        $view.= parent::tablefy( $data );
        
        // s($data);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);
        
    }
    
    private function forn()
    {
        $tipo      = $this->request->param('division');
        $where = '';
        $year=date('Y');
        $month=date('m');
        
        if($this->request->query('date'));
        {
            $year= substr($this->request->query('date'),0,4);
            $month= substr($this->request->query('date'),5,2);
            $day= substr($this->request->query('date'),8,2);
            if($day) $where.= sprintf(" AND DAY(data_vencimento)=%s ", $day);
        }
        
        
        
        $sql = sprintf("
						SELECT count(p.id) as Counter,f.id AS _IDFORN, f.TipoDespesa, f.nome as Fornecedor,f.estado as _Estado,sum(p.`valor_cobrado`) as Subtotal
						FROM finance.`pagamentos` p 
						LEFT JOIN mak.fornecedores f ON (f.id=p.fornecedor) 
						
						WHERE   f.TipoDespesa = '%s' AND
						        MONTH(data_vencimento)=%s AND 
						        YEAR(data_vencimento)=%s  
						        
						        %s
						GROUP BY _IDFORN        
						  ORDER BY Subtotal DESC
 	                    LIMIT 50
						", $tipo, $month, $year, $where
					
                        );

            // $result = $this->action_connect_SELECT($sql);	

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();	
        
        // foreach($result as $key => $val)
        // {
        //         $result[$key]['Rateio_Fornecedor'] = self::select_rateio_fornecedor($val['_IDFORN']);
        //         $result[$key]['Rateio_Pagamento'] = self::select_rateio_pagamentos($val['IDPAG']);
        //         // $result[$key]['Banco'] = self::select_banco($val['banco']);
        //         // $result[$key]['contas'] = self::select_contas($val['id']);
        // }
        
        
        // s($result);
        return $result;
        

    }    
    
 
     public function action_rateio()
    {
        $tipo      = $this->request->param('division');
        $view       = self::rateio($tipo);
        $theme='light';
      

        
        // s($data);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);
        
    }

 private function rateio()
    {
        $tipo      = $this->request->param('division');
        $where = '';
        $year=date('Y');
        $month=date('m');
        
        if($this->request->query('date'));
        {
            $year= substr($this->request->query('date'),0,4);
            $month= substr($this->request->query('date'),5,2);
            $day= substr($this->request->query('date'),8,2);
            if($day) $where.= sprintf(" AND DAY(data_vencimento)=%s ", $day);
        }
        
          $sql = sprintf("
						SELECT f.id AS _IDFORN, p.id as IDPAG, t.descricao as Descrição, p. `data_vencimento` as DataVencimento , p.`documento` as Documento, p.`obs` as Obs, f.nome as Fornecedor,f.TipoDespesa, p.`valor_cobrado` as ValorCobrado
						FROM finance.`pagamentos` p 
						LEFT JOIN mak.fornecedores f ON (f.id=p.fornecedor) 
						LEFT JOIN finance.despesas_tipos t ON (t.id=f.TipoDespesa) 
						WHERE MONTH(data_vencimento)=%s AND 
						        YEAR(data_vencimento)=%s AND 
						        f.TipoDespesa = '%s' AND p.valor_cobrado > 0 
						        %s
						  ORDER BY `p`.`valor_cobrado` DESC
 	                    LIMIT 500
						", $month, $year, $tipo, $where
					
                        );

            // $result = $this->action_connect_SELECT($sql);	

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();	
        
        $counter=0;
        foreach($result as $key => $val)
        {
                $Rateio_Fornecedor = self::select_rateio_fornecedor($val['_IDFORN']);
                $Rateio_Pagamento  = self::select_rateio_pagamentos($val['IDPAG']);
                if($Rateio_Fornecedor == "sem rateio definido ") $Rateio_Fornecedor = null;
                
                
                if(isset($Rateio_Pagamento[0]))
                {
                    foreach($Rateio_Pagamento as $pagamento)
                    {
                        $data[$counter] = $val;
                        // $data[$counter]['Valor'] = $val['ValorCobrado'];
                        $data[$counter]['Unidade']     = $pagamento['Unidade'];
                        $data[$counter]['Segmento']    = $pagamento['Segmento'];
                        $data[$counter]['Departamento']    = $pagamento['Departamento'];
                        $data[$counter]['Cota']        = $pagamento['Cota'];
                        $data[$counter]['ValorRateio'] = $val['ValorCobrado']*($pagamento['Cota']/100) ;
                        $counter++;
                    }    
                    
                }else{
                    
                    if(isset($Rateio_Fornecedor[0]))
                    {
                        foreach($Rateio_Fornecedor as $fornecedor)
                        {
                            $data[$counter] = $val;
                            // $data[$counter]['Valor'] = $val['ValorCobrado'];
                            $data[$counter]['Unidade']  = $fornecedor['Unidade'];
                            $data[$counter]['Segmento']  = $fornecedor['Segmento'];
                            $data[$counter]['Departamento']    = $fornecedor['Departamento'];
                            $data[$counter]['Cota']  = $fornecedor['Cota'];
                            $data[$counter]['ValorRateio'] = $val['ValorCobrado']*($fornecedor['Cota']/100) ;
                            $counter++;
                        }    
                    }
                    
                }
                
                
                
                
                // $data[$counter]['Unidade'] = $result[$key]['Rateio_Pagamento']['Unidade'];
                
                // $result[$key]['Banco'] = self::select_banco($val['banco']);
                // $result[$key]['contas'] = self::select_contas($val['id']);
        }
        
        
        foreach($data as $rateio)
        {
            // s($rateio);
            if(!isset($unidades[$rateio['Unidade']])) $unidades[$rateio['Unidade']]=0;
            $unidades[$rateio['Unidade']]+= $rateio['ValorRateio'];
            
            if(!isset($segmentos[$rateio['Segmento']])) $segmentos[$rateio['Segmento']]=0;
            $segmentos[$rateio['Segmento']]+= $rateio['ValorRateio'];

            if($rateio['Departamento']=='') $rateio['Departamento'] ='Nihil';
            if(!isset($departamentos[$rateio['Departamento']])) $departamentos[$rateio['Departamento']]=0;
            $departamentos[$rateio['Departamento']]+= $rateio['ValorRateio'];
          
        }
        
        

        $respo[0]=  $unidades;
        // $respo[0]['Segmento'][0] =  $segmentos;
        // $respo[0]['Departamento'][0] =  $departamentos;
        // s($respo);
        
        $view='';
        $respo['theme'] = 3;
        
        $respo[0]=  $unidades;
        $view.= parent::tablefy( $respo );
        
        $respo[0] =  $segmentos;
        $view.= parent::tablefy( $respo );

        $respo[0]=  $departamentos;
        $view.= parent::tablefy( $respo );
        
        return $view;
        
       
    }    
    
}