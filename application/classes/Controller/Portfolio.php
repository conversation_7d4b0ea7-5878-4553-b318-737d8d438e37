<?php

class Controller_Portfolio extends Controller_Website
{

   
   public function before()
    {
         parent::before();
         
    }


    public function action_index()
    {
        $segid = $this->request->param('division');
        $brand = $this->request->param('xtras');

        if($segid==1)
        {
            
            $data = self::next('machines');
            $data['caption']= self::machines();
        }
        if($segid==2)
        {
            $data = self::next('bearings');
            $data['caption']= self::bearings();
        }
        
         if($segid==3)
        {
            $data = self::next('parts');
            $data['caption']= self::parts();
        }
        
          if($segid==5)
        {
            $data = self::next('auto');
            $data['caption']= self::auto();
        }

        $theme='datatables';
        $view = '';
        
      
       $color['Growth'][0]['class'] = ' text-red-400 text-md font-bold';
        $color['Growth'][0]['rule']  = ' > 0 ';
        $color['Growth'][1]['class'] = ' text-red-400 text-md font-bold';
        $color['Growth'][1]['rule']  = ' > 50 ';
        $color['Growth'][2]['class'] = ' text-blue-600 text-md  font-bold';
        $color['Growth'][2]['rule']  = ' > 75';
        $color['Growth'][3]['class'] = ' text-green-700 text-md  font-bold';
        $color['Growth'][3]['rule']  = ' > 100 ';
        
        
        $color['PercMês'][0]['class'] = ' text-red-400 text-md font-bold';
        $color['PercMês'][0]['rule']  = ' > 0 ';
        $color['PercMês'][1]['class'] = ' text-red-400 text-md font-bold';
        $color['PercMês'][1]['rule']  = ' > 5 ';
        $color['PercMês'][2]['class'] = ' text-blue-600 text-md  font-bold';
        $color['PercMês'][2]['rule']  = ' > 10';
        $color['PercMês'][3]['class'] = ' text-green-700 text-md  font-bold';
        $color['PercMês'][3]['rule']  = ' > 20 ';
        
        $color['Subtotal'][0]['class'] = ' text-red-400 text-md font-bold';
        $color['Subtotal'][0]['rule']  = ' > 1000 ';
        $color['Subtotal'][1]['class'] = ' text-yellow-400 text-md font-bold';
        $color['Subtotal'][1]['rule']  = ' > 10000 ';
        $color['Subtotal'][2]['class'] = ' text-blue-600 text-md  font-bold';
        $color['Subtotal'][2]['rule']  = ' > 50000';
        $color['Subtotal'][3]['class'] = ' text-green-700 text-md  font-bold';
        $color['Subtotal'][3]['rule']  = ' > 100000 ';
        
        $data['color'] = $color;
        
        //// Editable
        
         $edt['Vip'] = 'mak.clientes|vip|id|_IDCLI';
         $edt['ParecerComercial'] = 'mak.clientes|ParecerComercial|id|_IDCLI';
         $data['editable'] = $edt;
         
          //// Format Data
        $fmt['Sugerido'] = $fmt['Revenda'] = $fmt['Diff'] = $fmt['ValorMedioAno'] = '2us'; 
        $fmt['Fob'] = $fmt['LastFob'] = '3us';
        
        if($segid==1)
            $fmt['Fob'] = $fmt['LastFob'] = $fmt['Sugerido'] = $fmt['Revenda'] = $fmt['ValorMedioAno'] = '0br'; 
            
        $fmt['Subtotal'] = $fmt['Acumulado']  = '1kb';
        $fmt['AGE'] = '0us'; 
        $data['format'] = $fmt;
        
        // $idven =  $this->request->query('idven');
        // $baseurl='/metrics/customers/index';
        
        
            
        // s( $this->pagination);
        // $data['pagination'] = $this->pagination;
        $data['theme'] = 2;
        
    //   $data['found_rows'] = $found_rows;
        // $data['pager']['pagination'] = $this->pagination;
        // // die();
        
        
       
        
        $view.= parent::tablefy( $data );
        
        // s($data['pagination']);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);
        // s($data);

        // echo 'index '. $this->request->param('id');
        // $inv = json_decode(self::get_product(), true);

        //s($inv);
        // $solr = self::get_solr($code);
        //  s($solr);
        //  die();
        // $this->product = array_merge($inv, $solr);
        //s($this->product );
        //if($this->product['produtoComprasSugeridoUnidades'] < 0)  die('Quantidade sugerida menor que 0 . Favor vericar se já existe pedidos de compra suficientes para este produto.');

        //json_decode(self::get_product(),true);

        // $view = View::factory('history')
        //     ->set('menu', '')
        //     ->set('array', $this->product)
        //     ->set('factories', $factories)
        //     ->set('next', $next);

        // $this->response->body($view);
    }
  
   private function auto()
    {
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?data='.date("Y-m-d"), 'title' => 'Hoje') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?data='.date("Y-m"), 'title' => 'Mês') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?data='.date("Y"), 'title' => 'Ano') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?recencia=0 AND 30', 'title' => 'Recência 0-30') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?recencia=31 AND 60', 'title' => 'Recência 31-60') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?recencia=61 AND 60', 'title' => 'Recência 61-90') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?recencia=91 AND 180', 'title' => 'Recência 91-180') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?recencia=181 AND 365', 'title' => 'Recência 181-365') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?cidade=Sao Paulo', 'title' => 'Cidade-SP') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?estado=SP', 'title' => 'SP') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?estado=MG', 'title' => 'MG') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?estado=PR', 'title' => 'PR') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?estado=SC', 'title' => 'SC') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?estado=RS', 'title' => 'RS') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?estado=RJ', 'title' => 'RJ') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?estado=GO', 'title' => 'GO') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?estado=CE', 'title' => 'CE') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?estado=PE', 'title' => 'PE') ;
      
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?nick=Narciso', 'title' => 'Narciso') ;
         
         
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?modelo=MTR-CV', 'title' => 'MTR-CV') ;

         
         return $data['caption'];
    
    } 
  
     private function parts()
    {
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?data='.date("Y-m-d"), 'title' => 'Hoje') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?data='.date("Y-m"), 'title' => 'Mês') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?data='.date("Y"), 'title' => 'Ano') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?recencia=0 AND 30', 'title' => 'Recência 0-30') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?recencia=31 AND 60', 'title' => 'Recência 31-60') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?recencia=61 AND 60', 'title' => 'Recência 61-90') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?recencia=91 AND 180', 'title' => 'Recência 91-180') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?recencia=181 AND 365', 'title' => 'Recência 181-365') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?cidade=Sao Paulo', 'title' => 'Cidade-SP') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?estado=SP', 'title' => 'SP') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?estado=MG', 'title' => 'MG') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?estado=PR', 'title' => 'PR') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?estado=SC', 'title' => 'SC') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?estado=RS', 'title' => 'RS') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?estado=RJ', 'title' => 'RJ') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?estado=GO', 'title' => 'GO') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?estado=CE', 'title' => 'CE') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?estado=PE', 'title' => 'PE') ;
      
        $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?marca=ksm', 'title' => 'KSM') ;
        $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?marca=desheng', 'title' => 'Desheng') ;
        $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?marca=strong', 'title' => 'Strong H') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?marca=jec', 'title' => 'JEC') ;
            
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?nick=Industria', 'title' => 'Industria') ;
         
         
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?modelo=btr', 'title' => 'btr') ;

         
         return $data['caption'];
    
    } 
    private function bearings()
    {
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?data='.date("Y-m-d"), 'title' => 'Hoje') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?data='.date("Y-m"), 'title' => 'Mês') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?data='.date("Y"), 'title' => 'Ano') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?recencia=0 AND 30', 'title' => 'Recência 0-30') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?recencia=31 AND 60', 'title' => 'Recência 31-60') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?recencia=61 AND 60', 'title' => 'Recência 61-90') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?recencia=91 AND 180', 'title' => 'Recência 91-180') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?recencia=181 AND 365', 'title' => 'Recência 181-365') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?cidade=Sao Paulo', 'title' => 'Cidade-SP') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?estado=SP', 'title' => 'SP') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?estado=MG', 'title' => 'MG') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?estado=PR', 'title' => 'PR') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?estado=SC', 'title' => 'SC') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?estado=RS', 'title' => 'RS') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?estado=RJ', 'title' => 'RJ') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?estado=GO', 'title' => 'GO') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?estado=CE', 'title' => 'CE') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?estado=PE', 'title' => 'PE') ;
         
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?marca=ppk', 'title' => 'PPK') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?marca=mak', 'title' => 'MAK') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?marca=dtb', 'title' => 'DTB') ;
         
         
      
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?nick=Industria', 'title' => 'Industria') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?nick=Marcio', 'title' => 'Marcio') ;
         
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?modelo=6301', 'title' => '6301') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?modelo=6201', 'title' => '6201') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?modelo=6202', 'title' => '6202') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?modelo=6203', 'title' => '6203') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?modelo=6204', 'title' => '6204') ;
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?modelo=CS608', 'title' => 'CS608') ;
         
         return $data['caption'];
    
    }

 
    private function machines()
    {
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?data='.date("Y-m-d"), 'title' => 'Hoje') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?data='.date("Y-m"), 'title' => 'Mês') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?data='.date("Y"), 'title' => 'Ano') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?a2021=1', 'title' => 'Abstinente-2022') ;
             
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?recencia=0 AND 30', 'title' => 'Recência 0-30') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?recencia=31 AND 60', 'title' => 'Recência 31-60') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?recencia=61 AND 60', 'title' => 'Recência 61-90') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?recencia=91 AND 180', 'title' => 'Recência 91-180') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?recencia=181 AND 365', 'title' => 'Recência 181-365') ;
             
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?cep=0110', 'title' => 'São Caetano street') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?cep=0112', 'title' => 'Bom Retiro') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?cep=03', 'title' => 'Brás') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?cidade=Sao Paulo', 'title' => 'Cidade-SP') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=SP', 'title' => 'SP') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=MG', 'title' => 'MG') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=PR', 'title' => 'PR') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=SC', 'title' => 'SC') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=RS', 'title' => 'RS') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=RJ', 'title' => 'RJ') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=GO', 'title' => 'GO') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=CE', 'title' => 'CE') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=PE', 'title' => 'PE') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=RN', 'title' => 'RN') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=PA', 'title' => 'PA') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=PI', 'title' => 'PI') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=BA', 'title' => 'BA') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?estado=DF', 'title' => 'DF') ;
             
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?nick=Regiane', 'title' => 'Regiane') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?nick=Rosana', 'title' => 'Rosana') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?nick=Miriam', 'title' => 'Miriam') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?nick=Debora', 'title' => 'Debora') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?nick=Ediam', 'title' => 'Ediam') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?nick=Fernanda', 'title' => 'Fernanda') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?nick=Edilene', 'title' => 'Edilene') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?nick=Camilly', 'title' => 'Camilly') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?nick=Amanda', 'title' => 'Amanda') ;
             
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=A8100', 'title' => 'A8100') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=A8000', 'title' => 'A8000') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=B9500', 'title' => 'B9500') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=C5000', 'title' => 'C5000') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=A8000', 'title' => 'A8000') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=A6000', 'title' => 'A6000') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=0303', 'title' => 'ZJ-0303') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=ZJ-9610', 'title' => 'ZJ-9610') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=ZJ-1900', 'title' => 'ZJ-1900') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=ZJ-M', 'title' => 'ZJ-M') ;
             
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=SP-730', 'title' => 'SP-730') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=SP-282', 'title' => 'SP-282') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=SP-700', 'title' => 'SP-700D') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=SP-5', 'title' => 'SP-500') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=B1', 'title' => 'B1') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?modelo=M1968', 'title' => 'M1968') ;
             
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?marca=Zoje', 'title' => 'Zoje') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?marca=sewpower', 'title' => 'Sewpower') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?marca=dollor', 'title' => 'Dollor') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?marca=mak prime', 'title' => 'Makprime') ;
             $data['caption'][] = array('link'=>'/metrics/portfolio/index/1/?marca=supernova', 'title' => 'Supernova') ;
           
             
             
             return $data['caption'];
        
    }

    private function next($segmento='machines')
    {
           
        $having="";
        $where="";
        $group="";
        
        // if($this->request->query('not')) $where.= sprintf(" AND %s ", $this->request->query('not'));
        // echo $where;
        // die();
        if($this->request->query('estado')) $where.= sprintf(" AND estado='%s' ", $this->request->query('estado'));
        if($this->request->query('bairro')) $where.= sprintf(" AND bairro='%s' ", $this->request->query('bairro'));
        if($this->request->query('cidade'))
        {
            if(substr($this->request->query('cidade'),0,1)=='!')
            {
                $where.= sprintf(" AND cidade != '%s' ",  ltrim($this->request->query('cidade'), '!'));
            }else{
                $where.= sprintf(" AND cidade='%s' ", $this->request->query('cidade'));
            }
        }
        if($this->request->query('nome')) $where.= sprintf(" AND c.nome LIKE '%s' ", '%'.$this->request->query('nome').'%');
        if($this->request->query('nfe')) $where.= sprintf(" AND nfe LIKE '%s' ", $this->request->query('nfe').'%');
        if($this->request->query('nick')) $where.= sprintf(" AND u.nick LIKE '%s' ", $this->request->query('nick').'%');
        if($this->request->query('marca')) $where.= sprintf(" AND i.marca LIKE '%s' ", $this->request->query('marca').'%');
        if($this->request->query('modelo')) $where.= sprintf(" AND i.modelo LIKE '%s' ", '%'.$this->request->query('modelo').'%');
        if($this->request->query('data')) $where.= sprintf(" AND h.data LIKE '%s' ", $this->request->query('data').'%');
        if($this->request->query('cep')) $where.= sprintf(" AND c.cep LIKE '%s' ", $this->request->query('cep').'%');
        if($this->request->query('group')) $group= sprintf(" %s ", $this->request->query('group'));
        // if($this->request->query('vols')) $having= sprintf(" Having vols %s ", $this->request->query('vols'));
        if($this->request->query('recencia')) $having= sprintf(" Having `Dias` BETWEEN %s ", $this->request->query('recencia'));
        if($this->request->query('blue')) $having= sprintf(" Having `IdxBlue` BETWEEN %s ", $this->request->query('blue'));
        if($this->request->query('a2021')) $having= sprintf(" Having `QtA2022` < 1 ");
        
        // if($this->request->query('not')) $where.= sprintf(" AND cidade != '%s' ", $this->request->query('blue'));
            
            //  SUM(IF(YEAR(h.data)=2022 AND MONTH(h.data)=1, round(hi.quant,0) ,0)) as QuantJaneiro,
						      //   SUM(IF(YEAR(h.data)=2022 AND MONTH(h.data)=2, round(hi.quant,0) ,0)) as QuantFevereiro,
						      //   SUM(IF(YEAR(h.data)=2022 AND MONTH(h.data)=3, round(hi.quant,0) ,0)) as QuantMarço,
						      //   SUM(IF(YEAR(h.data)=2022 AND MONTH(h.data)=4, round(hi.quant,0) ,0)) as QuantAbril,
						      //   SUM(IF(YEAR(h.data)=2022 AND MONTH(h.data)=5, round(hi.quant,0) ,0)) as QuantMaio,
						      //   SUM(IF(YEAR(h.data)=2022 AND MONTH(h.data)=6, round(hi.quant,0) ,0)) as QuantJunho,
						      //   SUM(IF(YEAR(h.data)=2022 AND MONTH(h.data)=7, round(hi.quant,0) ,0)) as QuantJulho,
						      
						       
          $sql = sprintf("SELECT 
                                 c.cnpj _CNPJ, 
                                 c.id as _IDCLI,
                                  u.nick Vendedor,
						         LCASE(c.nome) as Cliente,
						        
						         LCASE(c.Cidade) Cidade,
						         c.Estado UF,
						         cep _Cep,
						         c.Vip,
						         
						         DATEDIFF(NOW(),MAX(h.data)) AS Dias,
						         SUM(IF(YEAR(h.data)=2024, round(hi.quant*hi.valor_base,0) ,0)) Subtotal,
						         SUM(IF(YEAR(h.data)=2022, round(hi.quant,0) ,0)) as QtA2022,
						         SUM(IF(YEAR(h.data)=2023, round(hi.quant,0) ,0)) as QtA2023,
						         SUM(IF(YEAR(h.data)=2024, hi.quant,0)) as QtA2024,
						         
						         SUM(IF(YEAR(h.data)=2024 AND MONTH(h.data)=1, round(hi.quant,0) ,0)) as QtJan,
						         SUM(IF(YEAR(h.data)=2024 AND MONTH(h.data)=2, round(hi.quant,0) ,0)) as QtFev,
						         SUM(IF(YEAR(h.data)=2024 AND MONTH(h.data)=3, round(hi.quant,0) ,0)) as QtMar,
						         SUM(IF(YEAR(h.data)=2024 AND MONTH(h.data)=4, round(hi.quant,0) ,0)) as QtAbr,
						         SUM(IF(YEAR(h.data)=2024 AND MONTH(h.data)=5, round(hi.quant,0) ,0)) as QtMai,
						         SUM(IF(YEAR(h.data)=2024 AND MONTH(h.data)=6, round(hi.quant,0) ,0)) as QtJun,
						         SUM(IF(YEAR(h.data)=2024 AND MONTH(h.data)=7, round(hi.quant,0) ,0)) as QtJul,
						         SUM(IF(YEAR(h.data)=2024 AND MONTH(h.data)=8, round(hi.quant,0) ,0)) as QtAgo,
						         SUM(IF(YEAR(h.data)=2024 AND MONTH(h.data)=9, round(hi.quant,0) ,0)) as QtSet,
						         SUM(IF(YEAR(h.data)=2024 AND MONTH(h.data)=10, round(hi.quant,0) ,0)) as QtOut,
						         round(SUM(IF(YEAR(h.data)=2024 AND MONTH(h.data)=10, hi.quant,0))/SUM(hi.quant)*100,0) as PercMês,
						         round(SUM(IF(YEAR(h.data)=2024, hi.quant,0))/SUM(IF(YEAR(h.data)=2023, round(hi.quant,0) ,0))*100,0) as Growth,
						         SUM(hi.quant) as Qt,
						         
						         ROUND((SELECT SUM( IF( (s.peso/s.volumes*2)>40,round(s.volumes/2,0),0)) FROM Analytics.ssw s WHERE s.cnpj=c.cnpj AND s.seller='welttec' )/SUM(IF(YEAR(h.data)=2022, hi.quant,0)),0) as IdxBlue,
						         (SELECT SUM( IF( (s.peso/s.volumes*2)>40,round(s.volumes/2,0),0)) FROM Analytics.ssw s WHERE s.cnpj=c.cnpj AND s.seller='welttec' ) as Blue,
						         (SELECT SUM( IF( (s.peso/s.volumes*2)>40,round(s.volumes/2,0),0)) FROM Analytics.ssw s WHERE s.cnpj=c.cnpj AND s.seller='ss' ) as Red,
						         (SELECT SUM( IF( (s.peso/s.volumes*2)>40,round(s.volumes/2,0),0)) FROM Analytics.ssw s WHERE s.cnpj=c.cnpj AND s.seller='bracob' ) as Brc,
						         c.ParecerComercial
						         
						 FROM `hoje` h 
						 LEFT JOIN clientes c ON (c.id=h.idcli) 
						 LEFT JOIN hist hi ON (hi.pedido=h.id) 
						 LEFT JOIN inv i ON (i.id=hi.isbn) 
						 LEFT JOIN produtos p ON (p.id=i.idcf) 
						 LEFT JOIN users u ON (u.id=c.vendedor) 
						 WHERE c.vip<9 AND hi.valor_base>0 AND  p.segmento='%s' AND nop IN (27,28,51,76) AND YEAR(h.data)>2021 %s 
						 GROUP BY Cliente 
						 %s
						 ORDER BY `Subtotal` DESC 
                         LIMIT 1000", $segmento, $where,$having  );
             $query = DB::query(Database::SELECT, $sql);
             $result = $query->execute()->as_array();
             
            foreach($result as $k => $v)
             {
                
                $result[$k]['Modelo'] = self::model($v['_IDCLI']);
                
             }
         return $result ;
        
        // = $this->action_connect_SELECT($sql);
    }
    
    private function model($idcli)
    {    
            
         $sql = sprintf(" Select modelo,
                                 SUM(hist.quant) AS q,
                                 SUM(hist.quant*hist.valor_base) AS v 
                          From inv,hist
                          LEFT JOIN hoje h ON (h.id=hist.pedido)
                          WHERE hist.valor_base>0 AND  inv.id=hist.isbn AND hist.idcli=%s and YEAR(h.data)>2020  AND nop IN (27,28,51)
                          GROUP BY hist.isbn 
                          Order by v DESC 
                          Limit 4 ", $idcli  );
                         
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();
        
        // s($result)     ;
        //die();
        if($result[0]['q']>0)
         {
             foreach($result as $k => $v)
             {
                $m[]= ''.$v['q'].' x '.$v['modelo'] ;
             }
             
            return implode(',  ',$m) ;
            
         }else{
             
            return ''; 
         }
             
        
        
    }
  
  
    private function ssw($cnpj)
    {    
            
         $sql = sprintf("SELECT   SUM( IF( (s.peso/s.volumes*2)>40,round(s.volumes/2,0),0)) as ssw
						 FROM Analytics.ssw s
						 WHERE cnpj=%s AND ( seller='welttec' )
                         LIMIT 500", $cnpj  );
                         
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();
             
        if($result[0]['ssw']>0)
         {
            return $result[0]['ssw'] ;
         }else{
            return 0; 
         }
             
        
        
    }
  
    
    public function action_list()
    {     
        $division = $this->request->param('division');
        $table = 'Lists.'.$this->request->param('xtras');    
        
        self::process($table,$division);
    }
    
    
    public function process($table,$segid=1)
    {     
        
        list($data,$found_rows)       = self::getCustomers($table,$segid);
        // s($found_rows);
        // die();
        // $tot        = self::getTotalAmount($segid);
        
        $theme='dark';
        $view = '';
        
        // s($data);
        if($this->request->query('data')=='json') 
        {
            $view  = json_encode($data);
        }
        
        // field color    
        // $color['Pendentes'][0]['class'] = ' text-red-400 text-md font-bold';
        // $color['Pendentes'][0]['rule']  = ' > 100 ';
        // $color['Pendentes'][1]['class'] = ' text-red-600 text-xl  font-semibold';
        // $color['Pendentes'][1]['rule']  = ' > 1000';
        // $color['Pendentes'][2]['class'] = ' text-red-700 text-2xl  font-bold';
        // $color['Pendentes'][2]['rule']  = ' > 10000 ';

       
        
         $color['ComprasMês'][0]['class'] = ' text-green-300 text-md font-bold';
        $color['ComprasMês'][0]['rule']  = ' > 10000 ';
        $color['ComprasMês'][1]['class'] = ' text-green-400 text-xl font-bold';
        $color['ComprasMês'][1]['rule']  = ' > 20000 ';
        $color['ComprasMês'][2]['class'] = ' text-green-500 text-2xl  font-bold';
        $color['ComprasMês'][2]['rule']  = ' > 50000';
        $color['ComprasMês'][3]['class'] = ' text-green-700 text-3xl  font-bold';
        $color['ComprasMês'][3]['rule']  = ' > 75000 ';
        
        
        $color['ComprasAno'][0]['class'] = ' text-green-200 text-md font-bold';
        $color['ComprasAno'][0]['rule']  = ' > 50000 ';
        $color['ComprasAno'][1]['class'] = ' text-green-300 text-lg font-bold';
        $color['ComprasAno'][1]['rule']  = ' > 100000 ';
        $color['ComprasAno'][2]['class'] = ' text-green-400 text-xl font-bold';
        $color['ComprasAno'][2]['rule']  = ' > 200000 ';
        $color['ComprasAno'][3]['class'] = ' text-green-500 text-2xl  font-bold';
        $color['ComprasAno'][3]['rule']  = ' > 300000';
        $color['ComprasAno'][4]['class'] = ' text-green-600 text-3xl  font-bold';
        $color['ComprasAno'][4]['rule']  = ' > 500000 ';
        
        
        $color['UltimaCompraDias'][0]['class'] = ' text-green-400 text-md font-bold';
        $color['UltimaCompraDias'][0]['rule']  = ' > 0 ';
        $color['UltimaCompraDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['UltimaCompraDias'][1]['rule']  = ' > 30 ';
        $color['UltimaCompraDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        $color['UltimaCompraDias'][2]['rule']  = ' > 60';
        $color['UltimaCompraDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        $color['UltimaCompraDias'][3]['rule']  = ' > 90 ';
        
         $color['UltimoTicketDias'][0]['class'] = ' text-green-400 text-md font-bold';
        $color['UltimoTicketDias'][0]['rule']  = ' > 0 ';
        $color['UltimoTicketDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['UltimoTicketDias'][1]['rule']  = ' > 30 ';
        $color['UltimoTicketDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        $color['UltimoTicketDias'][2]['rule']  = ' > 60';
        $color['UltimoTicketDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        $color['UltimoTicketDias'][3]['rule']  = ' > 90 ';
        
        $data['color'] = $color;
        
        //// Editable
        $edt['VendedorID'] = 'mak.clientes|vendedor|id|_IDCLI';
        $edt['ParecerComercial'] = 'mak.clientes|ParecerComercial|id|_IDCLI';
        $data['editable'] = $edt;
        
        $fmt['LimiteLiberado'] = $fmt['ComprasAno'] = $fmt['ComprasMês']  = '1kb';
        $fmt['AGE'] = '0us'; 
        $data['format'] = $fmt;
        
        $idven =  $this->request->query('idven');
        $baseurl='/metrics/customers/index';
        
        
            
        // s( $this->pagination);
        // $data['pagination'] = $this->pagination;
        $data['theme'] = 2;
        
    //   $data['found_rows'] = $found_rows;
        // $data['pager']['pagination'] = $this->pagination;
        // // die();
        
            // $data['caption'][] = array('link'=>$baseurl.'/1/?brand=zoje', 'title' => 'Zoje') ;
            // $data['caption'][] = array('link'=>$baseurl.'/1/?name=reta', 'title' => 'Retas') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=620', 'title' => '6200') ;

    
        
        $view.= parent::tablefy( $data );
        
        // s($data['pagination']);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);

    }
    
  
   
    private function getCustomers($table='clientes_follow',$segment=1)
    {
        // $order ='Sub DESC';
        $this->segment = $this->segments[$segment]['Name'];
        // $order =' Subtotal DESC'; 
       
        // if($this->request->query('sort')=='') $order =' Age DESC'; 
        // if($this->request->query('sort')=='last') $order =' UltimaVenda DESC'; 
        // $this->order = $order; 
        
        $this->pagination = Pagination::factory(array(
            'total_items'    => 100,
            'items_per_page' => 10,
        ));
       
       
        $w = ''; 

        if(isset($_GET['nome'])) {
            $w.=sprintf(" AND c.nome LIKE '%s' ", $_GET['nome'].'%' );
            $this->pagination = Pagination::factory(array(
                'total_items'    => 200,
                'items_per_page' => 5,
            ));
        }
      
       $this->pagination->items_per_page=10;
       $start =120;
       $end   =365;
        if($this->request->query('idven')) $w.=sprintf(" AND c.vendedor = '%s' ",     $this->request->query('idven'));
        if($this->request->query('min'))  $start= $this->request->query('min'); 
        if($this->request->query('max'))  $end=$this->request->query('max'); 
        if($this->request->query('rows'))  $this->pagination->items_per_page =$this->request->query('rows');
        
        
     
        //             ROUND( c.Limite - (SELECT  SUM(ch.valor) FROM cheques ch WHERE ch.idcli=c.id AND ( YEAR(datadep)=0 OR datadep> current_date ) GROUP BY ch.idcli),0) AS LimiteDisponível
        //  (SELECT DATEDIFF(NOW(), data) FROM Ecommerce.clientes_login cl WHERE cl.idcli=c.id ORDER BY cl.id DESC LIMIT 1 ) as ÚltimoAcesso,
    // FORMAT((SELECT SUM(hoje.valor) FROM hoje WHERE hoje.idcli=cf.idcli  AND (nop=27 OR nop=28 OR nop=51) AND DAY(hoje.data)=DAY(NOW()) AND MONTH(hoje.data)=MONTH(NOW()) and YEAR(hoje.data) = YEAR(NOW())),0,'de_DE') AS ComprasDia,  
    //  FORMAT((SELECT SUM(hoje.valor) FROM hoje WHERE hoje.idcli=cf.idcli  AND (nop=27 OR nop=28 OR nop=51) AND WEEK(hoje.data)=WEEK(NOW()) and YEAR(hoje.data) = YEAR(NOW()) ),0,'de_DE') AS ComprasSemana,
    //  (SELECT  SUM(ch.valor) FROM cheques ch WHERE ch.idcli=c.id AND ( YEAR(datadep)=0 OR datadep> current_date ) GROUP BY ch.idcli) AS Pendentes,
       $sql = sprintf("    
                    SELECT SQL_CALC_FOUND_ROWS
						c.id,
						c.id as _IDCLI,
						LCASE(nome) as Cliente,
						CONCAT_WS(' - ',ender,nro) as _Endereço,
						LEFT(cep,5) as Cep,
						LCASE(cidade) as Cidade,
						UCASE(estado) as UF,
						u.nick as GerenteDoCliente,
						u.id as VendedorID,
						TO_DAYS(NOW()) - (SELECT TO_DAYS(max(hoje.datae)) FROM hoje WHERE  hoje.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51) and hoje.datae <= CURDATE() GROUP BY hoje.idcli ) AS UltimaCompraDias,
						                   
                        (SELECT DATEDIFF(NOW(), data) FROM crm.chamadas ch WHERE ch.cliente_id=c.id ORDER BY ch.id DESC LIMIT 1 ) as UltimoTicketDias,
                        (SELECT DATEDIFF(NOW(), date) FROM webteam.clientes_visitas cv WHERE cv.idcli=c.id ORDER BY cv.id DESC LIMIT 1 ) as _UltimaVisita,
                        (SELECT DATEDIFF(NOW(), data) FROM Ecommerce.clientes_login cl WHERE cl.idcli=c.id ORDER BY cl.id DESC LIMIT 1 ) as _UltimoAcessoSite,
                        
                        
                        (SELECT SUM(hoje.valor) FROM hoje WHERE hoje.idcli=cf.idcli  AND (nop=27 OR nop=28 OR nop=51) AND DAY(hoje.data)=DAY(NOW()) AND MONTH(hoje.data)=MONTH(NOW()) and YEAR(hoje.data) = YEAR(NOW())) AS ComprasDia,  
                        
                        (SELECT SUM(hoje.valor) FROM hoje WHERE hoje.idcli=cf.idcli  AND (nop=27 OR nop=28 OR nop=51) AND WEEK(hoje.data)=WEEK(NOW()) and YEAR(hoje.data) = YEAR(NOW()) )AS ComprasSemana,
                        
                        (SELECT SUM(hoje.valor) FROM hoje WHERE hoje.idcli=cf.idcli  AND (nop=27 OR nop=28 OR nop=51) AND MONTH(hoje.data)=MONTH(NOW()) and YEAR(hoje.data) = YEAR(NOW()) ) AS ComprasMês,
                        (SELECT SUM(hoje.valor) FROM hoje WHERE hoje.idcli=cf.idcli  AND (nop=27 OR nop=28 OR nop=51) AND YEAR(hoje.data) = YEAR(NOW()) ) AS ComprasAno,
                        
                        
                        
                     
                        
                        (SELECT SUM(cheques.valor) FROM cheques WHERE cheques.idcli=c.id AND cheques.data < CURDATE()-3 AND ( ISNULL(cheques.datadep) OR MONTH(cheques.datadep)=0 ) GROUP BY cheques.idcli) AS ValorEmAtraso,
                    
                        c.Limite as LimiteLiberado,
                        
                      
                        
                        c.ParecerComercial
                  
                   

					FROM %s cf
					LEFT JOIN mak.clientes c ON (cf.idcli=c.id)
                    LEFT JOIN mak.users u ON (u.id=c.vendedor)
                    WHERE 1
                    GROUP BY c.id
                    
                    ORDER BY UltimaCompraDias ASC
            						
					LIMIT 100", $table,
                           
                           $this->pagination->offset+$this->pagination->current_page-1,
                           $this->pagination->items_per_page);

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();
        
        //s($result);
        
        $sql = "SELECT FOUND_ROWS()";
        $query = DB::query(Database::SELECT, $sql);
        $found_rows = $query->execute()->as_array();

     
        
        foreach( $result as $key => $val) 
        {
            // $result[$key]['CV'] =  self::getCV($val['_IDCLI']);
            
            $result[$key]['Cliente'] =  ucwords($val['Cliente']);
            $result[$key]['Cidade'] =  ucwords($val['Cidade']);
            // foreach( $val as $k => $v) 
            // {
            //     if(empty($v)) $result[$key][$k] = 0;
            // }
            
            // if($val['UltimaCompra']< 1) $result[$key]['UltimaCompra'] = 'nunca';
            // if($val['Pendentes']< 1) $result[$key]['Pendentes'] = 0.00;


        }


        
    

        return  array($result,$found_rows[0]['FOUND_ROWS()']);

    }

  
    private function getCV($idcli=1)
    {
        
        $w=sprintf(" AND c.id='%s'",$idcli);

        // e.contatos as Contatos,
        $sql = sprintf("
                    SELECT u.id as _IDVEN,u.nick as Vendedor,u.segmento as Divisão, contact_rate as ContactRate,
                    c.recency AS Recência,
                    

                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and 
                    YEAR(h.data)= YEAR(CURRENT_DATE - INTERVAL 2 MONTH) AND MONTH(h.data)=  MONTH(CURRENT_DATE - INTERVAL 2 MONTH) and h.vendedor=cv.idven and  h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51)  GROUP BY h.idcli ),0) AS  MesRetrasado,

                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and YEAR(h.data)= YEAR(CURRENT_DATE - INTERVAL 1 MONTH) AND MONTH(h.data)=  MONTH(CURRENT_DATE - INTERVAL 1 MONTH) and  h.vendedor=cv.idven and  h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id  and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51)  GROUP BY h.idcli ),0) AS  MesPassado,
                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  
                            FROM hoje h, hist hi, inv i, produtos p  
                            WHERE   h.id > 1220000 and 
                                    YEAR(h.data)=YEAR(NOW()) AND 
                                    MONTH(h.data)=MONTH(now()) and 
                                    h.vendedor=cv.idven AND
                                    h.id=hi.pedido and 
                                    hi.isbn=i.id and 
                                    i.idcf=p.id and 

                                    h.idcli=c.id  AND 
                                    ( nop=27 OR nop=28 OR nop=51)  
                            GROUP BY h.idcli ),0) AS  compras_mes,

                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  FROM hoje h, hist hi, inv i, produtos p  WHERE  h.id > 1220000 and YEAR(h.data)=YEAR(NOW()) AND MONTH(h.data)=MONTH(now()) and DAY(h.data)=DAY(now()) and h.id=hi.pedido and hi.isbn=i.id and i.idcf=p.id and p.segmento <> 'parts' and h.idcli=c.id  AND ( nop=27 OR nop=28 OR nop=51) and h.vendedor=cv.idven GROUP BY h.idcli ),0) AS  compras_hoje,


                   /* (SELECT  SUM(h.valor) FROM hoje h WHERE  h.id > 1220000 and h.idcli=c.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW()) GROUP BY h.idcli) AS Ano, */

                    /* (SELECT  SUM(h.valor) FROM hoje h WHERE  h.id > 1220000   AND ( nop=27 OR nop=28 OR nop=51)  and h.idcli=c.id   and h.prazo<>15 AND YEAR(h.data)=YEAR(NOW())-1 GROUP BY h.idcli) AS AnoP,  */

                     (SELECT DATEDIFF(NOW(), ch.data_retorno)  FROM crm.`chamadas` ch WHERE ch.cliente_id=c.id and MONTH(data_retorno)>0 ORDER BY ch.id DESC LIMIT 1) AS agenda

            FROM mak.clientes_vendedores cv 
            LEFT JOIN mak.clientes c ON (cv.idcli=c.id)
            LEFT JOIN crm.rfm ON (rfm.clientes_id=cv.id)
            LEFT JOIN crm.empresas e ON (e.idcli=c.id)
            LEFT JOIN mak.users u ON (u.id=c.vendedor)
            WHERE cv.ativo=1  %s
            GROUP BY cv.id ,cv.idcli
            ORDER BY contact_rate DESC
            LIMIT 10
            ", $w );

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();        
        
        $view ='';
        if(!empty($result))
        {
            $theme='light';
            $result['theme'] = 2;
            $view = parent::tablefy( $result,$theme );
        }
        
        return $view;
                           
    }


}