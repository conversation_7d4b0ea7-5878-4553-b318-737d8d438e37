<?php

class Controller_Price extends Controller_Website
{
    // Constants for percentages and fixed costs
    const PIS_PERCENTAGE = 2.10;
    const COFINS_PERCENTAGE = 10.45;
    const ICMS_TAX_1 = 3.51;
    const ICMS_TAX_2 = 8.80;
    const DESEMBARACO_ADUANEIRO = 3036;
    const TAXA_SISCOMEX = 154.23;
    const FIXED_EXPENSE_PERCENTAGE = 5;
    const SEA_FREIGHT_FIXED_COST = 1800;
    const CONTAINER_WEIGHT_CAPACITY = 25000; // kg
    const CONTAINER_VOLUME_CAPACITY = 56; // kg
    const SEGURO = .001; // %


    public function before()
    {
        parent::before();
    }

    // Main pricing action method
   public function action_pricing()
    {
        $isbn = $this->request->param('division');

        if (!$isbn) {
            throw new HTTP_Exception_400('Invalid product ISBN');
        }

        // Get product inventory details
        $inventory = $this->get_inventory($isbn);

        // s($inventory);

        if (!$inventory) {
            throw new HTTP_Exception_404('Product not found');
        }

        // Calculate pricing factors based on product data
        $data = $this->calculate_pricing_factors($inventory);

        // Calculate different price ranges based on ICMS taxes
        $data['revendaFobValor'] = $this->calculate_revenda_fob($inventory, $data);

        // Use the view to render the table instead of jsonToTable
        $view = View::factory('pricing_table', ['data' => $data]);
        $this->response->body($view);
    }


    /**
     * Fetches the product inventory and related information from the database
     */
    private function get_inventory($isbn)
    {
        $sql = "
            SELECT
                i.id as Id, i.modelo as lev2, i.nome as xProduct, i.marca as xBrand,
                i.qtestq as qStock, i.revenda as Revenda, i.fob as vFob, i.peso as Peso,
                i.volume as Volume, pr.outras, pr.ncm as NCM, pr.ii,
                (SELECT freight FROM next
                 RIGHT JOIN shipments ON shipments.id = next.shipment
                 WHERE month(shipments.status) > 0 AND next.isbn = i.id ORDER BY status DESC LIMIT 1) as UltimoFrete,
                (SELECT usd_real FROM next
                 RIGHT JOIN shipments ON shipments.id = next.shipment
                 WHERE month(shipments.status) > 0 AND next.isbn = i.id  ORDER BY status DESC LIMIT 1) as UltimoDolar,
                (SELECT fob FROM next
                 RIGHT JOIN shipments ON shipments.id = next.shipment
                 WHERE month(shipments.status) > 0 AND next.isbn = i.id ORDER BY status DESC  LIMIT 1) as UltimoFob,
                   (SELECT DOLAR FROM Vars) as Dolar,
                 SP.*
            FROM inv i
            LEFT JOIN produtos pr ON (pr.id = i.idcf)
            LEFT JOIN NFE.TributacaoSP SP ON (SP.ncm = pr.ncm AND SP.state='SP')
            WHERE i.id = :isbn
            LIMIT 10";

        return DB::query(Database::SELECT, $sql)
                ->parameters([':isbn' => $isbn])
                ->execute()
                ->as_array()[0];
    }

    /**
     * Calculate pricing-related factors, such as ValorCIFR$ price, FOB price, and freight percentage.
     */
    private function calculate_pricing_factors($inventory)
    {
        $data = []; //$inventory;

        // Product taxes and fees



        $data['I - EspecificaçõesDoProduto'] = ' ';
        // $data['EspecificaçõesDoProduto'] = ' ';

        $data['NCM'] = $inventory['NCM'];
        $data['PesoUnidade'] = $inventory['Peso'];
        $data['VolumeUnidade'] = $inventory['Volume'];

        $cap = substr($inventory['NCM'],0,4);

        if('8452'==$cap)
        {
            $data['PesoUnidade'] = $inventory['Peso']+26;
            $data['VolumeUnidade'] = $inventory['Volume']+0.0695;
        }

        $data['PesoMaxContainer'] = self::CONTAINER_WEIGHT_CAPACITY;
        $data['VolumeMaxContainer'] = self::CONTAINER_VOLUME_CAPACITY;



        $data['II - USD'] = ' ';
        $data['ValorDolar'] = $inventory['Dolar'];

        $data['QuantidadeUnidadesPorContainer'] = $this->calculate_sets($inventory['NCM'], $data['PesoUnidade'],  $inventory['Volume']);
        $data['ValorFobUnidade'] = $inventory['vFob'];
        $data['ValorFobContainer'] =  round($inventory['vFob'] *  $data['QuantidadeUnidadesPorContainer'],2);
        $data['ValorFrete'] = self::SEA_FREIGHT_FIXED_COST ;


        $data['III - Aliquotas'] = ' ';
        // $data['Aliquotas'] = ' ';
        $data['AliquotaImpostoImportacao'] = (int)$inventory['ii'];
        $data['AliquotaIPI'] = (int)$inventory['ipi'];
        $data['AliquotaPIS'] = self::PIS_PERCENTAGE;
        $data['AliquotaCOFINS'] = self::COFINS_PERCENTAGE;

        // Calculate sets based on product weight
        // $data['PesoContainer']   =self::CONTAINER_WEIGHT_CAPACITY;
        // $data['VolumeContainer'] =self::CONTAINER_VOLUME_CAPACITY;


        $data['IV - CIF'] = ' ';
        // $data['CIF'] = ' ';
        // Calculate sea freight percentage and related costs
        $data['ValorFobR$'] =  round($data['QuantidadeUnidadesPorContainer'] * $inventory['vFob']*  $data['ValorDolar'],2);
        $data['ValorFreteR$'] = self::SEA_FREIGHT_FIXED_COST*  $data['ValorDolar'] ;
        $data['ValorSeguroR$'] = round($data['ValorFobR$']*self::SEGURO,2);
        $data['   '] = ' ';
        $data['ValorCIFR$'] = $data['ValorFobR$']+$data['ValorFreteR$']+$data['ValorSeguroR$'];

        $data['V - Valor Impostos Base '] = ' ';

        $data['ValorIPI'] = round(((int)$inventory['ipi']/100) * $data['ValorCIFR$'],2);
        $data['TaxaSiscomex'] = self::TAXA_SISCOMEX;
        $data['ValorCOFINS'] = round((self::COFINS_PERCENTAGE/100) *  $data['ValorCIFR$'],2);
        $data['ValorPIS'] = round((self::PIS_PERCENTAGE/100) *  $data['ValorCIFR$'],2);
        $data['ValorII'] = round(((int)$inventory['ii']/100) *  $data['ValorCIFR$'],2) ;

        // $data['      '] = ' ';

        $data['ValorTotalTributosDespesasI'] =  $data['ValorIPI'] +  $data['TaxaSiscomex'] +  $data['ValorCOFINS'] + $data['ValorPIS'] + $data['ValorII'];

        $data['VI -Valor Outras Despesas'] = ' ';


        $data['ValorServicoDesembaracoAduaneiro'] = self::DESEMBARACO_ADUANEIRO;
        $data['ValorTaxaExpediente'] = 788.23;
        $data['Desconsolidacao'] = 1800;
        $data['ValorMarinhaMecante'] = $inventory['UltimoFrete']*.25 *  $data['ValorDolar'];
        $data['ValorArmazenagem'] = 6000;
        $data['ValorIcmsTTD'] =   round(($data['ValorCIFR$']+ $data['ValorTotalTributosDespesasI'])*0.014,2);


        // $data[ '      '] = ' ';
        $data['ValorTotalTributosDespesasII'] =  $data['ValorServicoDesembaracoAduaneiro']+
                                                        $data['ValorTaxaExpediente'] +
                                                        $data['Desconsolidacao'] +
                                                        $data['ValorMarinhaMecante'] +
                                                        $data['ValorArmazenagem'] +
                                                        $data['ValorIcmsTTD'] ;

        $data['VII - Valor Despesas Extras'] = ' ';
        $data['TransporteInternoPortoCD'] = 6000;
        $data['OutrosCustosDiretos'] = 2000;

        $data['VIII'] = ' ';
        $data['TotalFreteSeguroTributosDespesas'] = round(   $data['ValorFreteR$']+
                                                                    $data['ValorSeguroR$']+
                                                                    $data['ValorSeguroR$']+
                                                                    $data['ValorTotalTributosDespesasI']  +
                                                                    $data['ValorTotalTributosDespesasII'] +
                                                                    $data['TransporteInternoPortoCD'] +
                                                                    $data['OutrosCustosDiretos'],2);
        $data['ValorTotalFobFreteSeguroTributosDespesas'] = round($data['TotalFreteSeguroTributosDespesas'] + $data['ValorFobR$'],2);

        $data['IndexTotalFobDespesas'] = round($data['ValorTotalFobFreteSeguroTributosDespesas']/  $data['ValorFobR$'] ,3);



        $data['ValorTotalSemPisCofinsIcmsIpi'] =  round( $data['TotalFreteSeguroTributosDespesas'] -  $data['ValorCOFINS']-  $data['ValorPIS']- $data['ValorIcmsTTD'] -  $data['AliquotaIPI'],2);

        $data['IndexTotalSemPisCofinsIcmsIpi'] = round(( $data['ValorTotalFobFreteSeguroTributosDespesas'] -
                                                                $data['ValorCOFINS']-
                                                                $data['ValorPIS']-
                                                                $data['ValorIcmsTTD'] -
                                                                $data['AliquotaIPI'] ) /
                                                                $data['ValorFobR$'],3) ;




        $data['IX'] = ' ';



        $data['Custo_Total_Produto_Individual'] =  round($data['ValorTotalFobFreteSeguroTributosDespesas'] /  $data['QuantidadeUnidadesPorContainer'],2);;


        $data['X'] = ' ';
             $data['IndexFrete'] = round(($data['ValorFreteR$'] / $data['ValorFobR$']) * 100, 1);
        $data['expensesPerc'] = self::FIXED_EXPENSE_PERCENTAGE;


        // // Calculate clearing percentage and prices
         $data['clearingPerc'] = $data['IndexFrete'] + $data['expensesPerc'] + $data['AliquotaImpostoImportacao'];
         $data['setFobPrice'] =  round($data['ValorDolar'] * $inventory['vFob'],2);
         $data['setCifPrice'] = round($data['ValorCIFR$']  / $data['QuantidadeUnidadesPorContainer'],2);
         $data['setClearPrice'] = round( $data['setFobPrice']* $data['IndexTotalSemPisCofinsIcmsIpi'],2);
         $data['setClearIdx'] = round($data['setClearPrice'] / $inventory['vFob'],2);

        return $data;
    }

    /**
     * Calculate the number of sets based on NCM and product weight
     */
    private function calculate_sets($ncm, $weight, $volume)
    {

        // Adjust container capacity based on NCM type
        $setsBasedOnVolume =0;
        $setsBasedOnWeight =0;

        if( $volume>0 )   $setsBasedOnVolume= self::CONTAINER_VOLUME_CAPACITY / ($volume);
        if( $weight>0 )   $setsBasedOnWeight= self::CONTAINER_WEIGHT_CAPACITY / ($weight);

        $numberOfSets = 1;
        if($setsBasedOnWeight> 0 ) $numberOfSets = $setsBasedOnWeight;
        if($setsBasedOnVolume> 0 ) $numberOfSets = $setsBasedOnVolume;

        if($setsBasedOnWeight> 0 and  $setsBasedOnVolume>0 ) $numberOfSets = min($setsBasedOnVolume, $setsBasedOnWeight);

        // Ensure the result is at least 1 (you can't have 0 sets)
        return max(round($numberOfSets,0), 1);


    }

    /**
     * Calculates the "revenda FOB" value based on ICMS rates and different markups.
     */
    private function calculate_revenda_fob($inventory, $data)
    {
        $revendaFobValues = [];

        $icmsRates = [self::ICMS_TAX_1, self::ICMS_TAX_2];

        foreach ($icmsRates as $icmsPerc) {
            $taxPerc = $data['AliquotaPIS'] + $data['AliquotaCOFINS'] + $icmsPerc;

            // Calculate FOB values for different markup percentages
            for ($markup = 17; $markup <= 30; $markup++) {
                $idx = round($data['setClearIdx'] / (1 - (($taxPerc + $markup) / 100)), 2);
                $revendaFobValues[$icmsPerc][$markup] = round($idx * $inventory['vFob'], 2);
            }
        }

        return $revendaFobValues;
    }

    /**
     * Converts a JSON structure to an HTML table for output.
     */
    private function jsonToTable($data)
    {
        $table = '<table class="table table-bordered">';
        foreach ($data as $key => $value) {
            $table .= '<tr>';
            $table .= '<th>' . htmlspecialchars($key) . '</th><td>';

            if (is_array($value)) {
                $table .= $this->jsonToTable($value); // Recursive call for nested arrays
            } else {
                $table .= htmlspecialchars($value);
            }

            $table .= '</td></tr>';
        }
        $table .= '</table>';
        return $table;
    }


}
