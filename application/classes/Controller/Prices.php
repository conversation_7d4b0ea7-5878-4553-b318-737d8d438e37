
<?php

class Controller_Prices extends Controller_Website
{

    const PIS_PERCENTAGE = 1.65;
    const COFINS_PERCENTAGE = 7.6;
    const ICMS_TAX_1 = 3.51; //4;
    const ICMS_TAX_2 = 8.88; //18;
    const FIXED_EXPENSE_PERCENTAGE = 5;
    const SEA_FREIGHT_FIXED_COST = 100; // If it's always 100 in the calculation
    const CONTAINER_WEIGHT_CAPACITY =25000; // kg

   
   public function before()
    {
         parent::before();
         
    }

    public function action_index()
    {
        $segid = $this->request->param('division');
        $q = $this->request->param('xtras');
      
        
        if($segid==1)        {
            $data = self::get('machines');
            $data['caption']=   self::getCaptionLinks('machines');
            return $this->prepareResponse($data);
        }
        
        if($segid==2)        {
            $data = self::get('bearings',$q);
            $data['caption']=   self::getCaptionLinks('bearings'); 
            return $this->prepareResponse($data);
        }
        
        if($segid==3)        {
            $data = self::get('parts');
            $data['caption']=   self::getCaptionLinks('parts'); 
            return $this->prepareResponse($data);
        }
        
        if($segid==5)        {
            $data = self::get('auto');
            $data['caption']=   self::getCaptionLinks('auto'); 
            return $this->prepareResponse($data);
        }


    }
  
      private function prepareResponse($data)
    {
        $theme = 'datatables';
        $view = parent::tablefy($data);
        $response = parent::pagefy($view, $theme);
        $this->response->body($response);
        
        
        // $theme='datatables';
        // $view = '';
        
      
        // $color['Diff'][0]['class'] = ' text-red-400 text-lg font-bold';
        // $color['Diff'][0]['rule']  = ' < 1 ';
        // $color['Diff'][1]['class'] = ' text-blue-400 text-md font-bold';
        // $color['Diff'][1]['rule']  = ' > 1 ';
        // // $color['Diff'][2]['class'] = ' text-blue-600 text-md  font-bold';
        // // $color['Diff'][2]['rule']  = ' > 75';
        // // $color['Diff'][3]['class'] = ' text-green-700 text-md  font-bold';
        // // $color['Diff'][3]['rule']  = ' > 100 ';
        
        
        // $color['Modelo']['class'] = ' text-blue-500 text-lg font-bold';
        // $color['M17']['class'] = ' text-green-600 text-lg font-bold';
        // $color['M16']['class'] = ' text-blue-500 text-lg font-bold';
        // $color['M15']['class'] = ' text-blue-400 text-lg font-bold';
        // $color['M14']['class'] = ' text-orange-500 text-base font-bold';
        // $color['M13']['class'] = ' text-orange-400 text-base font-bold';
        // $color['M12']['class'] = ' text-orange-300 text-base font-bold';
        // $color['M11']['class'] = ' text-orange-200 text-base font-bold';
        // $color['M10']['class'] = ' text-orange-600 text-base font-bold';
        // $color['M9']['class'] = ' text-red-700 text-sm font-bold';
        // $color['M8']['class'] = ' text-red-600 text-xs font-bold';
        // $color['M7']['class'] = ' text-red-500 text-xs font-bold';
        // $color['M6']['class'] = ' text-red-400 text-xs font-bold';
        // $color['M5']['class'] = ' text-red-300 text-xs font-bold';
        
        // $color['VidaEstoqueMeses'][0]['class'] = ' text-green-400 text-xs font-bold';
        // $color['VidaEstoqueMeses'][0]['rule']  = ' > 0 ';
        // $color['VidaEstoqueMeses'][1]['class'] = ' text-blue-400 text-md font-bold';
        // $color['VidaEstoqueMeses'][1]['rule']  = ' > 5 ';
        // $color['VidaEstoqueMeses'][2]['class'] = ' text-orange-600 text-md  font-bold';
        // $color['VidaEstoqueMeses'][2]['rule']  = ' > 10';
        // $color['VidaEstoqueMeses'][3]['class'] = ' text-red-700 text-lg  font-bold';
        // $color['VidaEstoqueMeses'][3]['rule']  = ' > 20 ';
        
        // $color['Subtotal'][0]['class'] = ' text-red-400 text-xs font-bold';
        // $color['Subtotal'][0]['rule']  = ' > 10000 ';
        // $color['Subtotal'][1]['class'] = ' text-yellow-600 text-sm font-bold';
        // $color['Subtotal'][1]['rule']  = ' > 100000 ';
        // $color['Subtotal'][2]['class'] = ' text-blue-600 text-base  font-bold';
        // $color['Subtotal'][2]['rule']  = ' > 500000';
        // $color['Subtotal'][3]['class'] = ' text-green-700 text-lg  font-bold';
        // $color['Subtotal'][3]['rule']  = ' > 1000000 ';
        
        // $data['color'] = $color;
        
        // //// Editable
         
        //  $edt['Nome'] = 'mak.inv|nome|id|_ISBN';
        //  $edt['Revenda'] = 'mak.inv|revenda|id|_ISBN';
        //  $edt['Idx'] = 'mak.inv|costindex|id|_ISBN';
        //  $data['editable'] = $edt;
        
        // // $idven =  $this->request->query('idven');
        // // $baseurl='/metrics/customers/index';
        
        
            
        // // s( $this->pagination);
        // // $data['pagination'] = $this->pagination;
        // $data['theme'] = 3;
        
        // // $fmt['V']= $fmt['V22']= $fmt['V21']= $fmt['V20']= $fmt['V19']= $fmt['V18']= $fmt['V17']= $fmt['V16'] = '0ms';
        //  $fmt['Matriz']=$fmt['Avg90']=$fmt['Subtotal'] = '0br';
        // // $fmt['V9']= $fmt['V8']= $fmt['V7']= $fmt['V6']= $fmt['V5']= $fmt['V4'] = '0ms';
        // // $fmt['AGE'] = '0us'; 
        //  $data['format'] = $fmt;

        // $view.= parent::tablefy( $data );
        
        // s($data['pagination']);
       
        // $response = parent::pagefy($view,$theme);

        // $this->response->body($response);
    }

    private function getCaptionLinks($type)
    {
        $captions = [];

        switch ($type) {
            case 'auto':
                $captions[] = ['link' => '/metrics/portfolio/index/5/?modelo=MTR-CV', 'title' => 'MTR-CV'];
                break;
            case 'parts':
                $captions[] = ['link' => '/metrics/portfolio/index/3/?modelo=btr', 'title' => 'btr'];
                break;
            case 'bearings':
                $captions[] = ['link' => '/metrics/portfolio/index/2/?modelo=CS608', 'title' => 'CS608'];
                break;
            case 'machines':
                $captions = [
                    ['link' => '/metrics/prices/index/1/?marca=sewpower', 'title' => 'Sewpower'],
                    ['link' => '/metrics/prices/index/1/?marca=Dollor', 'title' => 'Dollor'],
                    // Add other machines...
                ];
                break;
        }
        return $captions;
}

    private function get($segmento='machines')
    {
      
        $having="";
        $where="";
        $this->group="hi.isbn";
        
        // if($this->request->query('not')) $where.= sprintf(" AND %s ", $this->request->query('not'));
        // echo $where;
        // die();
        
        if($this->request->query('blue')) $where.= sprintf(" AND  r.blue > %s", $this->request->query('blue'));
        
        // if($this->request->query('rrh')) $where.= sprintf(" AND  r.rolemak_ranking_history >0 AND r.rolemak_ranking_history <= %s ", $this->request->query('rrh'));
        if($this->request->query('rrn')) $where.= sprintf(" AND  r.rolemak_ranking_now >0 AND r.rolemak_ranking_now <= %s ", $this->request->query('rrn'));
        if($this->request->query('gr')) $where.= sprintf(" AND  r.global_ranking >0 AND r.global_ranking <= %s ", $this->request->query('gr'));
        if($this->request->query('meso')) $where.= sprintf(" AND meso LIKE '%s' ", $this->request->query('meso').'%');
        if($this->request->query('estado')) $where.= sprintf(" AND estado='%s' ", $this->request->query('estado'));
        if($this->request->query('regiao')) $where.= sprintf(" AND regiao='%s' ", $this->request->query('regiao'));
        if($this->request->query('cidade'))
        {
            if(substr($this->request->query('cidade'),0,1)=='!')
            {
                $where.= sprintf(" AND cidade != '%s' ",  ltrim($this->request->query('cidade'), '!'));
            }else{
                $where.= sprintf(" AND cidade='%s' ", $this->request->query('cidade'));
            }
        }
        if($this->request->query('nome')) $where.= sprintf(" AND c.nome LIKE '%s' ", '%'.$this->request->query('nome').'%');
        if($this->request->query('desc')) $where.= sprintf(" AND i.nome LIKE '%s' ", '%'.$this->request->query('desc').'%');
        if($this->request->query('nfe')) $where.= sprintf(" AND nfe LIKE '%s' ", $this->request->query('nfe').'%');
        if($this->request->query('nick')) $where.= sprintf(" AND u.nick LIKE '%s' ", $this->request->query('nick').'%');
        if($this->request->query('marca')) $where.= sprintf(" AND i.marca LIKE '%s' ", $this->request->query('marca').'%');
        if($this->request->query('modelo')) $where.= sprintf(" AND i.modelo LIKE '%s' ", '%'.$this->request->query('modelo').'%');
        if($this->request->query('modinit')) $where.= sprintf(" AND i.modelo LIKE '%s' ", $this->request->query('modinit').'%');
        if($this->request->query('embalagem')) $where.= sprintf(" AND i.embalagem LIKE '%s' ", $this->request->query('embalagem'));
        if($this->request->query('data')) $where.= sprintf(" AND h.data LIKE '%s' ", $this->request->query('data').'%');
        if($this->request->query('cep')) $where.= sprintf(" AND c.cep LIKE '%s' ", $this->request->query('cep').'%');
        if($this->request->query('group')) $this->group= sprintf(" %s ", $this->request->query('group'));
        // if($this->request->query('vols')) $having= sprintf(" Having vols %s ", $this->request->query('vols'));
        if($this->request->query('recencia')) $having= sprintf(" Having `Dias` BETWEEN %s ", $this->request->query('recencia'));
        // if($this->request->query('blue')) $having= sprintf(" Having `IdxBlue` BETWEEN %s ", $this->request->query('blue'));
        // if($this->request->query('a2021')) $having= sprintf(" Having `QtA2022` < 1 ");
   						       
        //  $sql = sprintf("SELECT c.id _IDCLI,
        //                         i.id _ISBN,
        //                         i.costindex,
        //                         i.modelo,
        //                         i.marca,
        //                         i.nome,
        //                         i.fob,
        //                         i.revenda,
        //                         u.nick as VD,
        //                         COUNT(DISTINCT c.id) idclis,  
        //                         c.nome as Cliente, 
        //                         c.Cidade,c.Estado, 
        //                         LEFT(c.Cep,4) Cep, 
        //                         Meso,
        //                         NomeMeso,
        //                         NomeMicro,
        
         
							 //          (
							 //  (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE i.id=e1.`ProdutoPOID`)
							 //  +
							 //  (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE i.id=e3.`ProdutoPOID`)
							 //  +
							 //  (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE i.id=e5.`ProdutoPOID`)
							 //  +
							 //  (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE i.id=e6.`ProdutoPOID`)
							 //  +
							 //  (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE i.id=e6t.`ProdutoPOID`)

						  // ) as Estoque,
						   
        //                         SUM(hi.valor_base*hi.quant) as V,
        //                         SUM(IF(YEAR(h.data)=2021,hi.quant,0)) as Q21, 
        //                         SUM(IF(YEAR(h.data)=2022,hi.quant,0)) as Q22, 
                            
        //                         SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=1,hi.quant,0)) as Q1,
        //                         SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=2,hi.quant,0)) as Q2, 
        //                         SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=3,hi.quant,0)) as Q3, 
        //                         SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=4,hi.quant,0)) as Q4, 
        //                         SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=5,hi.quant,0)) as Q5, 
        //                         SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=6,hi.quant,0)) as Q6, 
        //                         SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=7,hi.quant,0)) as Q7, 
        //                         SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=8,hi.quant,0)) as Q8, 
        //                         SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=9,hi.quant,0)) as Q9, 
        //                         SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=10,hi.quant,0)) as Q10, 
                                
        //                         SUM(IF(YEAR(h.data)=2022,hi.valor_base*hi.quant,0)) as V22, 
        //                         SUM(IF(YEAR(h.data)=2021,hi.valor_base*hi.quant,0)) as V21, 
        //                         SUM(IF(YEAR(h.data)=2020,hi.valor_base*hi.quant,0)) as V20, 
        //                         SUM(IF(YEAR(h.data)=2019,hi.valor_base*hi.quant,0)) as V19

                                
        //                         FROM inv i  
        //                         LEFT JOIN hist hi ON (hi.isbn=i.id) 
        //                         LEFT JOIN mak.hoje h ON (h.id=hi.pedido) 
        //                         LEFT JOIN produtos p ON (p.id=i.idcf)
        //                         LEFT JOIN clientes c ON (c.id=h.idcli) 
        //                         LEFT JOIN users u ON (u.id=c.vendedor)
        //                         LEFT JOIN crm.rfm r ON (r.clientes_id=c.id)
        //                         LEFT JOIN estados e ON (e.uf=c.estado)
        //                         LEFT JOIN estatisticas.vMun m ON (m.NomeMunic=c.cidade )
        //                         WHERE c.vip< 9 AND hi.valor_base>0 AND nop IN (27,28,51,76) AND m.UF=e.codigo_ibge AND p.segmento='machines' AND h.id > 812191 %s 
                                
        //                         GROUP BY %s
                                
                                
                                
                                
                                
        //                         LIMIT 5" ,  $where, $this->group  );
                                
        $sql = sprintf("SELECT
                                i.id _ISBN,
                                i.modelo as Modelo,
                                i.embalagem as Emb,
                                i.marca as Marca,
                                i.nome as _Descrição,
                                '' Action,
                                '' Status,
                                ( qtestq+qtestq2 ) as Matriz,
							   
							   (SELECT IFNULL(EstoqueDisponivel, 0)+IFNULL(EstoqueReservado, 0) FROM mak_0370.Estoque e3 WHERE i.id=e3.`ProdutoPOID`)
							   +
							   (SELECT IFNULL(EstoqueDisponivel, 0)+IFNULL(EstoqueReservado, 0) FROM mak_0885.Estoque e8 WHERE i.id=e8.`ProdutoPOID`) as BarraFunda,
							   
							   (SELECT IFNULL(EstoqueDisponivel, 0)+IFNULL(EstoqueReservado, 0) FROM mak_0613.Estoque e6 WHERE i.id=e6.`ProdutoPOID`) 
							   +
							   (SELECT IFNULL(EstoqueDisponivel, 0)+IFNULL(EstoqueReservado, 0) FROM mak_0613.Estoque_TTD_1 e6t WHERE i.id=e6t.`ProdutoPOID`) as Blu,
          
                               (SELECT IFNULL(EstoqueDisponivel, 0)+IFNULL(EstoqueReservado, 0) FROM mak_0966.Estoque e9 WHERE i.id=e9.`ProdutoPOID`) 
							   +
							   (SELECT IFNULL(EstoqueDisponivel, 0)+IFNULL(EstoqueReservado, 0) FROM mak_0966.Estoque_TTD_1 e9t WHERE i.id=e9t.`ProdutoPOID`) as Blu2,
							 
							 
							    ''  as EstoqueTotal,
						      (SELECT   sum(n.quant) 
						        FROM next n
                                LEFT JOIN  shipments sh on (sh.id=n.shipment)	
                                LEFT JOIN  next_stage ns on (ns.id=n.stage)		  
				                WHERE   n.quant>0 AND
                                    n.state <>9 AND 
                                    MONTH( sh.status)=0 AND
                                    n.isbn = i.id AND
                                    ns.stage='shipping'
                                GROUP BY n.isbn) as EmTranSito,
                            

                                
                                
                           ( SELECT SUM(n.quant)  FROM next n ,shipments s WHERE  s.id=n.shipment AND month(s.status) =0  and  n.isbn =i.id  and n.state<>9 GROUP BY i.id
							) AS Next,
							                                round((SELECT 	SUM(hist.quant) as qProd 
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 90 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=i.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                        	        GROUP BY hist.isbn)/3,0)  AS Avg90,
 
                                 round((SELECT 	SUM(hist.quant) as qProd 
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 365 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=i.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                        	        GROUP BY hist.isbn)/12,0)  AS Avg365,                       	        
                        	  
                                 round((SELECT 	SUM(hist.quant) as qProd 
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 1095 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=i.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                        	        GROUP BY hist.isbn)/36,0)  AS Avg3a,      
                        	        
							(SELECT  TO_DAYS(NOW()) - TO_DAYS(arrival) 
						        FROM next n
                                LEFT JOIN  shipments sh on (sh.id=n.shipment)	
                                LEFT JOIN  next_stage ns on (ns.id=n.stage)		  
				                WHERE   n.quant>0 AND
                                    n.state <>9 AND 
                                    MONTH( sh.status)=0 AND
                                    n.isbn = i.id AND
                                    ns.stage='shipping'
                              ORDER BY status desc  
								  LIMIT 1)
                                AS ChegaDias,  

                                 '' as Ações,
                                i.fob as Fob,
                                
                                (SELECT fob
								  FROM   next 
								  RIGHT  JOIN shipments ON shipments.id=next.shipment 
								  WHERE  month(shipments.status)>0 and next.isbn=i.id and next.state <>9 and next.quant >0
								  ORDER BY status desc  
								  LIMIT 1) as UltimoFob,
								  
								(SELECT  fob 
						        FROM next n
                                LEFT JOIN  shipments sh on (sh.id=n.shipment)	
                                LEFT JOIN  next_stage ns on (ns.id=n.stage)		  
				                WHERE   n.quant>0 AND
                                    n.state <>9 AND 
                                    MONTH( sh.status)=0 AND
                                    n.isbn = i.id AND
                                    ns.stage='shipping'
                              ORDER BY status desc  
								  LIMIT 1)
                                AS NextFob,    
                                

								  
                                i.revenda as Revenda,
                                

                        	 round((SELECT 	SUM(hist.quant) as qProd 
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 90 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=i.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                        	GROUP BY hist.isbn)/3,0)
                            AS VidaEstoqueMeses,
							                        	
							                        	
                        		
                        		(SELECT TO_DAYS(NOW()) - TO_DAYS(hoje.data)+1
					            FROM  hoje,hist 
						        WHERE hoje.id=hist.pedido AND  hist.isbn =i.id  AND hoje.nop in (27,28,51,76)
						        GROUP BY hoje.data 
						        ORDER BY hist.id DESC 
						        LIMIT 1 ) RecênciaDias,
				    		
				    		
				    		    (SELECT TO_DAYS(now()) - TO_DAYS(shipments.status) as dias
								  FROM   next 
								  RIGHT  JOIN shipments ON shipments.id=next.shipment 
								  WHERE  month(shipments.status)>0 and next.isbn=i.id and next.state <>9 and next.quant >0
								  ORDER BY dias asc 
								  LIMIT 1) as IdadeDias,
								  		
				    		(SELECT 	count(isbn)
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 30 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=i.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                        	GROUP BY hist.isbn) as Freq30,
 
				    		(SELECT 	count(isbn)
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 90 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=i.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                        	GROUP BY hist.isbn) as Freq90,
                        	
                        	(SELECT 	count(isbn)
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 180 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=i.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                        	GROUP BY hist.isbn) as Freq180,
                        

                        	(SELECT 	count(isbn)
                        			FROM 	hist,hoje	
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 365 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=i.id AND				
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0 
                        	GROUP BY hist.isbn) as Freq365                     	
								  
                                
                           
						   
						   
                                
                                FROM inv i  
                               
                                LEFT JOIN produtos p ON (p.id=i.idcf)

    
                                WHERE revenda>0 AND p.segmento='%s' %s 
                                
                                
                                
                                
                                LIMIT 1000" ,$segmento,  $where );                                
                                
                                // die($sql);
   
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute()->as_array();
           
            // $vt = 0;
            // foreach ($result as $key => $row) 
            // {
            //     // $vt+=$row["V22"];
            // }
            
             foreach ($result as $key => $row) 
            {
                
                if (is_null($row["Avg90"]) or $row["Avg90"]<1)  $result[$key]["Avg90"]=0;
                
               
                
                $result[$key]['EstoqueTotal']=0;
                $result[$key]['EstoqueTotal']+= (int) $result[$key]['Matriz']+ (int) $result[$key]['BarraFunda']+ (int) $result[$key]['Blu']+ (int) $result[$key]['Blu2'];
                 
                if($result[$key]["Avg90"] > 0 ) $result[$key]["VidaEstoqueMeses"] = round($result[$key]['EstoqueTotal']/$result[$key]["Avg90"],0);
                  
                 $result[$key]['Subtotal'] = round(($result[$key]['EstoqueTotal']*$row["Fob"]*8),2);
                 
                $result[$key]['Status']=$result[$key]['PreçoNext']=$result[$key]['IdxAtual']='';
                if($row["UltimoFob"]>0) $result[$key]['IdxAtual'] = round(($row["Revenda"]/$row["UltimoFob"]),2);
                if($row["NextFob"]>0) $result[$key]['PreçoNext'] = round(($row["NextFob"]*16),2);
                
                if($result[$key]['EstoqueTotal']==0) $result[$key]['IdadeDias'] = 0;
                
                
                
                
                
           
                
            }
            
            foreach ($result as $key => $row) 
            {
                $curve='';
                if($row["Freq365"]==0 and $row["Subtotal"]>0 ) $curve= 'Demanda inexistente';
                if($row["Freq365"]>0) $curve= 'Demanda baixa demais';
                if($row["Freq180"]>0) $curve= 'Demanda muito baixa';
                if($row["Freq90"]>0) $curve= 'Demanda baixa';
                if($row["Freq30"]>0) $curve= 'Demanda moderada';
                if($row["Freq30"]>10) $curve= 'Demanda boa';
                if($row["Freq30"]>22) $curve= 'Demanda alta';
                
                
                if($row["Subtotal"]<20000 ) $amount= 'Valor baixo';
                if($row["Subtotal"]>20000 ) $amount= 'Valor razoável';
                if($row["Subtotal"]>50000 ) $amount= 'Valor moderado';
                if($row["Subtotal"]>100000 ) $amount= 'Valor alto';
                if($row["Subtotal"]>500000 ) $amount= 'Valor bem alto';
                if($row["Subtotal"]>1000000 ) $amount= 'Valor gigantesco ';
                if($row["Subtotal"]==0 ) $amount= '';
                
                $excess='';
              
                if($row["VidaEstoqueMeses"]>0 ) $excess= 'Estoque baixo';
                if($row["VidaEstoqueMeses"]>3 ) $excess= 'Estoque ok';
                if($row["VidaEstoqueMeses"]>6 ) $excess= 'Em Excesso Relativo';
                if($row["VidaEstoqueMeses"]>6 and $row["RecênciaDias"]==0 ) $excess= 'Em Excesso Absoluto';
                
                $age='';
                if($row["IdadeDias"]>30) $age= 'Estoque Recente';
                 if($row["IdadeDias"]>90) $age= 'Estoque em Alerta';
                                 if($row["IdadeDias"]>180) $age= 'Estoque Antigo';
                if($row["IdadeDias"]>365) $age= 'Estoque Antigo Demais';

               
   
                if($row["IdadeDias"]<30 and !empty($row["IdadeDias"])) $age= 'Estoque Novo';
               
                // if($row["IdadeDias"]==0 and is_null($row["RecênciaDias"])) $age= 'Produto Novo';
                 if(is_null($row["IdadeDias"])) $age= '';
                
                
                if($row["RecênciaDias"]==0 and $row["IdadeDias"]>180 and $row["EstoqueTotal"]>0) $age= 'Faz tempo que não vende';
                
                // if($row["IdadeDias"]>365 and $row["EstoqueTotal"]>0) $result[$key]['Status'].= ' Estoque Antigo';
                // if($row["IdadeDias"]<90 and $row["EstoqueTotal"]>0) $result[$key]['Status'].= ' Estoque Novo';
                
                $qty='';
                if($row["EstoqueTotal"]==0) $qty= ' Sem estoque';
                
                $action = '';
                if($excess=='Estoque baixo' and $curve== 'Demanda alta' and $result[$key]['Next']==0)   $result[$key]['Action']= 'Colocar pedidos urgentemente!';
                
                $vars = [];
                $vars[] = str_replace(' ','-',$age);
                $vars[] = str_replace(' ','-',$excess);
                $vars[] = str_replace(' ','-',$amount);
                $vars[] = str_replace(' ','-',$curve);
                $vars[] = str_replace(' ','-',$qty);
                // $vars[] = str_replace(' ','-',$action);
                
                
                
                
                $result[$key]['Status'] = implode('<br>', $vars);
                
            }

            // s($result);
            
            
            // $rv = self::chart_prepare($result);
            //  //s($rv);
            //  //die();
            // return $rv;
            // // $rv = self::classify($result);
            
            
            //  die();
            
         return $result ;
        
    }
    
    function classify($result)
    {
        $v  = array_column($result, 'V22');
        array_multisort($v, SORT_DESC, $result);
        
        // for ($i=1; $i<=12;$i++)
        // {
        //     $func = 'X'.$i;  
            
        //     foreach ($result as $key => $row) 
        //     {    
        //         $rank = $row[$func];
        //         if($row[$func]==0) $rank='-';
        //          $rv[$row['_ISBN']]['X'.$i] = $rank;
        //          $q[$row['_ISBN']][]=$row[$func];
                 
        //     }
    
        // }
        
        // foreach ($result as $key => $row) 
        // {
        //     $rv[$row['_ISBN']]['X2021'] =  ($row['Q21']<1) ? '-' : $row['Q21'];
        // }


         
        foreach ($result as $key => $row) 
        {
           
          
            $rv[$row['_ISBN']]['_ISBN'] = $row['_ISBN'];
            $rv[$row['_ISBN']]['FOB'] = $row['fob'];
            $rv[$row['_ISBN']]['Estoque'] = $row['Estoque'];
            $rv[$row['_ISBN']]['Subtotal'] = $row['Estoque']*$row['fob']*_DOLAR*$row['costindex'];
            $fob = $row['fob'];
            $idx = $row['costindex'];
            $dolar = _DOLAR;
            $costa =  round($fob*$idx*$dolar,0);
            
            $costb = $costa/.85;
            $TaxEven = round($costb,0);
            
            
            $rv[$row['_ISBN']]['Marca'] = $row['marca'];
            $rv[$row['_ISBN']]['Modelo'] = $row['modelo'];
            $rv[$row['_ISBN']]['Nome'] = $row['nome'];
            $rv[$row['_ISBN']]['Revenda'] = $row['revenda'];
            $rv[$row['_ISBN']]['10%Avista'] = $row['revenda']*.85;
            $rv[$row['_ISBN']]['5%Em-5x'] = $row['revenda']*.90;
            $rv[$row['_ISBN']]['Diff'] = 0;
            for ($i=20; $i>=17;$i--)
            {
                $rv[$row['_ISBN']]['M'.$i] = round($costb/((100-$i)/100),-1);
            }
            
            if ($rv[$row['_ISBN']]['M17']>0) $rv[$row['_ISBN']]['Diff'] = round($row['revenda']/$rv[$row['_ISBN']]['M17'],2);

            $rv[$row['_ISBN']]['Clientes'] = $row['idclis'];
            
            $rv[$row['_ISBN']]['Q2022'] =  ($row['Q22']<1) ? '-' : $row['Q22'];
            
            $rv[$row['_ISBN']]['V'] =  round($row['V']/1000,0).'K';
            // $rv[$row['_ISBN']]['V22'] = ($row['V22']< 1) ? '-' :  round($row['V22']/1000,0).'K';
            // $rv[$row['_ISBN']]['PC'] =  ($row['PC']<= 0) ? '-' : round($row['PC'],1).'%';
            
            $rv[$row['_ISBN']]['Idx'] = $row['costindex'];
        }

           
        // for ($i=1; $i<=10;$i++)
        // {
        //     $func = 'Q'.$i;  
            
        //     foreach ($result as $key => $row) 
        //     {    
        //         $rank = $row[$func];
        //         if($row[$func]==0) $rank='-';
        //          $rv[$row['_ISBN']]['Q'.$i] = $rank;
        //          $q[$row['_ISBN']][]=$row[$func];
                 
        //     }
    
        // }
        
        // foreach ($q as $key => $row) 
        // {
        //     $rv[$key]['Mean']      = round(Phpml\Math\Statistic\Mean::arithmetic($row),0); 
        //     $rv[$key]['Deviation']= round(Phpml\Math\Statistic\StandardDeviation::population($row),0); 
        // }
 
          
        // for ($i=22; $i>=19;$i--)
        // {
        //     $func = 'V'.$i;  
        //     $var  = array_column($result, $func);
        //     array_multisort($var, SORT_DESC, $result);
        //     foreach ($result as $key => $row) 
        //     {    
        //         $rank = $key+1;
        //         if($row[$func]==0) $rank='-';
        //          $rv[$row['_ISBN']]['A'.$i] = $rank;
        //         //  $info['rankh'] = $rv[$row['_ISBN']]['rTotal'];
        //          $info['rankn'] = $rv[$row['_ISBN']]['A22'];
        //          $info['idcli'] = $row['_ISBN'];
        //         //  self::store($info);
        //     }
            
        // }
 
  foreach ($rv as $key => $row) 
        {
            $resp[] = $row; 
            
        }
        
        // d($rv);
        // die();
        return $resp;
        
    } 

    public function inv($cProd)
	{		
		 $sql = sprintf("SELECT     
      						i.id as Id,
							i.modelo as lev2,
							i.id AS seo2,
							i.id as cProduct,
							i.modelo as mProduct,
							i.nome as xProduct,
							i.marca as xBrand,
							i.qtestq as qStock,
							i.revenda as Revenda,
							i.fob as vFob,
							i.peso as Peso,
							i.peso as Weight,
							i.volume as Volume,
							pr.icms,
							pr.ipi,
							pr.ii,
							pr.pis,
							pr.cofins,
							pr.outras,
							pr.ncm as NCM,
																		         (SELECT freight
								  FROM   next 
								  RIGHT  JOIN shipments ON shipments.id=next.shipment 
								  WHERE  month(shipments.status)>0 and next.isbn=i.id and next.state <>9 and next.quant >0
								  ORDER BY status desc  
								  LIMIT 1) as UltimoFrete,
														         (SELECT usd_real
								  FROM   next 
								  RIGHT  JOIN shipments ON shipments.id=next.shipment 
								  WHERE  month(shipments.status)>0 and next.isbn=i.id and next.state <>9 and next.quant >0
								  ORDER BY status desc  
								  LIMIT 1) as UltimoDolar,
		       
							         (SELECT fob
								  FROM   next 
								  RIGHT  JOIN shipments ON shipments.id=next.shipment 
								  WHERE  month(shipments.status)>0 and next.isbn=i.id and next.state <>9 and next.quant >0
								  ORDER BY status desc  
								  LIMIT 1) as UltimoFob
		       
						FROM  inv i	
						LEFT JOIN produtos pr ON (pr.id=i.idcf)				
					
						WHERE 	
							  i.id=%s
							  
								
					
						GROUP BY i.id ORDER BY   i.modelo  ", $cProd )   ;
						

		$result = $this->action_connect_SELECT($sql);	
		
		return $result[0];
		
		}	

    public function action_pricing()
    {
        $isbn = $this->request->param('division');
        
        // Get product inventory details
        $inventory = $this->get_inventory($isbn);
    
        // Calculate pricing factors based on product data
        $pricingData = $this->calculate_pricing_factors($inventory);
        
        // Calculate different price ranges based on ICMS taxes
        $pricingData['revendaFobValor'] = $this->calculate_revenda_fob($inventory, $pricingData);
    
        // Display the table
        echo $this->jsonToTable($pricingData);
    }
    
    /**
     * Fetches the product inventory and related information from the database
     */
    private function get_inventory($isbn)
    {
        $sql = "
            SELECT i.id as Id, i.modelo as lev2, i.nome as xProduct, i.marca as xBrand,
                   i.qtestq as qStock, i.revenda as Revenda, i.fob as vFob, i.peso as Peso,
                   i.volume as Volume, pr.icms, pr.ipi, pr.ii, pr.pis, pr.cofins, pr.outras, pr.ncm as NCM,
                   (SELECT freight FROM next RIGHT JOIN shipments ON shipments.id = next.shipment 
                    WHERE month(shipments.status) > 0 AND next.isbn = i.id LIMIT 1) as UltimoFrete,
                   (SELECT usd_real FROM next RIGHT JOIN shipments ON shipments.id = next.shipment 
                    WHERE month(shipments.status) > 0 AND next.isbn = i.id LIMIT 1) as UltimoDolar,
                   (SELECT fob FROM next RIGHT JOIN shipments ON shipments.id = next.shipment 
                    WHERE month(shipments.status) > 0 AND next.isbn = i.id LIMIT 1) as UltimoFob
            FROM inv i
            LEFT JOIN produtos pr ON (pr.id = i.idcf)
            WHERE i.id = :isbn
            GROUP BY i.id
            ORDER BY i.modelo
            LIMIT 1";
    
        return DB::query(Database::SELECT, $sql)
                ->parameters([':isbn' => $isbn])
                ->execute()
                ->as_array()[0];
    }
    
    /**
     * Calculate pricing-related factors, such as the CIF price, set clear price, and freight percentage.
     */
    private function calculate_pricing_factors($inventory)
    {
        $pricingData = [];
    
        // Constants for calculation
        $icmsRates = [self::ICMS_TAX_1, self::ICMS_TAX_2];
        
        // Set factors related to the product's FOB, taxes, and freight
        $pricingData['NCM'] = $inventory['NCM'];
        $pricingData['iiPerc'] = (int)$inventory['ii'];
        $pricingData['ipiPerc'] = (int)$inventory['ipi'];
        $pricingData['pisPerc'] = self::PIS_PERCENTAGE;
        $pricingData['CofinsPerc'] = self::COFINS_PERCENTAGE;
    
        // If the product belongs to a specific NCM, adjust parameters
        $pricingData['sets'] = (substr($inventory['NCM'], 0, 4) === '8452')
            ? round(self::CONTAINER_WEIGHT_CAPACITY / ($inventory['Peso'] + 24), 0)
            : round(self::CONTAINER_WEIGHT_CAPACITY / $inventory['Peso'], 0);
        
        // Calculate sea freight percentage
        $pricingData['seaFreightPrice'] = $inventory['UltimoFrete'];
        $pricingData['dolar'] = $inventory['UltimoDolar'];
        $pricingData['containerValue'] = $pricingData['sets'] * $inventory['vFob'];
        $pricingData['seaFreightPerc'] = round(($pricingData['seaFreightPrice'] / $pricingData['containerValue']) * 100, 1);
        $pricingData['expensesPerc'] = self::FIXED_EXPENSE_PERCENTAGE;
    
        // Total clearing percentage
        $pricingData['clearingPerc'] = $pricingData['seaFreightPerc'] + $pricingData['expensesPerc'] + $pricingData['iiPerc'];
    
        // Set FOB price and CIF price
        $pricingData['setFobPrice'] = $pricingData['dolar'] * $inventory['vFob'];
        $pricingData['setCifPrice'] = (($pricingData['containerValue'] + $pricingData['seaFreightPrice'] + self::SEA_FREIGHT_FIXED_COST) 
            * $pricingData['dolar']) / $pricingData['sets'];
    
        // Set the clear price and index
        $pricingData['setClearPrice'] = $pricingData['setFobPrice'] / (1 - ($pricingData['clearingPerc'] / 100));
        $pricingData['setClearIdx'] = $pricingData['setClearPrice'] / $inventory['vFob'];
    
        return $pricingData;
    }
    
    /**
     * Calculates the "revenda FOB" value based on ICMS rates and different markups.
     */
    private function calculate_revenda_fob($inventory, $pricingData)
    {
        $revendaFobValues = [];
        
        // Tax calculation based on ICMS percentages
        $icmsRates = [self::ICMS_TAX_1, self::ICMS_TAX_2];
        foreach ($icmsRates as $icmsPerc) {
            $taxPerc = $pricingData['pisPerc'] + $pricingData['CofinsPerc'] + $icmsPerc;
    
            // Calculate FOB value for different markups (15% to 40%)
            for ($markup = 15; $markup <= 40; $markup++) {
                $idx = round($pricingData['setClearIdx'] / (1 - (($taxPerc + $markup) / 100)), 2);
                $revendaFobValues[$icmsPerc][$markup] = round($idx * $inventory['vFob'], 2);
            }
        }
    
        return $revendaFobValues;
    }
    
    /**
     * Converts a JSON structure to a HTML table.
     */
    private function jsonToTable($data)
    {
        $table = '<table class="json-table" width="100%">';
        foreach ($data as $key => $value) {
            $table .= '<tr valign="top">';
            $table .= '<td><strong>' . $key . ':</strong></td><td>';
            
            if (is_array($value)) {
                $table .= $this->jsonToTable($value);  // Recursive call for nested arrays
            } else {
                $table .= $value;
            }
            $table .= '</td></tr>';
        }
        $table .= '</table>';
        return $table;
    }



}