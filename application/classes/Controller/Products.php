<?php

class Controller_Products extends Controller_Chartfy
{

   
   public function before()
    {
         parent::before();
         
    }

   public function action_chart()
    {
        $segid = $this->request->param('division');
        $brand = $this->request->param('xtras');

        if($segid==1)
        {
            
            // $data = self::next('machines');
            $raw = self::next('machines');
            $data = self::chart_prepare($raw);
            $data['caption']= self::machines();
        }
        if($segid==2)
        {
            $data = self::next('bearings');
            $data['caption']= self::bearings();
        }
        
         if($segid==3)
        {
            $data = self::next('parts');
            $data['caption']= self::parts();
        }
        
          if($segid==5)
        {
            $data = self::next('auto');
            $data['caption']= self::auto();
        }

        $theme='datatables';
        $view = '';
        
      
   
  
        $data['theme'] = 2;
        
        $fmt['V']= $fmt['V22']= $fmt['V21']= $fmt['V20']= $fmt['V19']= $fmt['V18']= $fmt['V17']= $fmt['V16'] = '0ms';
        $fmt['V15']= $fmt['V14']= $fmt['V13']= $fmt['V12']= $fmt['V11']= $fmt['V10'] = '0ms';
        $fmt['V9']= $fmt['V8']= $fmt['V7']= $fmt['V6']= $fmt['V5']= $fmt['V4'] = '0ms';
        $fmt['AGE'] = '0us'; 
        $data['format'] = $fmt;
        
        
    //   $data['found_rows'] = $found_rows;
        // $data['pager']['pagination'] = $this->pagination;
        // // die();
        
        
       
        
        $view.= parent::chartfy( $data );
        
        $response =  View::factory('pagefy/chart')
            ->set('table', $view );
            
        $this->response->body($response);
                    
                    
        //die();
        
        // s($data['pagination']);
       
        //$response = parent::pagefy($view,$theme);
        // s($_SERVER);


        // s($data);

        // echo 'index '. $this->request->param('id');
        // $inv = json_decode(self::get_product(), true);

        //s($inv);
        // $solr = self::get_solr($code);
        //  s($solr);
        //  die();
        // $this->product = array_merge($inv, $solr);
        //s($this->product );
        //if($this->product['produtoComprasSugeridoUnidades'] < 0)  die('Quantidade sugerida menor que 0 . Favor vericar se já existe pedidos de compra suficientes para este produto.');

        //json_decode(self::get_product(),true);

        // $view = View::factory('history')
        //     ->set('menu', '')
        //     ->set('array', $this->product)
        //     ->set('factories', $factories)
        //     ->set('next', $next);

        // $this->response->body($view);
    }
    
    public function action_index()
    {
        $segid = $this->request->param('division');
        $brand = $this->request->param('xtras');

        if($segid==1)
        {
            
            $raw = self::next('machines');
            $data = self::classify($raw);
            
            $data['caption']= self::machines();
        }
        if($segid==2)
        {
            $data = self::next('bearings');
            $data['caption']= self::bearings();
        }
        
         if($segid==3)
        {
            $data = self::next('parts');
            $data['caption']= self::parts();
        }
        
          if($segid==5)
        {
            $data = self::next('auto');
            $data['caption']= self::auto();
        }

        $theme='datatables';
        $view = '';
        
      
    //   $color['Growth'][0]['class'] = ' text-red-400 text-md font-bold';
    //     $color['Growth'][0]['rule']  = ' > 0 ';
    //     $color['Growth'][1]['class'] = ' text-red-400 text-md font-bold';
    //     $color['Growth'][1]['rule']  = ' > 50 ';
    //     $color['Growth'][2]['class'] = ' text-blue-600 text-md  font-bold';
    //     $color['Growth'][2]['rule']  = ' > 75';
    //     $color['Growth'][3]['class'] = ' text-green-700 text-md  font-bold';
    //     $color['Growth'][3]['rule']  = ' > 100 ';
        
        
    //     $color['PercMês'][0]['class'] = ' text-red-400 text-md font-bold';
    //     $color['PercMês'][0]['rule']  = ' > 0 ';
    //     $color['PercMês'][1]['class'] = ' text-red-400 text-md font-bold';
    //     $color['PercMês'][1]['rule']  = ' > 5 ';
    //     $color['PercMês'][2]['class'] = ' text-blue-600 text-md  font-bold';
    //     $color['PercMês'][2]['rule']  = ' > 10';
    //     $color['PercMês'][3]['class'] = ' text-green-700 text-md  font-bold';
    //     $color['PercMês'][3]['rule']  = ' > 20 ';
        
    //     $color['Subtotal'][0]['class'] = ' text-red-400 text-md font-bold';
    //     $color['Subtotal'][0]['rule']  = ' > 1000 ';
    //     $color['Subtotal'][1]['class'] = ' text-yellow-400 text-md font-bold';
    //     $color['Subtotal'][1]['rule']  = ' > 10000 ';
    //     $color['Subtotal'][2]['class'] = ' text-blue-600 text-md  font-bold';
    //     $color['Subtotal'][2]['rule']  = ' > 50000';
    //     $color['Subtotal'][3]['class'] = ' text-green-700 text-md  font-bold';
    //     $color['Subtotal'][3]['rule']  = ' > 100000 ';
        
    //     $data['color'] = $color;
        
        //// Editable
        
        //  $edt['Vip'] = 'mak.clientes|vip|id|_IDCLI';
        //  $edt['ParecerComercial'] = 'mak.clientes|ParecerComercial|id|_IDCLI';
        //  $data['editable'] = $edt;
        
        // $idven =  $this->request->query('idven');
        // $baseurl='/metrics/customers/index';
        
        
            
        // s( $this->pagination);
        // $data['pagination'] = $this->pagination;
        $data['theme'] = 2;
        
        $fmt['V']= $fmt['V22']= $fmt['V21']= $fmt['V20']= $fmt['V19']= $fmt['V18']= $fmt['V17']= $fmt['V16'] = '0ms';
        $fmt['V15']= $fmt['V14']= $fmt['V13']= $fmt['V12']= $fmt['V11']= $fmt['V10'] = '0ms';
        $fmt['V9']= $fmt['V8']= $fmt['V7']= $fmt['V6']= $fmt['V5']= $fmt['V4'] = '0ms';
        $fmt['AGE'] = '0us'; 
        $data['format'] = $fmt;
        
        
    //   $data['found_rows'] = $found_rows;
        // $data['pager']['pagination'] = $this->pagination;
        // // die();
        
        
       
        
        $view.= parent::tablefy( $data );
        
        // s($data['pagination']);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);
        // s($data);

        // echo 'index '. $this->request->param('id');
        // $inv = json_decode(self::get_product(), true);

        //s($inv);
        // $solr = self::get_solr($code);
        //  s($solr);
        //  die();
        // $this->product = array_merge($inv, $solr);
        //s($this->product );
        //if($this->product['produtoComprasSugeridoUnidades'] < 0)  die('Quantidade sugerida menor que 0 . Favor vericar se já existe pedidos de compra suficientes para este produto.');

        //json_decode(self::get_product(),true);

        // $view = View::factory('history')
        //     ->set('menu', '')
        //     ->set('array', $this->product)
        //     ->set('factories', $factories)
        //     ->set('next', $next);

        // $this->response->body($view);
    }
  
    private function auto()
    {
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/5/?modelo=MTR-CV', 'title' => 'MTR-CV') ;

         
         return $data['caption'];
    
    } 
  
     private function parts()
    {    
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/3/?modelo=btr', 'title' => 'btr') ;

         
         return $data['caption'];
    
    } 
    private function bearings()
    {
        
         $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?modelo=CS608', 'title' => 'CS608') ;
         
         return $data['caption'];
    
    }

 
    private function machines()
    {
        
             
              $data['caption'][] = array('link'=>'/metrics/portfolio/index/2/?modelo=CS608', 'title' => 'CS608') ;
             return $data['caption'];
        
    }

    private function next($segmento='machines')
    {
        $having="";
        $where="";
        $this->group="hi.isbn";
        
        // if($this->request->query('not')) $where.= sprintf(" AND %s ", $this->request->query('not'));
        // echo $where;
        // die();
        
        if($this->request->query('blue')) $where.= sprintf(" AND  r.blue > %s", $this->request->query('blue'));
        
        if($this->request->query('rrh')) $where.= sprintf(" AND  r.rolemak_ranking_history >0 AND r.rolemak_ranking_history <= %s ", $this->request->query('rrh'));
        if($this->request->query('rrn')) $where.= sprintf(" AND  r.rolemak_ranking_now >0 AND r.rolemak_ranking_now <= %s ", $this->request->query('rrn'));
        if($this->request->query('gr')) $where.= sprintf(" AND  r.global_ranking >0 AND r.global_ranking <= %s ", $this->request->query('gr'));
        if($this->request->query('meso')) $where.= sprintf(" AND meso LIKE '%s' ", $this->request->query('meso').'%');
        if($this->request->query('estado')) $where.= sprintf(" AND estado='%s' ", $this->request->query('estado'));
        if($this->request->query('regiao')) $where.= sprintf(" AND regiao='%s' ", $this->request->query('regiao'));
        if($this->request->query('cidade'))
        {
            if(substr($this->request->query('cidade'),0,1)=='!')
            {
                $where.= sprintf(" AND cidade != '%s' ",  ltrim($this->request->query('cidade'), '!'));
            }else{
                $where.= sprintf(" AND cidade='%s' ", $this->request->query('cidade'));
            }
        }
        if($this->request->query('nome')) $where.= sprintf(" AND c.nome LIKE '%s' ", '%'.$this->request->query('nome').'%');
        if($this->request->query('desc')) $where.= sprintf(" AND i.nome LIKE '%s' ", '%'.$this->request->query('desc').'%');
        if($this->request->query('nfe')) $where.= sprintf(" AND nfe LIKE '%s' ", $this->request->query('nfe').'%');
        if($this->request->query('nick')) $where.= sprintf(" AND u.nick LIKE '%s' ", $this->request->query('nick').'%');
        if($this->request->query('marca')) $where.= sprintf(" AND i.marca LIKE '%s' ", $this->request->query('marca').'%');
        if($this->request->query('modelo')) $where.= sprintf(" AND i.modelo LIKE '%s' ", '%'.$this->request->query('modelo').'%');
        if($this->request->query('data')) $where.= sprintf(" AND h.data LIKE '%s' ", $this->request->query('data').'%');
        if($this->request->query('cep')) $where.= sprintf(" AND c.cep LIKE '%s' ", $this->request->query('cep').'%');
        if($this->request->query('group')) $this->group= sprintf(" %s ", $this->request->query('group'));
        // if($this->request->query('vols')) $having= sprintf(" Having vols %s ", $this->request->query('vols'));
        if($this->request->query('recencia')) $having= sprintf(" Having `Dias` BETWEEN %s ", $this->request->query('recencia'));
        if($this->request->query('blue')) $having= sprintf(" Having `IdxBlue` BETWEEN %s ", $this->request->query('blue'));
        if($this->request->query('a2021')) $having= sprintf(" Having `QtA2022` < 1 ");
   						       
         $sql = sprintf("SELECT c.id _IDCLI,
                                i.id _ISBN,
                                i.modelo,
                                i.marca,
                                u.nick as VD,
                                COUNT(DISTINCT c.id) idclis,  
                                c.nome as Cliente, 
                                c.Cidade,c.Estado, 
                                LEFT(c.Cep,4) Cep, 
                                Meso,
                                NomeMeso,
                                NomeMicro,
                                r.global_ranking GR,
                                r.rolemak_ranking_history RR,
                                r.rolemak_ranking_now RN,
                                SUM(DISTINCTROW  r.blue) Blue,
                                SUM(DISTINCTROW r.red) Red,
                                SUM(hi.valor_base*hi.quant) as V,
                                SUM(IF(YEAR(h.data)=2021,hi.quant,0)) as Q21, 
                                SUM(IF(YEAR(h.data)=2022,hi.quant,0)) as Q22, 
                                
                                SUM(IF(YEAR(h.data)=2021 and MONTH(h.data)=1,hi.quant,0)) as X1,
                                SUM(IF(YEAR(h.data)=2021 and MONTH(h.data)=2,hi.quant,0)) as X2, 
                                SUM(IF(YEAR(h.data)=2021 and MONTH(h.data)=3,hi.quant,0)) as X3, 
                                SUM(IF(YEAR(h.data)=2021 and MONTH(h.data)=4,hi.quant,0)) as X4, 
                                SUM(IF(YEAR(h.data)=2021 and MONTH(h.data)=5,hi.quant,0)) as X5, 
                                SUM(IF(YEAR(h.data)=2021 and MONTH(h.data)=6,hi.quant,0)) as X6, 
                                SUM(IF(YEAR(h.data)=2021 and MONTH(h.data)=7,hi.quant,0)) as X7, 
                                SUM(IF(YEAR(h.data)=2021 and MONTH(h.data)=8,hi.quant,0)) as X8, 
                                SUM(IF(YEAR(h.data)=2021 and MONTH(h.data)=9,hi.quant,0)) as X9, 
                                SUM(IF(YEAR(h.data)=2021 and MONTH(h.data)=10,hi.quant,0)) as X10, 
                                SUM(IF(YEAR(h.data)=2021 and MONTH(h.data)=11,hi.quant,0)) as X11, 
                                SUM(IF(YEAR(h.data)=2021 and MONTH(h.data)=12,hi.quant,0)) as X12, 
                                
                                SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=1,hi.quant,0)) as Q1,
                                SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=2,hi.quant,0)) as Q2, 
                                SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=3,hi.quant,0)) as Q3, 
                                SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=4,hi.quant,0)) as Q4, 
                                SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=5,hi.quant,0)) as Q5, 
                                SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=6,hi.quant,0)) as Q6, 
                                SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=7,hi.quant,0)) as Q7, 
                                SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=8,hi.quant,0)) as Q8, 
                                SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=9,hi.quant,0)) as Q9, 
                                SUM(IF(YEAR(h.data)=2022 and MONTH(h.data)=10,hi.quant,0)) as Q10, 
                                
                                SUM(IF(YEAR(h.data)=2022,hi.valor_base*hi.quant,0)) as V22, 
                                SUM(IF(YEAR(h.data)=2021,hi.valor_base*hi.quant,0)) as V21, 
                                SUM(IF(YEAR(h.data)=2020,hi.valor_base*hi.quant,0)) as V20, 
                                SUM(IF(YEAR(h.data)=2019,hi.valor_base*hi.quant,0)) as V19,
                                SUM(IF(YEAR(h.data)=2018,hi.valor_base*hi.quant,0)) as V18, 
                                SUM(IF(YEAR(h.data)=2017,hi.valor_base*hi.quant,0)) as V17, 
                                SUM(IF(YEAR(h.data)=2016,hi.valor_base*hi.quant,0)) as V16, 
                                SUM(IF(YEAR(h.data)=2015,hi.valor_base*hi.quant,0)) as V15,
                                SUM(IF(YEAR(h.data)=2014,hi.valor_base*hi.quant,0)) as V14,
                                SUM(IF(YEAR(h.data)=2013,hi.valor_base*hi.quant,0)) as V13,
                                SUM(IF(YEAR(h.data)=2012,hi.valor_base*hi.quant,0)) as V12,
                                SUM(IF(YEAR(h.data)=2011,hi.valor_base*hi.quant,0)) as V11,
                                SUM(IF(YEAR(h.data)=2010,hi.valor_base*hi.quant,0)) as V10,
                                SUM(IF(YEAR(h.data)=2009,hi.valor_base*hi.quant,0)) as V9,
                                SUM(IF(YEAR(h.data)=2008,hi.valor_base*hi.quant,0)) as V8,
                                SUM(IF(YEAR(h.data)=2007,hi.valor_base*hi.quant,0)) as V7,
                                SUM(IF(YEAR(h.data)=2006,hi.valor_base*hi.quant,0)) as V6,
                                SUM(IF(YEAR(h.data)=2005,hi.valor_base*hi.quant,0)) as V5,
                                SUM(IF(YEAR(h.data)=2004,hi.valor_base*hi.quant,0)) as V4

                                
                                FROM inv i  
                                LEFT JOIN hist hi ON (hi.isbn=i.id) 
                                LEFT JOIN mak.hoje h ON (h.id=hi.pedido) 
                                LEFT JOIN produtos p ON (p.id=i.idcf)
                                LEFT JOIN clientes c ON (c.id=h.idcli) 
                                LEFT JOIN users u ON (u.id=c.vendedor)
                                LEFT JOIN crm.rfm r ON (r.clientes_id=c.id)
                                LEFT JOIN estados e ON (e.uf=c.estado)
                                LEFT JOIN estatisticas.vMun m ON (m.NomeMunic=c.cidade )
                                WHERE c.vip< 9 AND hi.valor_base>0 AND nop IN (27,28,51) AND m.UF=e.codigo_ibge AND p.segmento='machines' AND h.id > 812191 %s 
                                
                                GROUP BY %s
                                
                                
                                
                                LIMIT 500" ,  $where, $this->group  );
                                
                                // die($sql);
   
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute()->as_array();
           
            $vt = 0;
            foreach ($result as $key => $row) 
            {
                $vt+=$row["V22"];
            }
            
            foreach ($result as $key => $row) 
            {
                $result[$key]['PC'] = ($row["V22"]/$vt)*100;
            }

            // s($result);
            
            
            // $rv = self::chart_prepare($result);
            //  //s($rv);
            //  //die();
            // return $rv;
            // // $rv = self::classify($result);
            
            
            //  die();
            
         return $result ;
        
    }
  

    function chart_prepare($result)
    {
         $v  = array_column($result, 'V');
         array_multisort($v, SORT_DESC, $result);

        foreach ($result as $key => $row) 
        {
            
            //  $rv[$row['_IDCLI']]['_IDCLI']  = $row['_IDCLI'];
            if($this->request->query('group')=='h.idcli')             $rv[$row['_IDCLI']]['Cliente'] = $row['Cliente'];
            $rv[$row['_IDCLI']]['Cidade'] = $row['Cidade'];
            $rv[$row['_IDCLI']]['Estado'] = $row['Estado'];
            // $rv[$row['_IDCLI']]['rTotal']  = $key+1;
        }

        for ($i=11; $i<=22;$i++)
        {
            $func = 'V'.$i;  
            // $var  = array_column($result, $func);
            // array_multisort($var, SORT_DESC, $result);
             
            foreach ($result as $key => $row) 
            {    
                // $rank = $key+1;
                // if($row[$func]==0) $rank='-';
                //  $rv[$row['_IDCLI']]['A'.$i] = $rank;
                
                 $r[$row['_IDCLI']][] = round($row[$func]/1000,0);
                 $rv[$row['_IDCLI']]['ValorTotal'] = round($row['V']/1000,0);
                 $rv[$row['_IDCLI']]['Evolução'] = implode(', ',$r[$row['_IDCLI']]).' ';
                 
                 $rv[$row['_IDCLI']]['A'.$i] = round($row[$func]/1000,0);
                 
                 
            }
           
        }
        //  s($rv);
             foreach ($rv as $key => $row) 
            {    
               $resp[] = $row;
                 
            }
        return $resp;
        
    } 
    
    function classify($result)
    {
        $v  = array_column($result, 'V22');
        array_multisort($v, SORT_DESC, $result);
        
        for ($i=1; $i<=12;$i++)
        {
            $func = 'X'.$i;  
            
            foreach ($result as $key => $row) 
            {    
                $rank = $row[$func];
                if($row[$func]==0) $rank='-';
                 $rv[$row['_ISBN']]['X'.$i] = $rank;
                 $q[$row['_ISBN']][]=$row[$func];
                 
            }
    
        }
        
          foreach ($result as $key => $row) 
        {
            $rv[$row['_ISBN']]['X2021'] =  ($row['Q21']<1) ? '-' : $row['Q21'];
            // $rv[$row['_ISBN']]['V22'] = ($row['V22']< 1) ? '-' :  round($row['V22']/1000,0).'K';
            // $rv[$row['_ISBN']]['PC'] =  ($row['PC']<= 0) ? '-' : round($row['PC'],1).'%';

              
        }


        
        for ($i=1; $i<=10;$i++)
        {
            $func = 'Q'.$i;  
            
            foreach ($result as $key => $row) 
            {    
                $rank = $row[$func];
                if($row[$func]==0) $rank='-';
                 $rv[$row['_ISBN']]['Q'.$i] = $rank;
                 $q[$row['_ISBN']][]=$row[$func];
                 
            }
    
        }
        
        foreach ($result as $key => $row) 
        {
            $rv[$row['_ISBN']]['Q2022'] =  ($row['Q22']<1) ? '-' : $row['Q22'];
            $rv[$row['_ISBN']]['V'] =  round($row['V']/1000,0).'K';
            // $rv[$row['_ISBN']]['V22'] = ($row['V22']< 1) ? '-' :  round($row['V22']/1000,0).'K';
            $rv[$row['_ISBN']]['PC'] =  ($row['PC']<= 0) ? '-' : round($row['PC'],1).'%';
            $rv[$row['_ISBN']]['Modelo'] = $row['modelo'];
            $rv[$row['_ISBN']]['Marca'] = $row['marca'];

           
            // $rv[$row['_ISBN']]['Red22'] =  ($row['Red']<1) ? '-' : $row['Red'];
            // $rv[$row['_ISBN']]['Blue22'] = ($row['Blue']<1) ? '-' : $row['Blue'];
            // $rv[$row['_ISBN']]['Diff'] = ($row['Q22']-$row['Blue']==0) ? "-" : $row['Q22']-$row['Blue'];
            $rv[$row['_ISBN']]['Clientes'] = $row['idclis'];
            
            
            
            //  if($this->group=="h.idcli") 
            // {
            //     $rv[$row['_ISBN']]['VD'] =   $row['VD'];
            //     $rv[$row['_ISBN']]['Cliente'] = $row['Cliente'];
            //     // $rv[$row['_ISBN']]['RRHist'] =  ($row['RR']);
            //     $rv[$row['_ISBN']]['RRNow'] =  ($row['RN']);
            //     $rv[$row['_ISBN']]['GRank'] =  ($row['GR']);
                
            // }    
            // if($this->request->query('group')<>'estado' and $this->request->query('group')<>'meso') 
            // {
               
            //     $rv[$row['_ISBN']]['Cidade'] = $row['Cidade'];
            //      $rv[$row['_ISBN']]['Cep'] = $row['Cep'];
                
            // }
            // if($this->request->query('group')<>'estado') 
            // {
                
            //     $rv[$row['_ISBN']]['Meso'] = $row['Meso'];
            //     $rv[$row['_ISBN']]['NomeMeso'] = $row['NomeMeso'];
            //     $rv[$row['_ISBN']]['NomeMicro'] = $row['NomeMicro'];
            // }
            
            // $rv[$row['_ISBN']]['UF'] = $row['Estado'];
            //  $rv[$row['_ISBN']]['rTotal'] = $key+1;
            
              
        }

        foreach ($q as $key => $row) 
        {
            $rv[$key]['Mean']      = round(Phpml\Math\Statistic\Mean::arithmetic($row),0); 
            $rv[$key]['Deviation']= round(Phpml\Math\Statistic\StandardDeviation::population($row),0); 
        }
          
        for ($i=22; $i>=4;$i--)
        {
            $func = 'V'.$i;  
            $var  = array_column($result, $func);
            array_multisort($var, SORT_DESC, $result);
            foreach ($result as $key => $row) 
            {    
                $rank = $key+1;
                if($row[$func]==0) $rank='-';
                 $rv[$row['_ISBN']]['A'.$i] = $rank;
                //  $info['rankh'] = $rv[$row['_ISBN']]['rTotal'];
                 $info['rankn'] = $rv[$row['_ISBN']]['A22'];
                 $info['idcli'] = $row['_ISBN'];
                //  self::store($info);
            }
            
        }
        
        d($rv);
        die();
        return $rv;
        
    }

    public function store($info)
    {
            
            $sql = sprintf("UPDATE crm.`rfm` SET `rolemak_ranking_history` = '%s',`rolemak_ranking_now` = '%s' WHERE `rfm`.`clientes_id` = %s",
                $info['rankh'],
                $info['rankn'],
                $info['idcli']
                );
            //  s($sql);
            
             $query = DB::query(Database::INSERT, $sql);
             $result = $query->execute();
   }
   
   public function action_ssw()
    {
            $sql = "SELECT c.nome,c.cnpj,c.id idcli,
                            SUM( IF( (s.peso/s.volumes*2)>40,round(s.volumes/2,0),0)) as Q 
                    FROM Analytics.ssw s 
                    LEFT JOIN mak.clientes c ON (c.cnpj=s.cnpj)
                    WHERE s.seller='welttec' AND c.id > 0
                    GROUP BY cnpj 
                    LIMIT 10000 ";
                    
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute()->as_array();
            
            d($result);
            // die();
            
            foreach( $result as $k => $v)
            {
                $sql = sprintf("UPDATE crm.`rfm` SET `blue` = '%s' WHERE `rfm`.`clientes_id` = %s",
                    $v['Q'],
                    $v['idcli']
                    );
                //  s($sql);
            
                $query = DB::query(Database::INSERT, $sql);
                $result = $query->execute();
            }
   }
}