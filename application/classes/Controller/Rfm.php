<?php

class Controller_Rfm extends Controller_Website
{
    // Constantes para pontuação RFM
    const RECENCY_WEIGHTS = [
        30 => 5,   // Últimos 30 dias
        60 => 4,   // 31-60 dias
        90 => 3,   // 61-90 dias
        180 => 2,  // 91-180 dias
        999 => 1   // Mais de 180 dias
    ];

    const FREQUENCY_WEIGHTS = [
        20 => 5,  // 20+ compras
        15 => 4,  // 15-19 compras
        10 => 3,  // 10-14 compras
        5 => 2,   // 5-9 compras
        0 => 1    // Menos de 5 compras
    ];

    const MONETARY_WEIGHTS = [
        120000 => 5,  // R$120.000+
        60000 => 4,   // R$60.000-9.999
        30000 => 3,   // R$30.000-4.999
        15000 => 2,   // R$15.000-2.499
        0 => 1       // Menos de R$15.000
    ];

    public function before()
    {
        parent::before();
    }

    public function action_index()
    {
        // Obter o período de análise (padrão: últimos 12 meses)
        $periodo = $this->request->param('periodo', 12);
        
        // Buscar dados dos clientes
        $clientes = $this->get_customer_data($periodo);
        
        if (!$clientes) {
            throw new HTTP_Exception_404('Nenhum dado encontrado para o período especificado');
        }

        // Calcular RFM para cada cliente
        $rfm_scores = $this->calculate_rfm_scores($clientes);
        
        // Renderizar view com os resultados
        $view = View::factory('rfm/index', [
            'scores' => $rfm_scores,
            'periodo' => $periodo
        ]);
        
        $this->response->body($view);
    }

    private function get_customer_data($meses)
    {
        $sql = "
            SELECT 
                c.id as cliente_id,
                c.nome as cliente_nome,
                MAX(p.data) as data,
                p.valor_base,
                COUNT(p.id) as frequencia,
                SUM(p.valor_base) as valor_total_periodo
            FROM clientes c
            JOIN hoje p ON (p.idcli = c.id)
            WHERE p.data >= DATE_SUB(NOW(), INTERVAL :meses MONTH) 
                  and p.nop in (27,28,51)
            GROUP BY c.id, c.nome
            ORDER BY valor_total_periodo DESC, c.id
            LIMIT 1000";

        return DB::query(Database::SELECT, $sql)
                ->parameters([':meses' => $meses])
                ->execute()
                ->as_array();
    }

    private function calculate_rfm_scores($clientes)
    {
        $hoje = new DateTime();
        $resultados = [];

        foreach ($clientes as $cliente) {
            // Calcular Recência - Corrigindo o cálculo de dias
            $ultima_compra = new DateTime($cliente['data']);
            $hoje = new DateTime();
            $dias_desde_ultima_compra = (int)$ultima_compra->diff($hoje)->format('%r%a'); // Força o valor como inteiro e considera o sinal

            // Calcular scores
            $score_r = $this->calculate_recency_score($dias_desde_ultima_compra);
            $score_f = $this->calculate_frequency_score($cliente['frequencia']);
            $score_m = $this->calculate_monetary_score($cliente['valor_total_periodo']);

            // Calcular pontuação total
            $score_total = ($score_r + $score_f + $score_m) / 3;

            // Determinar segmento do cliente
            $segmento = $this->determine_customer_segment($score_total);

            $resultados[] = [
                'cliente_id' => $cliente['cliente_id'],
                'cliente_nome' => $cliente['cliente_nome'],
                'recency_score' => $score_r,
                'frequency_score' => $score_f,
                'monetary_score' => $score_m,
                'score_total' => round($score_total, 2),
                'segmento' => $segmento
            ];
        }

        return $resultados;
    }

    private function calculate_recency_score($dias)
    {
        $dias = abs($dias); // Garante que o número de dias seja positivo
        
        if ($dias <= 30) return 5;      // Últimos 30 dias
        if ($dias <= 60) return 4;      // 31-60 dias
        if ($dias <= 90) return 3;      // 61-90 dias
        if ($dias <= 180) return 2;     // 91-180 dias
        return 1;                       // Mais de 180 dias
    }

    private function calculate_frequency_score($frequencia)
    {
        foreach (self::FREQUENCY_WEIGHTS as $limite => $score) {
            if ($frequencia >= $limite) {
                return $score;
            }
        }
        return 1;
    }

    private function calculate_monetary_score($valor_total)
    {
        foreach (self::MONETARY_WEIGHTS as $limite => $score) {
            if ($valor_total >= $limite) {
                return $score;
            }
        }
        return 1;
    }

    private function determine_customer_segment($score_total)
    {
        if ($score_total >= 4.5) return 'Champions';
        if ($score_total >= 4.0) return 'Leais';
        if ($score_total >= 3.0) return 'Potenciais';
        if ($score_total >= 2.0) return 'Em Risco';
        return 'Perdidos';
    }

    public function action_export()
    {
        $periodo = $this->request->param('periodo', 12);
        $clientes = $this->get_customer_data($periodo);
        $rfm_scores = $this->calculate_rfm_scores($clientes);

        // Preparar cabeçalho do CSV
        $headers = [
            'ID Cliente',
            'Nome Cliente',
            'Score Recência',
            'Score Frequência',
            'Score Monetário',
            'Score Total',
            'Segmento'
        ];

        // Criar arquivo CSV
        $output = fopen('php://output', 'w');
        fputcsv($output, $headers);

        foreach ($rfm_scores as $score) {
            fputcsv($output, [
                $score['cliente_id'],
                $score['cliente_nome'],
                $score['recency_score'],
                $score['frequency_score'],
                $score['monetary_score'],
                $score['score_total'],
                $score['segmento']
            ]);
        }

        // Configurar headers para download
        $this->response->headers('Content-Type', 'text/csv');
        $this->response->headers('Content-Disposition', 'attachment; filename=rfm_analysis.csv');
    }
}