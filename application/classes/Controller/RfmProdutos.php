<?php

class Controller_RfmProdutos extends Controller_Website
{
    // Constantes para pontuação RFM de Produtos
    const RECENCY_WEIGHTS = [
        30 => 5,   // Vendido nos últimos 30 dias
        60 => 4,   // 31-60 dias
        90 => 3,   // 61-90 dias
        180 => 2,  // 91-180 dias
        999 => 1   // Mais de 180 dias
    ];

    const FREQUENCY_WEIGHTS = [
        50 => 5,  // 50+ vendas
        30 => 4,  // 30-49 vendas
        20 => 3,  // 20-29 vendas
        10 => 2,  // 10-19 vendas
        0 => 1    // Menos de 10 vendas
    ];

    const MONETARY_WEIGHTS = [
        50000 => 5,  // R$50.000+
        25000 => 4,  // R$25.000-49.999
        10000 => 3,  // R$10.000-24.999
        5000 => 2,   // R$5.000-9.999
        0 => 1       // Menos de R$5.000
    ];

    public function action_index()
    {
        $periodo = (int)$this->request->param('periodo', 12);
        $page = (int)$this->request->query('page'); // Fixed: using query() instead of get()
        $page = ($page > 0) ? $page : 1; // Ensure we have a valid page number
        $per_page = 10;
        
        $produtos = $this->get_product_data($periodo);
        
        if (!$produtos) {
            throw new HTTP_Exception_404('Nenhum dado encontrado para o período especificado');
        }

        $total_records = count($produtos);
        $total_pages = ceil($total_records / $per_page);
        $current_page = max(1, min($page, $total_pages));
        
        $start = ($current_page - 1) * $per_page;
        $produtos_paginados = array_slice($produtos, $start, $per_page);
        
        $rfm_scores = $this->calculate_rfm_scores($produtos_paginados);
        
        $start_record = $start + 1;
        $end_record = min($start + $per_page, $total_records);
        
        $view = View::factory('rfm_produtos/index', [
            'scores' => $rfm_scores,
            'periodo' => $periodo,
            'current_page' => $current_page,
            'total_pages' => $total_pages,
            'total_records' => $total_records,
            'start_record' => $start_record,
            'end_record' => $end_record
        ]);
        
        $this->response->body($view);
    }

    private function get_product_data($meses)
    {
        $sql = "
            SELECT 
                i.id as produto_id,
                i.modelo as produto_modelo,
                i.marca as produto_marca,
                i.nome as produto_nome,

						    		(
                                        (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE i.id=e1.`ProdutoPOID`)
                                        +
                                        (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE i.id=e3.`ProdutoPOID`)
                                        +
                                        (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE i.id=e5.`ProdutoPOID`)
                                        +
                                        (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE i.id=e6.`ProdutoPOID`)
                                         +
                                        (SELECT EstoqueDisponivel FROM mak_0966.Estoque e9 WHERE i.id=e9.`ProdutoPOID`)
                                        +
                                        (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE i.id=e6t.`ProdutoPOID`)
         
                                     )*fob*5.7*1.2 AS Subtotal,
                                     
                MAX(h.data) as ultima_venda,
                COUNT(p.id) as frequencia_vendas,
                SUM(p.valor_base*p.quant) as valor_total_vendas
            FROM inv i
            JOIN hist p ON (p.isbn = i.id)
            JOIN hoje h ON (h.id = p.pedido)
            JOIN produtos s ON (s.id = i.idcf)
            WHERE h.data >= DATE_SUB(NOW(), INTERVAL :meses MONTH) 
                  AND h.nop IN (27,28,51) AND s.segmento='machines'
            GROUP BY i.id, i.modelo, i.nome
            HAVING Subtotal > 0
            ORDER BY Subtotal DESC, valor_total_vendas DESC
            LIMIT 300";

        return DB::query(Database::SELECT, $sql)
                ->parameters([':meses' => $meses])
                ->execute()
                ->as_array();
    }

    private function calculate_rfm_scores($produtos)
    {
        $hoje = new DateTime();
        $resultados = [];

        foreach ($produtos as $produto) {
            $ultima_venda = new DateTime($produto['ultima_venda']);
            $dias_desde_ultima_venda = (int)$ultima_venda->diff($hoje)->format('%r%a');

            $score_r = $this->calculate_recency_score($dias_desde_ultima_venda);
            $score_f = $this->calculate_frequency_score($produto['frequencia_vendas']);
            $score_m = $this->calculate_monetary_score($produto['valor_total_vendas']);

            $score_total = ($score_r + $score_f + $score_m) / 3;

            $segmento = $this->determine_product_segment($score_total);

            $resultados[] = [
                'produto_id' => $produto['produto_id'],
                'produto_modelo' => $produto['produto_modelo'],
                'produto_marca' => $produto['produto_marca'],
                'produto_nome' => $produto['produto_nome'],
                'subtotal' => $produto['Subtotal'],
                'recency_score' => $score_r,
                'frequency_score' => $score_f,
                'monetary_score' => $score_m,
                'score_total' => round($score_total, 2),
                'segmento' => $segmento
            ];
        }

        return $resultados;
    }

    private function calculate_recency_score($dias)
    {
        $dias = abs($dias);
        
        if ($dias <= 30) return 5;      // Vendido nos últimos 30 dias
        if ($dias <= 60) return 4;      // 31-60 dias
        if ($dias <= 90) return 3;      // 61-90 dias
        if ($dias <= 180) return 2;     // 91-180 dias
        return 1;                       // Mais de 180 dias
    }

    private function calculate_frequency_score($frequencia)
    {
        foreach (self::FREQUENCY_WEIGHTS as $limite => $score) {
            if ($frequencia >= $limite) {
                return $score;
            }
        }
        return 1;
    }

    private function calculate_monetary_score($valor_total)
    {
        foreach (self::MONETARY_WEIGHTS as $limite => $score) {
            if ($valor_total >= $limite) {
                return $score;
            }
        }
        return 1;
    }

    private function determine_product_segment($score_total)
    {
        if ($score_total >= 4.5) return 'Estrelas';
        if ($score_total >= 4.0) return 'Produtos Principais';
        if ($score_total >= 3.0) return 'Produtos Regulares';
        if ($score_total >= 2.0) return 'Produtos em Risco';
        return 'Produtos Inativos';
    }

    private function get_segment_color($segmento)
    {
        switch ($segmento) {
            case 'Estrelas':
                return 'text-yellow-600 font-bold';
            case 'Produtos Principais':
                return 'text-green-600 font-bold';
            case 'Produtos Regulares':
                return 'text-blue-600';
            case 'Produtos em Risco':
                return 'text-orange-600';
            default:
                return 'text-red-600';
        }
    }

    public function action_export()
    {
        $periodo = $this->request->param('periodo', 12);
        $produtos = $this->get_product_data($periodo);
        $rfm_scores = $this->calculate_rfm_scores($produtos);

        $headers = [
            'ID Produto',
            'Modelo',
            'Nome',
            'Score Recência',
            'Score Frequência',
            'Score Monetário',
            'Score Total',
            'Segmento'
        ];

        $output = fopen('php://output', 'w');
        fputcsv($output, $headers);

        foreach ($rfm_scores as $score) {
            fputcsv($output, [
                $score['produto_id'],
                $score['produto_modelo'],
                $score['produto_nome'],
                $score['recency_score'],
                $score['frequency_score'],
                $score['monetary_score'],
                $score['score_total'],
                $score['segmento']
            ]);
        }

        $this->response->headers('Content-Type', 'text/csv');
        $this->response->headers('Content-Disposition', 'attachment; filename=rfm_produtos_analysis.csv');
    }
}