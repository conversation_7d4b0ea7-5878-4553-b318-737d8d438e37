<?php

class Controller_Sales extends Controller_Websession
{

    public function lmp_faturamento_segmento_vendedor_diario( $unity, $segment, $user = false, $type = false, $time = false)
    {
        $this->cache = Cache::instance('file');

        $get = $this->request->query();

        //Cache
        $cache_id = __FUNCTION__.json_encode($get);
        $this->cache->delete($cache_id);

        $retrieved_data = $this->cache->get( $cache_id );
        //s($retrieved_data);

        $result = array ();

        if ( $retrieved_data )
        {
            $result = $retrieved_data;
        }else{

            //WHERE
            $where = null;

            if ( $unity )
                $where.=sprintf(" AND a.unity_okr = %s ", $unity);

            if ( $segment )
                $where.=sprintf(" AND a.segment_okr = %s ", $segment);

            if ( $user )
                $where.=sprintf(" AND e.owner_okr_targets = %s ", $user);

            if ( $type )
                $where.=sprintf(" AND e.type_okr_targets = '%s' ", $type);

            $sql = sprintf("SELECT 
                            a.unity_okr as objetivoUnidadeID,
                            b.Fan<PERSON> as objetivoUnidadeNome,
                            a.department_okr as objetivoDepartamentoNome,
                            a.segment_okr as objetivoSegmentoID,
                            a.objective_okr as objetivoObjetivo,
                            CONCAT(a.year_okr, ' - ', d.quarter_okr_kr, 'º TRI') as resultadoChaveTrimestre,
                            d.segment_okr_kr as resultadoChaveSegmentoID,
                            CONCAT('KR', d.number_okr_kr , ' (Resultado Chave ' , d.number_okr_kr , ') - ' , d.name_okr_kr) as resultadoChaveNome,
                            d.measure_okr_kr as resultadoChaveMedida,
                            e.quarter_okr_targets as metaTrimestre,
                            e.type_okr_targets as metaTipo,
                            e.period_okr_targets as metaPeriodo,
                            (CASE
                                WHEN MONTH(CURDATE())=1 THEN e.january_okr_targets
                                WHEN MONTH(CURDATE())=2 THEN e.february_okr_targets
                                WHEN MONTH(CURDATE())=3 THEN e.march_okr_targets
                                WHEN MONTH(CURDATE())=4 THEN e.april_okr_targets
                                WHEN MONTH(CURDATE())=5 THEN e.may_okt_targets
                                WHEN MONTH(CURDATE())=6 THEN e.june_okr_targets
                                WHEN MONTH(CURDATE())=7 THEN e.july_okr_targets
                                WHEN MONTH(CURDATE())=8 THEN e.august_okr_targets
                                WHEN MONTH(CURDATE())=9 THEN e.september_okr_targets
                                WHEN MONTH(CURDATE())=10 THEN e.october_okr_targets
                                WHEN MONTH(CURDATE())=11 THEN e.november_okr_targets
                                WHEN MONTH(CURDATE())=12 THEN e.december_okr_targets
                                ELSE 0
                            END) as metaDiarioMesAtual,
                            e.date_created_okr_targets as metaDataCriado,
                            e.owner_okr_targets as metaResponsavelID,
                            (SELECT nick FROM users u WHERE u.id=e.owner_okr_targets) as metaResponsavelNome 
                        FROM LMP.okr_general as a
                        LEFT JOIN mak.Emitentes as b on (a.unity_okr=b.EmitentePOID)
                        LEFT JOIN LMP.okr_keyresults as d on (a.id_okr=d.fk_okr_general_id)
                        LEFT JOIN LMP.okr_targets as e on (d.id_okr_kr=e.fk_okr_kr_id)
                        WHERE 1=1
                        AND a.department_okr = 'Comercial'
                        AND d.measure_okr_kr = 'Faturamento'
                        AND e.period_okr_targets LIKE '%s%%'
                        AND a.active_okr = 1
                        AND d.active_okr_kr = 1
                        AND e.active_okr_targets = 1
                        AND a.year_okr = YEAR(CURDATE())
                        %s
                        LIMIT 100 OFFSET 0", $time, $where);
            //s($sql);

            $query = DB::query(Database::SELECT, $sql);
            $response = $query->execute('webteam')->as_array();
            //s($sql,$response);
        }

        if ( isset( $response[0] ) )
        {
            $result = $response[0];
            $this->cache->set( $cache_id, $result, 60*60*24*1 );
        }

        return $result;
    }

    public function action_management()
    {
        $this->cache = Cache::instance('file');

        $get = $this->request->query();

        //Cache
        $cache_id = __FUNCTION__.json_encode($get);
        $this->cache->delete($cache_id);

        $retrieved_data = $this->cache->get( $cache_id );
        //s($retrieved_data);

        if ( $retrieved_data )
        {
            $response = $retrieved_data;
        }else{
            $where = null;

            if ( isset( $get['type'] ) AND !empty( $get['type'] ))
                $where.=sprintf(" AND clienteDivisao = '%s' ", $get['type'] );


            if ( isset( $get['time'] ) AND $get['time'] == 'mes' )
            {
                $table = 'Analytics.pedidos_matriz_mes';
            }else{
                $table = 'Analytics.pedidos_matriz_hoje';
            }

            $sql      = sprintf("SELECT *  FROM %s WHERE 1=1 %s ", $table, $where);
            $query    = DB::query(Database::SELECT, $sql);
            $response = $query->execute()->as_array();

            $this->cache->set( $cache_id, $response, 60*1*1*1 );
            //s($sql,$response);
            //die();
        }

        $array   = array();
        $sellers = array();

        if ( empty($response) )
            return $array;

        foreach ($response as $key => $item)
        {
            $array['type'][self::format_uri($item['clienteDivisao'])]['rows'][] = $item;
        }
        ksort($array, SORT_NUMERIC);


        //Somar Total
        $sumArray = array();

        foreach ( $array['type'] as $k => $item)
        {
            foreach ( $item['rows'] as $kk => $value )
            {
                $sumArray[$value['gerenteClienteID']] = 0;
            }

            foreach ( $item['rows'] as $kk => $value )
            {
                $sumArray[$value['gerenteClienteID']] += $value['produtoValorTotal'];
            }
        }
        //sort($sumArray);
        $sumArray = self::ksortarray( $sumArray );
        $array['sales'] = (array) $sumArray;


        foreach ( $array['type'] as $k => $item)
        {
            foreach ( $item['rows'] as $kk => $value )
            {
                $array['sellers'][$value['gerenteClienteID']] = self::format_uri($value['gerenteCliente']);
            }
        }
        $array['sellers'] = self::ksortarray( $array['sellers'] );

        //Somar Total
        $goalsArray = array();

        foreach ( $array['type'] as $k => $item)
        {
            foreach ( $item['rows'] as $kk => $value )
            {

                if ( isset( $get['time'] ) AND $get['time'] == 'mes' )
                {
                    $lmp = self::lmp_faturamento_segmento_vendedor_diario( $value['pedidoUnidadeID'], $value['clienteDivisaoID'], $value['gerenteClienteID'], 'Individual', 'Mensal');
                }else{
                    $lmp = self::lmp_faturamento_segmento_vendedor_diario( $value['pedidoUnidadeID'], $value['clienteDivisaoID'], $value['gerenteClienteID'], 'Individual', 'Diário');
                }

                //s($lmp);

                if ( isset($lmp['metaDiarioMesAtual'])  )
                {
                    $goalsArray[$value['gerenteClienteID']] = str_replace(',', '.', $lmp['metaDiarioMesAtual']);
                }else{
                    $goalsArray[$value['gerenteClienteID']] = 0;
                }
            }

            foreach ( $item['rows'] as $kk => $value )
            {
                $goalsArray[$value['gerenteClienteID']] -= $value['produtoValorTotal'];

                if ( $goalsArray[$value['gerenteClienteID']] < 0 )
                    $goalsArray[$value['gerenteClienteID']] = 0;
            }
        }

        $goalsArray = self::ksortarray( $goalsArray );
        $array['goals'] = (array) $goalsArray;


        $totalType    = 0;
        $countType    = 0;

        foreach ( $array['type'] as $k => $item)
        {
            $subtotal = 0;
            $subcount = 0;

            foreach ( $item['rows'] as $kk => $value )
            {
                $countType++;
                $subcount++;

                $subtotal  += $value['produtoValorTotal'];
                $totalType += $value['produtoValorTotal'];

                if ( isset( $get['time'] ) AND $get['time'] == 'mes' )
                {
                    $lmp = self::lmp_faturamento_segmento_vendedor_diario( $value['pedidoUnidadeID'], $value['clienteDivisaoID'], false, 'Equipe', 'Mensal' );
                }else{
                    //LMP
                    $lmp = self::lmp_faturamento_segmento_vendedor_diario( $value['pedidoUnidadeID'], $value['clienteDivisaoID'], false, 'Equipe', 'Diário');
                }

                $array['type'][$k]['lmp'] = $lmp;

                $meta = 0;

                if ( isset($lmp['metaDiarioMesAtual']) )
                {
                    $meta = (double) $lmp['metaDiarioMesAtual'];
                }

                $atingido = 0;
                $faltante = 0;

                if ( $meta > 0 )
                {
                    $atingido = round ( ( $subtotal / $meta ) * 100 , 2);
                    $faltante = number_format( $meta , 2, '.', '') - $subtotal;

                    $array['type'][$k]['subtotal']['vendaMetaDia'] = $meta;
                    $array['type'][$k]['subtotal']['vendaMetaDiaFormatado'] =  number_format($meta, 2, ',', '.');
                    $array['type'][$k]['subtotal']['produtoValorSubtotalPorcetagem'] = round ( ( $subtotal / $meta ) * 100 , 2);
                    $array['type'][$k]['subtotal']['vendaFaltanteDiaPorcetagem']     = round ( ( $faltante / $meta ) * 100 , 2);;
                }else{

                    $array['type'][$k]['subtotal']['vendaMetaDia'] = 0;
                    $array['type'][$k]['subtotal']['vendaMetaDiaFormatado'] = 0;
                    $array['type'][$k]['subtotal']['produtoValorSubtotalPorcetagem'] = 0;
                    $array['type'][$k]['subtotal']['vendaFaltanteDiaPorcetagem']     = 0;
                }

                $array['type'][$k]['subtotal']['subtotalPedidos']                = $subcount;
                $array['type'][$k]['subtotal']['vendaFaltanteDia']               = number_format($faltante, 2, ',', '.');
                $array['type'][$k]['subtotal']['produtoValorSubtotal']           = number_format($subtotal, 2, ',', '.');
            }
        }

        $array['type']['total']['produtoValorTotal'] = round($totalType,2);
        $array['type']['total']['totalPedidos']      = $countType;

        //s($array);
        //die();

        return $this->response->body( json_encode( $array ) );
    }

    public function action_management4()
    {
        $this->cache = Cache::instance('file');

        $get = $this->request->query();

        //Cache
        $cache_id = __FUNCTION__.json_encode($get);
        //$this->cache->delete($cache_id);

        $retrieved_data = $this->cache->get( $cache_id );
        //s($retrieved_data);

        if ( $retrieved_data )
        {
            $response = $retrieved_data;
        }else{
            $where = null;

            if ( isset( $get['type'] ) AND !empty( $get['type'] ))
                $where.=sprintf(" AND clienteDivisao = '%s' ", $get['type'] );

            if ( isset( $get['segmento'] ) AND !empty( $get['segmento'] ))
                $where.=sprintf(" AND produtoSegmento = '%s' ", $get['segmento'] );

            if ( isset( $get['time'] ) AND $get['time'] == 'mes' )
            {
                $table = 'Analytics.faturamento_mes';
            }else{
                $table = 'Analytics.faturamento_hoje';
            }

            $sql      = sprintf("SELECT * FROM %s WHERE 1=1 %s ", $table, $where);
            $query    = DB::query(Database::SELECT, $sql);
            $response = $query->execute()->as_array();

            $this->cache->set( $cache_id, $response, 60*1*1*1 );
            //s($sql,$response);
            //die();
        }

        $array   = array();
        $sellers = array();

        if ( empty($response) )
            return $array;

        foreach ($response as $key => $item)
        {
            $array['type'][self::format_uri($item['clienteDivisao'])]['rows'][] = $item;
        }
        ksort($array, SORT_NUMERIC);


        //Somar Total
        $sumArray = array();

        foreach ( $array['type'] as $k => $item)
        {
            foreach ( $item['rows'] as $kk => $value )
            {
                $sumArray[$value['gerenteClienteID']] = 0;
            }

            foreach ( $item['rows'] as $kk => $value )
            {
                $sumArray[$value['gerenteClienteID']] += $value['produtoValorTotal'];
            }
        }
        //sort($sumArray);
        $sumArray = self::ksortarray( $sumArray );
        $array['sales'] = (array) $sumArray;


        foreach ( $array['type'] as $k => $item)
        {
            foreach ( $item['rows'] as $kk => $value )
            {
                $array['sellers'][$value['gerenteClienteID']] = self::format_uri($value['gerenteCliente']);
            }
        }
        $array['sellers'] = self::ksortarray( $array['sellers'] );

        //Somar Total
        $goalsArray = array();

        foreach ( $array['type'] as $k => $item)
        {
            foreach ( $item['rows'] as $kk => $value )
            {

                if ( isset( $get['time'] ) AND $get['time'] == 'mes' )
                {
                    $lmp = self::lmp_faturamento_segmento_vendedor_diario( $value['pedidoUnidadeID'], $value['clienteDivisaoID'], $value['gerenteClienteID'], 'Individual', 'Mensal');
                }else{
                    $lmp = self::lmp_faturamento_segmento_vendedor_diario( $value['pedidoUnidadeID'], $value['clienteDivisaoID'], $value['gerenteClienteID'], 'Individual', 'Diário');
                }

                //s($lmp);

                if ( isset($lmp['metaDiarioMesAtual'])  )
                {
                    $goalsArray[$value['gerenteClienteID']] = str_replace(',', '.', $lmp['metaDiarioMesAtual']);
                }else{
                    $goalsArray[$value['gerenteClienteID']] = 0;
                }
            }

            foreach ( $item['rows'] as $kk => $value )
            {
                $goalsArray[$value['gerenteClienteID']] -= $value['produtoValorTotal'];

                if ( $goalsArray[$value['gerenteClienteID']] < 0 )
                    $goalsArray[$value['gerenteClienteID']] = 0;
            }
        }

        $goalsArray = self::ksortarray( $goalsArray );
        $array['goals'] = (array) $goalsArray;


        $totalType    = 0;
        $countType    = 0;

        foreach ( $array['type'] as $k => $item)
        {
            $subtotal = 0;
            $subcount = 0;

            foreach ( $item['rows'] as $kk => $value )
            {
                $countType++;
                $subcount++;

                $subtotal  += $value['produtoValorTotal'];
                $totalType += $value['produtoValorTotal'];

                if ( isset( $get['time'] ) AND $get['time'] == 'mes' )
                {
                    $lmp = self::lmp_faturamento_segmento_vendedor_diario( $value['pedidoUnidadeID'], $value['clienteDivisaoID'], false, 'Equipe', 'Mensal' );
                }else{
                    //LMP
                    $lmp = self::lmp_faturamento_segmento_vendedor_diario( $value['pedidoUnidadeID'], $value['clienteDivisaoID'], false, 'Equipe', 'Diário');
                }

                $array['type'][$k]['lmp'] = $lmp;

                $meta = 0;

                if ( isset($lmp['metaDiarioMesAtual']) )
                {
                    $meta = (double) $lmp['metaDiarioMesAtual'];
                }

                $atingido = 0;
                $faltante = 0;

                if ( $meta > 0 )
                {
                    $atingido = round ( ( $subtotal / $meta ) * 100 , 2);
                    $faltante = number_format( $meta , 2, '.', '') - $subtotal;

                    $array['type'][$k]['subtotal']['vendaMetaDia'] = $meta;
                    $array['type'][$k]['subtotal']['vendaMetaDiaFormatado'] =  number_format($meta, 2, ',', '.');
                    $array['type'][$k]['subtotal']['produtoValorSubtotalPorcetagem'] = round ( ( $subtotal / $meta ) * 100 , 2);
                    $array['type'][$k]['subtotal']['vendaFaltanteDiaPorcetagem']     = round ( ( $faltante / $meta ) * 100 , 2);;
                }else{

                    $array['type'][$k]['subtotal']['vendaMetaDia'] = 0;
                    $array['type'][$k]['subtotal']['vendaMetaDiaFormatado'] = 0;
                    $array['type'][$k]['subtotal']['produtoValorSubtotalPorcetagem'] = 0;
                    $array['type'][$k]['subtotal']['vendaFaltanteDiaPorcetagem']     = 0;
                }

                $array['type'][$k]['subtotal']['subtotalPedidos']                = $subcount;
                $array['type'][$k]['subtotal']['vendaFaltanteDia']               = number_format($faltante, 2, ',', '.');
                $array['type'][$k]['subtotal']['produtoValorSubtotal']           = number_format($subtotal, 2, ',', '.');
            }
        }

        $array['type']['total']['produtoValorTotal'] = round($totalType,2);
        $array['type']['total']['totalPedidos']      = $countType;

        //s($array);
        //die();

        return $this->response->body( json_encode( $array ) );
    }


    public function action_management3()
    {
        $this->cache = Cache::instance('file');

        $get = $this->request->query();

        //Cache
        $cache_id = __FUNCTION__.json_encode($get);
        $this->cache->delete($cache_id);

        $retrieved_data = $this->cache->get( $cache_id );
        //s($retrieved_data);

        if ( $retrieved_data )
        {
            $response = $retrieved_data;
        }else{
            $where = null;

            if ( isset( $get['type'] ) AND !empty( $get['type'] ))
                $where.=sprintf(" AND clienteDivisao = '%s' ", $get['type'] );

            $sql      = sprintf("SELECT * FROM Analytics.`vendas_mes` WHERE 1=1 %s ", $where);
            $query    = DB::query(Database::SELECT, $sql);
            $response = $query->execute()->as_array();

            $this->cache->set( $cache_id, $response, 60*10*1*1 );
            //s($sql,$response);
            //die();
        }

        $array   = array();
        $sellers = array();

        if ( empty($response) )
            return $array;

        foreach ($response as $key => $item)
        {
            $array['type'][self::format_uri($item['clienteDivisao'])]['rows'][] = $item;
        }
        ksort($array, SORT_NUMERIC);


        //Somar Total
        $sumArray = array();

        foreach ( $array['type'] as $k => $item)
        {
            foreach ( $item['rows'] as $kk => $value )
            {
                $sumArray[$value['gerenteClienteID']] = 0;
            }

            foreach ( $item['rows'] as $kk => $value )
            {
                $sumArray[$value['gerenteClienteID']] += $value['produtoValorTotal'];
            }
        }
        //sort($sumArray);
        $sumArray = self::ksortarray( $sumArray );
        $array['sales'] = (array) $sumArray;


        foreach ( $array['type'] as $k => $item)
        {
            foreach ( $item['rows'] as $kk => $value )
            {
                $array['sellers'][$value['gerenteClienteID']] = self::format_uri($value['gerenteCliente']);
            }
        }
        $array['sellers'] = self::ksortarray( $array['sellers'] );

        //Somar Total
        $goalsArray = array();

        foreach ( $array['type'] as $k => $item)
        {
            foreach ( $item['rows'] as $kk => $value )
            {
                $lmp = self::lmp_faturamento_segmento_vendedor_diario( $value['pedidoUnidadeID'], $value['clienteDivisaoID'], $value['gerenteClienteID'], 'Individual');
                //s($lmp);

                if ( isset($lmp['metaDiarioMesAtual'])  )
                {
                    $goalsArray[$value['gerenteClienteID']] = str_replace(',', '.', $lmp['metaDiarioMesAtual']);
                }else{
                    $goalsArray[$value['gerenteClienteID']] = 0;
                }
            }

            foreach ( $item['rows'] as $kk => $value )
            {
                $goalsArray[$value['gerenteClienteID']] -= $value['produtoValorTotal'];

                if ( $goalsArray[$value['gerenteClienteID']] < 0 )
                    $goalsArray[$value['gerenteClienteID']] = 0;
            }
        }

        $goalsArray = self::ksortarray( $goalsArray );
        $array['goals'] = (array) $goalsArray;


        $totalType    = 0;
        $countType    = 0;

        foreach ( $array['type'] as $k => $item)
        {
            $subtotal = 0;
            $subcount = 0;

            foreach ( $item['rows'] as $kk => $value )
            {
                $countType++;
                $subcount++;

                $subtotal  += $value['produtoValorTotal'];
                $totalType += $value['produtoValorTotal'];

                //LMP
                $lmp = self::lmp_faturamento_segmento_vendedor_diario( $value['pedidoUnidadeID'], $value['clienteDivisaoID'], false, 'Equipe' );
                $array['type'][$k]['lmp'] = $lmp;

                $meta = 0;

                if ( isset($lmp['metaDiarioMesAtual']) )
                {
                    $meta = (double) $lmp['metaDiarioMesAtual'];
                }

                $atingido = 0;
                $faltante = 0;

                if ( $meta > 0 )
                {
                    $atingido = round ( ( $subtotal / $meta ) * 100 , 2);
                    $faltante = number_format( $meta , 2, '.', '') - $subtotal;

                    $array['type'][$k]['subtotal']['vendaMetaDia'] = $meta;
                    $array['type'][$k]['subtotal']['vendaMetaDiaFormatado'] =  number_format($meta, 2, ',', '.');
                    $array['type'][$k]['subtotal']['produtoValorSubtotalPorcetagem'] = round ( ( $subtotal / $meta ) * 100 , 2);
                    $array['type'][$k]['subtotal']['vendaFaltanteDiaPorcetagem']     = round ( ( $faltante / $meta ) * 100 , 2);;
                }else{

                    $array['type'][$k]['subtotal']['vendaMetaDia'] = 0;
                    $array['type'][$k]['subtotal']['vendaMetaDiaFormatado'] = 0;
                    $array['type'][$k]['subtotal']['produtoValorSubtotalPorcetagem'] = 0;
                    $array['type'][$k]['subtotal']['vendaFaltanteDiaPorcetagem']     = 0;
                }

                $array['type'][$k]['subtotal']['subtotalPedidos']                = $subcount;
                $array['type'][$k]['subtotal']['vendaFaltanteDia']               = number_format($faltante, 2, ',', '.');
                $array['type'][$k]['subtotal']['produtoValorSubtotal']           = number_format($subtotal, 2, ',', '.');
            }
        }

        $array['type']['total']['produtoValorTotal'] = round($totalType,2);
        $array['type']['total']['totalPedidos']      = $countType;

        //s($array);
        //die();

        return $this->response->body( json_encode( $array ) );
    }

    public function action_management2()
    {
        $this->cache = Cache::instance('file');

        $get = $this->request->query();

        //Cache
        $cache_id = __FUNCTION__.json_encode($get);
        $this->cache->delete($cache_id);

        $retrieved_data = $this->cache->get( $cache_id );
        //s($retrieved_data);

        if ( $retrieved_data )
        {
            $response = $retrieved_data;
        }else{
            $where = null;

            if ( isset( $get['type'] ) AND !empty( $get['type'] ))
                $where.=sprintf(" AND segmento = '%s' ", $get['type'] );

            $sql      = sprintf("SELECT * FROM Analytics.`vendas_hoje` WHERE 1=1 %s ", $where);
            $query    = DB::query(Database::SELECT, $sql);
            $response = $query->execute()->as_array();

            $this->cache->set( $cache_id, $response, 60*10*1*1 );
            // s($sql,$response);
            // die();
        }

        $array   = array();
        $sellers = array();

        if ( empty($response) )
            return $array;

        foreach ($response as $key => $item)
        {
            $array['type'][self::format_uri($item['segmento'])]['rows'][] = $item;
        }
        ksort($array, SORT_NUMERIC);


        //Somar Total
        $sumArray = array();

        foreach ( $array['type'] as $k => $item)
        {
            foreach ( $item['rows'] as $kk => $value )
            {
                $sumArray[$value['vendedor']] = 0;
            }

            foreach ( $item['rows'] as $kk => $value )
            {
                $sumArray[$value['vendedor']] += $value['valor'];
            }
        }
        //sort($sumArray);
        $sumArray = self::ksortarray( $sumArray );
        $array['sales'] = (array) $sumArray;


        foreach ( $array['type'] as $k => $item)
        {
            foreach ( $item['rows'] as $kk => $value )
            {
                $array['sellers'][$value['vendedor']] = self::format_uri($value['vendedor']);
            }
        }
        $array['sellers'] = self::ksortarray( $array['sellers'] );

        //Somar Total
        $goalsArray = array();

        foreach ( $array['type'] as $k => $item)
        {
            foreach ( $item['rows'] as $kk => $value )
            {
                //$lmp = self::lmp_faturamento_segmento_vendedor_diario( $value['Pedido'], $value['segmento'], $value['vendedor'], 'Individual');
                //s($lmp);

                if ( isset($lmp['metaDiarioMesAtual'])  )
                {
                    $goalsArray[$value['vendedor']] = str_replace(',', '.', $lmp['metaDiarioMesAtual']);
                }else{
                    $goalsArray[$value['vendedor']] = 0;
                }
            }

            foreach ( $item['rows'] as $kk => $value )
            {
                $goalsArray[$value['vendedor']] -= $value['valor'];

                if ( $goalsArray[$value['vendedor']] < 0 )
                    $goalsArray[$value['vendedor']] = 0;
            }
        }

        $goalsArray = self::ksortarray( $goalsArray );
        $array['goals'] = (array) $goalsArray;


        $totalType    = 0;
        $totalEcomm   = 0;
        $countType    = 0;

        foreach ( $array['type'] as $k => $item)
        {
            $subtotal = 0;
            $subcount = 0;
            $subecomm = 0;

            foreach ( $item['rows'] as $kk => $value )
            {
                $countType++;
                $subcount++;

                $subtotal  += $value['valor'];
                $totalType += $value['valor'];

                if ( 140 == $value['vendedor'] or 139 == $value['vendedor'] or 138 == $value['vendedor'] )
                {
                    $subecomm++;
                    $totalEcomm++;
                }

                //LMP
                //$lmp = self::lmp_faturamento_segmento_vendedor_diario( $value['Pedido'], $value['segmento'], false, 'Equipe' );
                //$array['type'][$k]['lmp'] = $lmp;

                $meta = 0;

                if ( isset($lmp['metaDiarioMesAtual']) )
                {
                    $meta = (double) $lmp['metaDiarioMesAtual'];
                }

                $atingido = 0;
                $faltante = 0;

                if ( $meta <= 0 and $value['segmento'] == 'bearings'  )
                {
                    $meta = 144000;
                }

                if ( $meta <= 0 and $value['segmento'] == 'parts'  )
                {
                    $meta = 15000;
                }

                if ( $meta <= 0 and $value['segmento'] == 'auto'  )
                {
                    $meta = 20000;
                }

                if ( $meta <= 0 and $value['segmento'] == 'machines'  )
                {
                    $meta = 150000;
                }

                //s($subtotal , $meta);

                if ( $meta > 0 )
                {
                    $atingido = round ( ( $subtotal / $meta ) * 100 , 2);
                    $faltante = number_format( $meta , 2, '.', '') - $subtotal;


                    $array['type'][$k]['subtotal']['vendaMetaDia'] = $meta;
                    $array['type'][$k]['subtotal']['vendaMetaDiaFormatado'] =  number_format($meta, 2, ',', '.');
                    $array['type'][$k]['subtotal']['produtoValorSubtotalPorcetagem'] = round ( ( $subtotal / $meta ) * 100 , 2);
                    $array['type'][$k]['subtotal']['vendaFaltanteDiaPorcetagem']     = round ( ( $faltante / $meta ) * 100 , 2);;

                    $array['type'][$k]['subtotal']['subtotalPedidos']                = $subcount;
                    $array['type'][$k]['subtotal']['subtotalPedidosEcommerce']       = $subecomm;
                    $array['type'][$k]['subtotal']['vendaFaltanteDia']               = number_format($faltante, 2, ',', '.');
                    $array['type'][$k]['subtotal']['produtoValorSubtotal']           = number_format($subtotal, 2, ',', '.');

                }
            }
        }

        $array['type']['total']['produtoValorTotal']     = round($totalType,2);
        $array['type']['total']['totalPedidos']          = $countType;
        $array['type']['total']['totalPedidosEcommerce'] = $totalEcomm;

        $array['ecommerce'] = array(
            0 => array( 'name' => 'Total de pedidos Geral', 'y' =>  $array['type']['total']['totalPedidos'], 'color' => '#000' ) , 
            1 => array( 'name' => 'Total de pedidos Ecommerce', 'y' =>  $array['type']['total']['totalPedidosEcommerce'], 'color' => '#5E10D6' )
        );

        //s($array);
        //die();

        return $this->response->body( json_encode( $array ) );
    }


    function ksortarray( $array )
    {
        $counter = 0;

        foreach ( $array as $k => $v )
        {
            unset($array[$k]);
            $array[$counter]=$v;
            $counter++;
        }

        return $array;
    }

    function format_uri( $string, $separator = '-' )
    {
        $accents_regex = '~&([a-z]{1,2})(?:acute|cedil|circ|grave|lig|orn|ring|slash|th|tilde|uml);~i';
        $special_cases = array( '&' => 'and', "'" => '');
        $string = mb_strtolower( trim( $string ), 'UTF-8' );
        $string = str_replace( array_keys($special_cases), array_values( $special_cases), $string );
        $string = preg_replace( $accents_regex, '$1', htmlentities( $string, ENT_QUOTES, 'UTF-8' ) );
        $string = preg_replace("/[^a-z0-9]/u", "$separator", $string);
        $string = preg_replace("/[$separator]+/u", "$separator", $string);
        return $string;
    }

    public function action_index()
    {      
        $view =  View::factory("index");
        $this->response->body($view);
    }

    public function action_today()
    {
        $where = "";
        if($this->request->param('division')) $where.= sprintf(" AND segmento = '%s'", $this->request->param('division') );
        $sql = sprintf("SELECT SUM(valor) as valor FROM Analytics.`vendas_hoje` WHERE 1=1 %s ", $where);

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

        $view = number_format($result[0]['valor'], 2, ',', '.');

        $this->response->body($view);
    }

    public function action_today2()
    {
        $where = "";
        if($this->request->param('division')) $where.= sprintf(" AND segmento = '%s'", $this->request->param('division') );
        $sql = sprintf("SELECT SUM(valor) as valor FROM Analytics.`vendas_hoje` WHERE 1=1 %s ", $where);

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute('makread')->as_array();

        $view = number_format($result[0]['valor'], 2, ',', '.');

        $this->response->body($view);
    }

    public function action_goals()
    {
        $where = "";
        if($this->request->param('division')) $where.= sprintf(" AND segmento = '%s'", $this->request->param('division') );
        $sql = sprintf("SELECT SUM(valor) as valor FROM Analytics.`vendas_hoje` WHERE 1=1 %s ", $where);

        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

        $i = $this->request->param('division');

        $goal = 0;

        switch ($i) {
            case 'bearings':
                $goal = "150000";
                break;
            case 'machines':
                $goal = "150000";
                break;
            case 'parts':
                $goal = "30000";
            case 'auto':
                $goal = "30000";
        }

        $porcentagem = round( $result[0]['valor'] / $goal * 100 , 0);

        $this->response->body($porcentagem);
    }

    public function action_moto()
    {
        $total = 0;

        $where = "";

        $sql = sprintf("SELECT 
                            a.id as pedidoID, 
                            a.EmissorPOID as pedidoUnidadeID, 
                            h.Fantasia as pedidoUnidadeNome, 
                            a.nop as pedidoNaturezaOperacaoID, 
                            b.nop as pedidoNaturezaOperacaoNome,
                            a.data as pedidoDataEmissao,
                            a.datae as pedidoDataEntrega,
                            (CASE
                                WHEN (a.EmissorPOID = 1) THEN (SELECT DataGeracao FROM NFE.01103171000109_NFe as nfesp WHERE nfesp.NFePOID=a.nf)
                                WHEN (a.EmissorPOID = 2) THEN (SELECT DataGeracao FROM NFE.01103171000290_NFe as nfesp2 WHERE nfesp2.NFePOID=a.nf)
                                WHEN (a.EmissorPOID = 3) THEN (SELECT DataGeracao FROM NFE.01103171000370_NFe as nfesp_loja WHERE nfesp_loja.NFePOID=a.nf)
                                WHEN (a.EmissorPOID = 5) THEN (SELECT DataGeracao FROM NFE.01103171000532_NFe as nfece WHERE nfece.NFePOID=a.nf)
                                WHEN (a.EmissorPOID = 6) THEN (SELECT DataGeracao FROM NFE.01103171000613_NFe as nfesc WHERE nfesc.NFePOID=a.nf)
                                WHEN (a.EmissorPOID = 7) THEN (SELECT DataGeracao FROM NFE.01103171000702_NFe as nfego WHERE nfego.NFePOID=a.nf)
                                WHEN (a.EmissorPOID = 8) THEN (SELECT DataGeracao FROM NFE.01103171000885_NFe as nfesp_bf WHERE nfesp_bf.NFePOID=a.nf)
                            ELSE '01/01/1900'
                        END) as pedidoDataNotaFiscal, 
                        a.idcli as clienteID, 
                        g.segment as clienteDivisao, 
                        d.novo_segmento as clienteSegmentoID, 
                        e.segmento as clienteSegmentoNome, 
                        d.atividade_id as clienteAtividadeID, 
                        f.atividade as clienteAtividadeNome, 
                        d.nome as clienteNome, 
                        d.estado as clienteUF, 
                        a.emissor as vendedorEmissaoID, 
                        (SELECT nick FROM users u WHERE u.id=a.emissor) as vendedorEmissaoNome,
                        a.vendedor as vendedorID, 
                        (SELECT nick FROM users u WHERE u.id=a.vendedor) as vendedorNome,
                        SUM( c.quant * c.valor_base ) as pedidoSubtotal 
                        FROM hoje as a
                        LEFT JOIN nop as b on (b.id_nop=a.nop)
                        LEFT JOIN hist as c on (c.pedido=a.id)
                        LEFT JOIN clientes as d on (a.idcli=d.id)
                        LEFT JOIN novo_segmento as e on (e.id=d.novo_segmento)
                        LEFT JOIN clientes_atividade as f on (f.id = d.atividade_id)
                        LEFT JOIN segments as g on (g.id_segments = f.segmento_id)
                        LEFT JOIN Emitentes as h on (a.UnidadeLogistica=h.EmitentePOID)
                        WHERE 1=1
                        %s
                        AND d.novo_segmento = 2
                        AND a.id > 1182796
                        AND a.nop in ('27','28','51')

                        GROUP BY MONTH(pedidoDataNotaFiscal), YEAR(pedidoDataNotaFiscal)

                        HAVING 1=1
                        AND YEAR(pedidoDataNotaFiscal) = 2021
                        AND MONTH(pedidoDataNotaFiscal) = 6


                         ", $where);

        $query = DB::query(Database::SELECT, $sql);
        $response = $query->execute()->as_array();

        //s($result);

        if ( isset( $response[0] ) )
        {
            $total =  $response[0]['pedidoSubtotal'];
        }

        $this->response->body($total);
    }

    public function action_order()
    {
        $this->response->body(json_encode("1"));
    }

    public function action_start()
    {

        $view = '<div  hx-trigger="load  delay:2s" hx-get="/metrics/index/step1">Start</div>';

        $this->response->body($view);

    }

    public function action_step1()
    {

        $view = '<div  hx-trigger="load  delay:2s" hx-get="/metrics/index/step2">Step1</div>';

        $this->response->body($view);

    }

    public function action_step2()
    {

        $view = '<div  hx-trigger="load  delay:2s" hx-get="/metrics/index/end">Step2</div>';

        $this->response->body($view);

    }

    public function action_end()
    {

        $view = '<div  hx-trigger="click  delay:1s" hx-get="/metrics/index/start">End</div>';

        $this->response->body($view);

    }


}