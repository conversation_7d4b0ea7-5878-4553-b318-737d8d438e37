<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Seller extends Controller_Website {

    public function before()
    {
        parent::before();

        $this->cons['basedir'] = strtolower($this->request->directory());
        $this->cons['base'] = $_SERVER['HTTP_X_FORWARDED_PREFIX'].strtolower($this->request->directory());
        $this->cons['self'] = $this->cons['base'].'/'.$this->request->controller().'/'.$this->request->action().'/';
        // $this->cons['segmento'] = $this->request->param('division');
        $this->cons['iduser']   = $_SESSION['MM_Userid'];
        $this->cons['name']     = $_SESSION["MM_Nick"];
        $this->cons['idseg']    = $this->request->param('division');
        
        $this->cons['idseller'] = $this->request->param('xtras');
        
        if($this->request->param('xtras')==0) $this->cons['idseller'] = $_SESSION['MM_Userid'];
        
        $this->cons['nameseg']  = 'machines' ; //parent::get_segment_by_id($this->cons['idseg']);
        
        
        if( $_SESSION["MM_Nivel"]>4) 
        {
             
             if($this->cons['idseller']<>$_SESSION["MM_Userid"]) $this->cons['name'] =  $this->cons['idseller'];
             
             $this->cons['iduser']   = $this->cons['idseller'];
             
        }else{
            if ( 6 == $this->cons['idseg'] and (  73 == $_SESSION['MM_Userid'] or  225 == $_SESSION['MM_Userid']) )
            {
                $this->cons['name'] =  $this->request->param('division');
                
                
            }
        }

        if( empty($this->cons['idseg'])  )      $this->cons['idseg']='1';

        if(1==$_SESSION['MM_Userid'])
        {
            // /s($this->cons); 
          //   s($this->request);
            // s($_SERVER);
            // s($_SESSION);
        }

    }
    
    public function action_index()
    {
        
        $sellers = self::cv();
        
        foreach($sellers as $k => $v )
        {
            $data[] = self::data($v['idven']);
        }
        
        // s($data);
        
        // DIE();
        
         $theme='light';
        $view = '';
         // s( $this->pagination);
        $data['pagination'] = $this->pagination;
        $data['theme'] = 3;
        
        $view= parent::tablefy( $data );
      
      
       
        $response = parent::pagefy($view,$theme);

        $this->response->body($response);

    }
    
    
    public function action_charts()
    {

        $addr = '/'.$this->request->param('division').'/'.$this->request->param('xtras');
        
        $view = View::factory('seller-home')
                                    ->set('addr',$addr);

        $this->response->body($view);
    }


    public function action_process()
    {

        $addr = '/'.$this->request->param('division').'/'.$this->request->param('xtras');
        
        $view = View::factory('seller')
                                    ->set('addr',$addr);

        $this->response->body($view);
    }
   
 


    private function data($seller=1)
    {
//         echo '

// {"customers":226,"inativos":74,"ativos90":118,"ativos30":121,"vendas_dia":52697,"inativados":25,"tickets":45,"vendas_mes":718080,"vendas_mes_passado":1122813,"vendas_meta_mes":1070992,"tx_conversao_90":52,"tx_conversao_30":54,"tx_abandono":33,"tx_inativados":11,"vendas_meta_dia":53550,"ticket_meta_dia":30,"txvendas_mes":67,"tx_vendas_dia":98,"tx_ticket_dia":150}';

//         return;
        $result = self::sql($seller);
        // s($result);
        $tt = [];
        if(!empty($result))
        { 

            $tt['VendasMetaMes']=$tt['VendasMesPassado']= $tt['VendasMes']= $tt['Tickets']= $tt['Inativados'] = $tt['VendasDia'] = $tt['Ativos30'] = $tt['Ativos90'] = $tt['inativos'] = 0;
            $tt['Nick'] =   $result[0]['nick'];
            $tt['Customers'] = (int) $result[0]['Customers'];
            $tt['Tickets']   = (int) $result[0]['Tickets'];
            $tt['TicketsMes']   = (int) $result[0]['Tickets_mes'];
            $tt['Ativos90'] =  (int) $result[0]['c90'];
            $tt['Ativos30'] =  (int) $result[0]['c30'];
            $tt['Inativados'] =  (int) $result[0]['inativados'];
            $tt['Inativos'] =  self::abandonados( $this->cons['iduser']);
            $tt['ComprasAno'] = $result[0]['compras_ano'];

            foreach($result as $key => $val)
            {

                $tt['VendasDia']+= $val['compras_hoje'] ;
                $tt['VendasMes']+= $val['compras_mes'] ;
                $tt['VendasMesPassado']+= $val['mes_passado'] ;
                $tt['VendasMetaMes']+= ($val['mes_passado']+$val['mes_retrasado'])/2 ;
                
            }

            $tt['VendasMetaAno'] = $tt['VendasMetaMes']*11 ;

            $tt['TxConversao_90']  =  round($tt['Ativos90']/ $tt['Customers'],2)*100 ;
            $tt['TxConversao_30']  =  round($tt['Ativos30']/ $tt['Customers'],2)*100 ;
            $tt['TxAbandono']      =  round($tt['Inativos']/ $tt['Customers'],2)*100 ;
            $tt['TxInativados']    =  round($tt['Inativados']/ $tt['Customers'],2)*100 ;
            if($tt['VendasMetaMes']< 100000)
            {
                $tt['VendasMetaMes'] = 100000;

            }
            $tt['VendasMetaDia'] =  round($tt['VendasMetaMes']/20,0) ;
            $tt['TxVendasDia']    =  round( ($tt['VendasDia']  / $tt['VendasMetaDia']*100),0) ;
            
            $tt['TicketMetaDia'] =  max(30, $tt['Customers']*.10) ;
            $tt['TicketMetaMes'] =  $tt['TicketMetaDia']*22 ;

            
            $tt['TxVendasMes']    =  round( ($tt['VendasMes']  / $tt['VendasMetaMes']*100),0) ;
            if($tt['VendasMetaAno'] > 0 )
            {
                $tt['TxVendasAno']    =  round( ($tt['ComprasAno']  / $tt['VendasMetaAno']*100),0) ;
            }else{
                $tt['txVendasAno']    =  0 ;
            }
            
            $tt['TxTicketDia']    =  round( ($tt['Tickets']     / $tt['TicketMetaDia']*100),0) ;
            $tt['TxTicketMes']    =  round(( $tt['TicketsMes'] / $tt['TicketMetaMes']*100),0) ;
            
            // $tt['VendasDia'] =  number_format($tt['VendasDia'],0,',','.') ;
            // $tt['VendasMes'] =  number_format($tt['VendasMes'],0,',','.') ;
            // $tt['VendasMetaDia'] = number_format($tt['VendasMetaDia'],0,',','.') ;
            // $tt['VendasMetaMes']  = number_format($tt['VendasMetaMes'],0,',','.') ;
            // $tt['VendasMetaAno']  = number_format($tt['VendasMetaAno'],0,',','.') ;
            // $tt['TicketMetaDia'] =  number_format($tt['TicketMetaDia'],0,',','.') ;
            // $tt['TicketMetAMes'] =  number_format($tt['TicketMetaMes'],0,',','.') ;



           
        }

        return $tt ; 
        
        $this->response->body(json_encode($tt)); 
       // s($result,$tt);
       

    }


    private function sql($seller)
    {
        
     

        $w=sprintf(" AND cv.idseg='%s'",$this->cons['idseg']);


        $sql = sprintf("
                    
                    SELECT  u.*, 
                            c.status_crm,
            
                    count(DISTINCT c.id) as Customers,
                    
                    (SELECT count(DISTINCT ch.id) FROM crm.chamadas ch WHERE ch.user_id=cv.idven AND DATEDIFF(NOW(), data) =0  ORDER BY ch.id DESC LIMIT 1 ) as Tickets,
                    
                    (SELECT count(DISTINCT ch.id) FROM crm.chamadas ch WHERE ch.user_id=cv.idven AND MONTH(NOW()) = MONTH(data) AND YEAR(NOW()) = YEAR(data) LIMIT 1 ) as Tickets_mes,
                   
                 
                    (SELECT COUNT(DISTINCT c.id) FROM clientes c LEFT JOIN hoje h ON (h.idcli=c.id) WHERE c.vendedor = cv.idven AND h.data IS NULL) AS inativados,
                   
                            (   SELECT count(DISTINCT h.idcli) 
                                FROM hoje h, hist hi, inv i, produtos p  
                                WHERE   h.id > 1220000 and 
                                         DATEDIFF(NOW(), h.data) < 91 AND 
                                        h.vendedor=cv.idven and  
                                        h.id=hi.pedido and 
                                        hi.isbn=i.id and 
                                         i.idcf=p.id  AND 
                                        ( nop=27 OR nop=28 OR nop=51)  )
                    AS  c90,
                    
                    
                            (   SELECT count(DISTINCT h.idcli) 
                                FROM hoje h, hist hi, inv i, produtos p  
                                WHERE   h.id > 1220000 and 
                                        DATEDIFF(NOW(), h.data) < 31 AND 
                                        h.vendedor=cv.idven and  
                                        h.id=hi.pedido and 
                                        hi.isbn=i.id and 
                                         i.idcf=p.id  AND 
                                        ( nop=27 OR nop=28 OR nop=51)  )
                    AS  c30,
                   
                    ROUND(
                            (   SELECT SUM(hi.quant*hi.valor_base)  
                                FROM hoje h, hist hi, inv i, produtos p  
                                WHERE   h.id > 1220000 and 
                                        YEAR(h.data)= YEAR(CURRENT_DATE - INTERVAL 2 MONTH) AND 
                                        MONTH(h.data)=  MONTH(CURRENT_DATE - INTERVAL 2 MONTH) and 
                                        h.vendedor=cv.idven and  
                                        h.id=hi.pedido and 
                                        hi.isbn=i.id and 
                                         i.idcf=p.id  AND 
                                        ( nop=27 OR nop=28 OR nop=51)  )
                        ,0) AS  mes_retrasado,

                        ROUND(( SELECT SUM(hi.quant*hi.valor_base)  
                                FROM hoje h, hist hi, inv i, produtos p  
                                WHERE   h.id > 1220000 and 
                                        YEAR(h.data)= YEAR(CURRENT_DATE - INTERVAL 1 MONTH) AND 
                                        MONTH(h.data)=  MONTH(CURRENT_DATE - INTERVAL 1 MONTH) and  
                                        h.vendedor=cv.idven and  
                                        h.id=hi.pedido and 
                                        hi.isbn=i.id and 
                                        i.idcf=p.id  AND 
                                        ( nop=27 OR nop=28 OR nop=51)  
                    ),0) AS  mes_passado,
                    
                       ROUND((SELECT SUM(hi.quant*hi.valor_base)  
                            FROM hoje h, hist hi, inv i, produtos p  
                            WHERE   h.id > 1220000 and 
                                    YEAR(h.data)=YEAR(NOW()) AND 
                                    h.vendedor=cv.idven AND
                                    h.id=hi.pedido and 
                                    hi.isbn=i.id and 
                                    i.idcf=p.id and 

                                    ( nop=27 OR nop=28 OR nop=51)  
                           ),0) AS  compras_ano,

                    ROUND((SELECT SUM(hi.quant*hi.valor_base)  
                            FROM hoje h, hist hi, inv i, produtos p  
                            WHERE   h.id > 1220000 and 
                                    YEAR(h.data)=YEAR(NOW()) AND 
                                    MONTH(h.data)=MONTH(now()) and 
                                    h.vendedor=cv.idven AND
                                    h.id=hi.pedido and 
                                    hi.isbn=i.id and 
                                    i.idcf=p.id and 

                                    ( nop=27 OR nop=28 OR nop=51)  
                           ),0) AS  compras_mes,

                    ROUND(( SELECT SUM(hi.quant*hi.valor_base)  
                            FROM hoje h, hist hi, inv i, produtos p  
                            WHERE   h.id > 1220000 and 
                                    YEAR(h.data)=YEAR(NOW()) AND 
                                    MONTH(h.data)=MONTH(now()) and 
                                    DAY(h.data)=DAY(now()) and 
                                    h.id=hi.pedido and 
                                    hi.isbn=i.id and 
                                    i.idcf=p.id and 
                                    p.segmento = '%s' and 
                                    ( nop=27 OR nop=28 OR nop=51) and 
                                    h.vendedor=cv.idven )
                                    ,0) 
                                    AS  compras_hoje

            FROM clientes c
            LEFT JOIN mak.clientes_vendedores cv ON (cv.idcli=c.id)
            LEFT JOIN mak.rolemak_users u ON (u.id=cv.idven)
            WHERE cv.ativo=1 AND cv.idven=%s %s
            GROUP BY cv.idven
            LIMIT 1000
            ", $this->cons['nameseg'],$seller, $w );

        $query = DB::query(Database::SELECT, $sql);
        return $query->execute()->as_array();
    }

  private function abandonados($idven)
    {
        
         $sql = sprintf("SELECT h.idcli, MAX(data) AS d
                        FROM  hist hi, inv i, produtos p,hoje h
                        LEFT JOIN mak.clientes_vendedores cv ON (cv.idcli=h.idcli)
                        WHERE   h.id > 1220000 and 
                                cv.idven=%s and  
                                h.id=hi.pedido and 
                                hi.isbn=i.id and 
                                 i.idcf=p.id  AND 
                                ( nop=27 OR nop=28 OR nop=51) AND
                                 p.segmento = '%s' 
                        GROUP BY idcli
                        HAVING TO_DAYS(NOW()) - TO_DAYS((d)) > 90", 
                        $idven, $this->cons['nameseg']
                        );
                        
        $query = DB::query(Database::SELECT, $sql);
        $resp = $query->execute()->as_array();
        return (count($resp));
    }    


   private function cv()
    {

        $sql = sprintf("
            SELECT cv.idseg,idven,nick,COUNT(cv.idcli) idclis 
            FROM mak.clientes_vendedores cv 
            LEFT JOIN mak.users u ON (u.id=cv.idven)
            WHERE ativo=1 AND cv.idseg = 1
            GROUP BY cv.idven, cv.idseg
            ORDER BY idseg,nick
            LIMIT 20
            ");

        $query = DB::query(Database::SELECT, $sql);
        
        
        $result = $query->execute()->as_array();	
        
        // s($result);
        
         return $result ;

        

    }
 
} 