<?php

class Controller_Server extends Controller_Websession
{


    public function action_index()
    {      
        
        s($_SERVER);
        // $view =  View::factory("index");
        // $this->response->body($view);
    }


    public function action_start()
    {

        $view = '<div  hx-trigger="click  delay:2s" hx-get="/metrics/server/step1">Start</div>';
        
        // $view = '<div _=" on load wait 2s 
        //         then fetch /cdn/explore/cart.html 
        //         put the result into  #view
        //         then wait 5s 
        //         then fetch /metrics/server/step1
        //         put the result into  my.outerHTML
        // ">Start</div>';

        $this->response->body($view);

    }

    public function action_step1()
    {

        $view = '<div hx-swap="outerHTML" hx-trigger="click" hx-get="/metrics/server/step2"></div>
                    <div hx-trigger="load" hx-target="#view" hx-get="/cdn/explore/landing.html">begin</div>';

        $this->response->body($view);

    }

    public function action_step2()
    {

        $view = '<div hx-swap="outerHTML"  hx-trigger="click" hx-get="/metrics/server/end">Step2</div>';

        $this->response->body($view);

    }

    public function action_end()
    {

        $view = '<div  hx-trigger="click" hx-get="/metrics/server/start">End</div>';

        $this->response->body($view);

    }


}