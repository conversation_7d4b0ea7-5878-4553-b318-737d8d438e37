<?php

class Controller_Siscori extends Controller_Website
{

   
   public function before()
    {
         parent::before();
         
    }


    public function action_index()
    {
        
        
        $v='home';
        $segid = $this->request->param('division');
        $brand = $this->request->param('xtras');
        // die('sis');
        
         $order=" s.id desc ";
        $having="";
        $where="";
        $group="data,nfe";
        
        
        if($this->request->query('order')) $order= sprintf(" %s ", $this->request->query('order'));
        if($this->request->query('nome')) $where.= sprintf(" AND s.nome LIKE '%s' ", '%'.$this->request->query('nome').'%');
        if($this->request->query('ncm')) $where.= sprintf(" AND s.ncm LIKE '%s' ", $this->request->query('ncm').'%');
        
       
        if($this->request->query('qty')) $having= sprintf(" Having qty %s ", $this->request->query('qty'));
            
        $select_group='';    
        if($this->request->query('group'))
        {
            // (SELECT SUM( IF( YEAR(ssw.data)=2023 AND (ssw.peso/ssw.volumes*2)>40,round(ssw.volumes/2,0),0))  FROM Analytics.ssw WHERE ssw.cnpj=s.cnpj)  as qty_2023,
            $group= strtolower(trim($this->request->query('group')));
            $select_group= "    
                                (SELECT  SUM(IF(hi.valor_base>0,hi.quant,0))  
                                    FROM mak.hoje h 
                                    LEFT JOIN mak.hist hi ON (hi.pedido=h.id) 
                                    LEFT JOIN inv i ON (i.id=hi.isbn) 
                                    LEFT JOIN mak.produtos p ON (p.id=i.idcf) 
                                    WHERE YEAR(h.data) = 2023 AND h.nop IN (27,28,51,76) AND h.idcli=c.id AND p.segmento='machines'
                                    GROUP BY h.idcli
                                ) as rolemak_23,
                               
                               '' as Diff23,
                                '' as Diff,
                                Meso,
                                NomeMeso,
                                Micro,
                                NomeMicro,
                                cep,
                                r.global_ranking GR,
                                r.rolemak_ranking_history RR,
                                r.rolemak_ranking_now RN,
                                
                                sum(volumes) as volumes,
                                sum(peso) AS Peso_Total, 
                                round(peso/volumes*2,0) as Peso_Unit,";
                                
                                
             $order=" Subtotal desc ";
             $v=$group;


            
        }         
            
          $sql = sprintf("SELECT ncm,ordem,anomes,porigem,nome,replace(usd,',','.') usd, qtd,replace(sub,',','.') sub,porto
                               
                            FROM Analytics.siscori s 
                            WHERE 1=1 %s
                           
                           
                          
                            LIMIT 10000", $where,$group,$having,$order  );
             $query = DB::query(Database::SELECT, $sql);
             $result = $query->execute()->as_array();
             
            //  s( $result);
              foreach($result as $key => $val)
             {
                $result[$key]['counter'] = $key+1;

                $result[$key]['sub'] = round($val['sub'],2);
                
                if(strpos($val['nome'], $this->request->query('nome')) !== false)
                {
                    
                      
                   $result[$key]['nome'] = str_replace($this->request->query('nome'),'<span class="text-red-500 font-bold">'.$this->request->query('nome').'</span>', $val['nome']);
                   
                  
                   
                }
                
               $result[$key]['nome'] = str_replace('GBR','<span class="text-orange-500 font-bold">GBR</span>',  $result[$key]['nome']);
             }
        
        
             
        $view = parent::mustache($result,'siscori/'.$v);
        
        $this->response->body($view);
                // = $this->action_connect_SELECT($sql);
    }
    

    public function action_html()
    {
        $segid = $this->request->param('division');
        $id = $this->request->param('xtras');
      //  s($id);
             
         $sql = sprintf("SELECT html  FROM Analytics.ssw s 
                          
                            WHERE id = %s
                          
                            LIMIT 1", $id  );
             $query = DB::query(Database::SELECT, $sql);
             $result = $query->execute()->as_array();
             
            // echo $result[0]['html'];
            
            $view = parent::mustache($result,'ssw/ssw-html');

            $this->response->body($view);
        
    }


}