<?php

class Controller_Sql extends Controller_Website
{

   
   public function before()
    {
         parent::before();
    }


    public function action_index()
    {     
     
      // s($this->request->post());
      if($_SESSION['MM_Userid'] > 1)  die('.');
      if(substr(trim(strtolower($this->request->post('s'))),0,6) <>'select')
      {
        $sql='SELECT * FROM mak.inv WHERE 1';
        $view='';
       echo  $view     = View::factory('sql')                        
                        ->set('sql',$sql )
                        ->set('data',$view ); 
        return ; //die('Not Select');
      }else{
        
    
          
          $sql = $this->request->post('s');
          $limit = 10;
          if($lim = $this->request->query('limit') and $lim < 200) $limit = $this->request->query('limit');
          
          $sql = sprintf("%s  LIMIT %s",$sql,$limit);
            
            
          $query = DB::query(Database::SELECT, $sql);
            
          $resp = $query->execute()->as_array();	
          $resp['theme'] = 3; 
          if(!empty($resp))
          {
            $view= parent::tablefy( $resp );
            
            // s($data);
          // $view = parent::pagefy($view );
            // s($_SERVER);
            
            // $view     = View::factory('sql-result')                        
            //                 ->set('sql',$sql )
            //                 ->set('data',$view ); 
          }
      }  
                        
      $this->response->body($view);
        
    }

}