<?php

class Controller_Ssw extends Controller_Website
{

    public function before()
    {
        parent::before();
        $this->initializeRequestParams();
    }

    private function initializeRequestParams()
    {
        $this->cnpj = $this->request->query('cnpj') ?? null;
        $this->segid = $this->request->param('division');
        $this->brand = $this->request->param('xtras');
        $this->dateArray = explode('-', $this->request->query('data'));
        $this->sqlOrder = $this->request->query('order') ?? " s.id desc ";
        $this->sqlSeller = $this->request->query('seller') ?? null;
        $this->sellerCnpj = $this->getSellerCnpj($this->sqlSeller);
        $this->sqlBairro = $this->request->query('bairro') ?? null;
        $this->sqlCidade = $this->request->query('cidade') ?? null;
        $this->sqlMeso = $this->request->query('meso') ?? null;
        $this->sqlEstado = $this->request->query('estado') ?? null;
        $this->sqlGroup = $this->request->query('group') ?? 'nfe';
        $this->modelo = $this->request->query('group') ?? 'nfe';
        $this->nfe = $this->request->query('group') ?? 'nfe';
        $this->uri_original = $_SERVER['REQUEST_URI'];
        $this->setUrlsBasedOnSeller($this->sqlSeller);
        $this->view = "ssw";
        $this->sqlHaving = "";
        $this->where = "";
    }

    private function getSellerCnpj($seller)
    {
        switch ($seller) {
            case 'welttec':
                return '08088938000893';
            case 'ss':
                return '05013910000475';
            default:
                return null;
        }
    }

    private function setUrlsBasedOnSeller($seller)
    {
        $url = str_replace('//', '/', $_SERVER['HTTP_X_FORWARDED_PREFIX'] . $_SERVER['REQUEST_URI']);
        if ($seller === 'rolemak') {
            $this->uri_green = $url;
            $this->uri_blue = str_replace('rolemak', 'welttec', $url);
            $this->uri_red = str_replace('rolemak', 'ss', $url);
        } elseif ($seller === 'welttec') {
            $this->uri_blue = $url;
            $this->uri_green = str_replace('welttec', 'rolemak', $url);
            $this->uri_red = str_replace('welttec', 'ss', $url);
        } elseif ($seller === 'ss') {
            $this->uri_red = $url;
            $this->uri_green = str_replace('=ss', '=rolemak', $url);
            $this->uri_blue = str_replace('=ss', '=welttec', $url);
        }
    }

    public function action_index()
    {

        self::order();

        self::having();

        self::where();

        self::group();

        self::sql();

        self::normalize();

        self::show();

    }

    public function action_empty()
    {

        self::order();

        self::having();

        self::where();

        self::group();

        self::sql();


         if($this->request->query('nfe'))
        {
            $nfex= explode('1 0',strtolower($this->request->query('nfe')));
            $nfe =$nfex[1].'0';
            // s($nfe);

            $nfes= explode(' or ',strtolower($this->request->query('nfe')));

        }




       if($this->request->query('nfe'))
        {
            $nfex= explode('1 0',strtolower($this->request->query('nfe')));
            $nfe =$nfex[1].'0';
            // s($nfe);

            $nfes= explode(' or ',strtolower($this->request->query('nfe')));

        }


    $resultIndex= [];
    foreach($this->result as $key => $value)
    {
        $nfex= explode('1 0',$value['nfe']);

        $nf = (int) $nfex[1] ;

        $nfeFound['nfe'][]= $nf;

        $resultIndex[$nf]=$value;
    }

    // s($resultIndex,$nfeFound);

    $counter = (int) $nfe;

    if(isset($value['nfe']))
    {
        $nfex= explode('1 0',$value['nfe']);
        $nf = (int) $nfex[1] ;

    }


    for($x=0; $x<10; $x++)
    {
        //  s($counter,$nfeFound);
        $fillArray= array(
        'id' => "",
        'cnpj' =>  "",
        'nfe' =>  $counter,
        'data' =>  "",
        'volumes' =>  "",
        'peso' =>  "",
        'html' =>  "",
        'seller' =>  ""
        );

        if(!isset($nfeFound['nfe']) or !in_array($counter,$nfeFound['nfe']))
        {
            $resultIndex[$x]= $fillArray;

            // $seller = $this->sellerCnpj;
            // if(!$this->sellerCnpj) $seller='welttec';

            $resultIndex[$x]['link']= sprintf('https://ssw.inf.br/app/tracking/%s/%s',$this->sellerCnpj,$counter);
            $resultIndex[$x]['linkEsm']= sprintf('https://dev.office.internut.com.br/sqs/esm/?cnpj=%s&nf=%s',$this->sellerCnpj,$counter);




        }else{

            $resultIndex[$x]= $resultIndex[$counter];
            unset($resultIndex[$counter]);

        }
            // s($x,$counter,$nf);
        $counter++;
    }


    // s($resultIndex);

      $view = parent::mustache($resultIndex,'ssw/empty');

        $this->response->body($view);

    }

    private function order()
    {
        if($this->request->query('order')) $this->sqlOrder= sprintf(" %s ", $this->request->query('order'));
    }

    private function group()
    {

        if($this->request->query('group'))
        {

            $this->sqlGroup= strtolower(trim($this->request->query('group')));

            $this->sqlOrder=" Subtotal desc ";


            $os = array("cidade","estado");

            if (in_array($this->sqlGroup, $os))
            {

                $this->view = 'geral'; //$this->sqlGroup;

                $this->modelo = 'c.'.$this->sqlGroup;
            }



            $os = array("bairro");

            if (in_array($this->sqlGroup, $os))
            {

                $this->view = 'geral'; //$this->sqlGroup;

                $this->modelo = sprintf(" CONCAT(c.cidade,' ', c.%s) ",$this->sqlGroup);
            }


            $os = array("cnpj");

            if (in_array($this->sqlGroup, $os))
            {
                $this->modelo = 's.'.$this->sqlGroup;

                // $this->view = $this->sqlGroup;
            }


            $os = array("seller");

            if (in_array($this->sqlGroup, $os))
            {
                $this->modelo = 's.'.$this->sqlGroup;

                // $this->view = $this->sqlGroup;
            }


            // if('bairro' == $this->sqlGroup)
            //     $this->sqlGroup = sprintf(" cidade,%s",$this->sqlGroup);

            // if('cnpj,seller' == $this->sqlGroup)
            //     $this->view='cnpj';
        }

         if($this->sqlGroup=="cidade" AND !empty($this->request->query('data')))
        {
            if(isset($this->dateArray[0]))
            {
                $city = self::city($this->dateArray[0] );
            }
        }

        if($this->sqlGroup==" cidade,bairro" AND !empty($this->request->query('data')))
        {
            if(isset($this->dateArray[0]))
            {
                $block = self::block($this->dateArray[0] );
            }
        }

    }

    private function having()
    {

        if($this->request->query('qty')) $this->sqlHaving= sprintf(" Having qty %s ", $this->request->query('qty'));

    }

    private function where()
    {

        if($this->request->query('rrh')) $this->where.= sprintf(" AND  r.rolemak_ranking_history >0 AND r.rolemak_ranking_history <= %s ", $this->request->query('rrh'));

        if($this->request->query('rrn')) $this->where.= sprintf(" AND  r.rolemak_ranking_now >0 AND r.rolemak_ranking_now <= %s ", $this->request->query('rrn'));

        if($this->request->query('gr')) $this->where.= sprintf(" AND  r.global_ranking >0 AND r.global_ranking <= %s ", $this->request->query('gr'));

        if($this->request->query('meso')) $this->where.= sprintf(" AND mun.meso LIKE '%s' ", $this->request->query('meso').'%');

        if($this->request->query('micro')) $this->where.= sprintf(" AND Micro LIKE '%s' ", $this->request->query('micro').'%');

        if($this->request->query('nomemicro')) $this->where.= sprintf(" AND NomeMicro LIKE '%s' ", $this->request->query('nomemicro').'%');

        if($this->request->query('regiao')) $this->where.= sprintf(" AND regiao='%s' ", $this->request->query('regiao'));

        if($this->request->query('estado')) $this->where.= sprintf(" AND estado='%s' ", $this->request->query('estado'));

        if($this->request->query('cidade')) $this->where.= sprintf(" AND cidade='%s' ", $this->request->query('cidade'));

        if($this->request->query('bairro')) $this->where.= sprintf(" AND bairro='%s' ", $this->request->query('bairro'));

        if($this->request->query('nome')) $this->where.= sprintf(" AND c.nome LIKE '%s' ", '%'.$this->request->query('nome').'%');

        if($this->request->query('tag')) $this->where.= sprintf(" AND c.tags LIKE '%s' ", '%'.$this->request->query('tag').'%');

        if($this->request->query('nfe'))
        {
            $nfes= explode(' or ',strtolower($this->request->query('nfe')));
            $this->where.= " AND ( 1=3 ";
            foreach($nfes as $k => $v)
            {
                $this->where.= sprintf(" OR nfe LIKE '%s' ", $v.'%');
            }
            $this->where.= " ) ";
        }
        // echo $this->where;
        // die();
        if($this->request->query('cnpj')) $this->where.= sprintf(" AND s.cnpj = '%s' ", $this->request->query('cnpj'));

        // $select22='';
        if($this->request->query('data'))
        {
            $this->where.= sprintf(" AND s.data LIKE '%s' ", $this->request->query('data').'%');
        }

        if($this->request->query('cep')) $this->where.= sprintf(" AND c.cep LIKE '%s' ", $this->request->query('cep').'%');

        if($this->request->query('vd')) $this->where.= sprintf(" AND nick LIKE '%s' ", $this->request->query('vd').'%');

        if($this->request->query('seller')) $this->where.= sprintf(" AND s.seller LIKE '%s' ", $this->request->query('seller').'%');

    }

    private function sql()
    {

        if($this->sqlGroup)
        {

            $group = sprintf('GROUP BY s.%s',$this->sqlGroup);
        }else{
            $modelo= 'c.nome';

        }

        $sql = sprintf("SELECT   '' as numero,
                                s.id AS SSWID,
                                s.seller Company,
                                s.data,
                                nfe,
                                nick,
                                s.cnpj,
                                lcase(%s) as Modelo,
                                lcase(nome) as Cliente,
                                c.email,
                                peso peso,
                                volumes volumes,
                                IF(volumes>1, ROUND(peso/(volumes/2),1), 1) as pv,
                                IF( peso/(volumes/2)>50, Sum(round(volumes/2,0)),0) as sets,
                                SUM( IF( peso/(volumes/2)>50, round(volumes/2,0),0)   )  as Subtotal,
                                cep,lcase(ender) ender,nro,lcase(bairro) bairro,
                                TRIM(lcase(cidade)) cidade,
                                estado,
                                c.id as idcli,
                                c.tags as Tags,
                                COUNT(DISTINCT c.cnpj) as qClientes,

                                SUM( IF( (peso/(volumes/2))>50,round(volumes/2,0),0)) as qty_this_year,

                                ( SELECT  SUM(IF(hi.valor_base>0,hi.quant,0))
                                FROM mak.hoje h
                                LEFT JOIN mak.hist hi ON (hi.pedido=h.id)
                                LEFT JOIN mak.inv i ON (i.id=hi.isbn)
                                LEFT JOIN mak.produtos p ON (p.id=i.idcf)
                                WHERE YEAR(h.data) = YEAR(now()) AND h.nop IN (27,28,51,76) AND h.idcli=c.id AND p.segmento='machines'
                                ) as rolemak_this_year

                            FROM Analytics.ssw s
                            LEFT JOIN mak.clientes c on (s.cnpj=c.cnpj)
                            LEFT JOIN mak.users u ON (u.id=c.vendedor)

                            WHERE 1=1   %s
                            GROUP BY %s
                            %s
                            ORDER BY %s
                            LIMIT 500", $this->modelo,  $this->where,$this->sqlGroup,$this->sqlHaving,$this->sqlOrder  );

        $query = DB::query(Database::SELECT, $sql);

        $this->result = $query->execute()->as_array();

    // s( $this->result);

    }


    private function normalize()
    {


        foreach($this->result as $key => $val)
        {
            $nf = substr($val['nfe'],-5);

            $this->result[$key]['link']= sprintf('https://ssw.inf.br/app/tracking/%s/%s',$this->sellerCnpj,$nf);

            $rolemakOnly= [];

            if('cnpj' == $this->sqlGroup)
            {

                $this->result[$key]['monthly'] = self::getSswMonthly($val['cnpj']);

                $this->result[$key]['rolemak'] = self::getMonthlyRolemak($val['cnpj']);

            }

            if('estado' == $this->sqlGroup)
            {

                $this->result[$key]['monthly'] = self::getSswMonthly($val['estado']);

                $this->result[$key]['rolemak'] = self::getMonthlyRolemak($val['estado']);

            }

            if('cidade' == $this->sqlGroup)
            {

                $this->result[$key]['monthly'] = self::getSswMonthly($val['cidade']);

                $this->result[$key]['rolemak']  = self::getMonthlyRolemak($val['cidade']);

            }

            if('bairro' == $this->sqlGroup)
            {

                $this->result[$key]['monthly'] = self::getSswMonthly($val['bairro']);

                $this->result[$key]['rolemak']  = self::getMonthlyRolemak($val['bairro']);

            }

         }



        if( ( $this->sqlBairro or $this->sqlCidade) and 'cnpj' == $this->sqlGroup )
        {
            $rolemakOnly= self::getRolemakOnly($this->sqlBairro);

            $counter = count($this->result) ;

            if($counter > 0)
            {
                foreach($rolemakOnly as $key => $val)
                {
                    $this->result[$counter] = $val;
                    $this->result[$counter]['rolemak'] = self::getMonthlyRolemak($val['cnpj']);

                    $counter++;
                }
            }
        }

    }

    private function show()
    {
        // s($this->result);

        $view = parent::mustache($this->result,'ssw/'.$this->view);

        $this->response->body($view);
    }



    private function city( $year='2024')
    {

         $sql = sprintf("SELECT estado, LCASE(cidade) cidade,MONTH(h.data) as Mes, SUM(IF(hi.valor_base>1000,hi.quant,0))  as qty,  COUNT(DISTINCT c.cnpj) as qClientes
                            FROM mak.hoje h
                            LEFT JOIN mak.clientes c ON (c.id=h.idcli)
                            LEFT JOIN mak.hist hi ON (hi.pedido=h.id)
                            LEFT JOIN mak.inv i ON (i.id=hi.isbn)
                            LEFT JOIN mak.produtos p ON (p.id=i.idcf)
                            WHERE   YEAR(h.data) = '%s'
                                    AND h.nop IN (27,28,51,76)
                                    AND h.idcli=c.id
                                    AND p.segmento='machines'

                            GROUP BY estado,cidade,Mes
                            ORDER BY cidade,Mes ASC
                            LIMIT 10000
                             ",
                             $year);
    $query = DB::query(Database::SELECT, $sql);
    $result = $query->execute()->as_array();

    // return $result;
    $counter=0;
    foreach($result as $key => $val)
    {
        $res[$val['cidade']][$counter]['estado']=$val['estado'] ;
        $res[$val['cidade']][$counter]['cidade']=$val['cidade'] ;
        $res[$val['cidade']][$counter]['mes']=$val['Mes'] ;
        $res[$val['cidade']][$counter]['qty']=$val['qty'] ;
        $counter++;

    }


  return $res;

    s($res);

    die();

    }

    private function block ( $year='2024')
    {

        $sql = sprintf("SELECT estado, LCASE(cidade) cidade,lcase(bairro) bairro, MONTH(h.data) as Mes, SUM(IF(hi.valor_base>1000,hi.quant,0))  as qty,   COUNT(DISTINCT c.cnpj) as qClientes
                            FROM mak.hoje h
                            LEFT JOIN mak.clientes c ON (c.id=h.idcli)
                            LEFT JOIN mak.hist hi ON (hi.pedido=h.id)
                            LEFT JOIN mak.inv i ON (i.id=hi.isbn)
                            LEFT JOIN mak.produtos p ON (p.id=i.idcf)
                            WHERE   YEAR(h.data) = '%s'
                                    AND h.nop IN (27,28,51,76)
                                    AND h.idcli=c.id
                                    AND p.segmento='machines'

                            GROUP BY estado,cidade,bairro,Mes
                            ORDER BY cidade,Mes ASC
                            LIMIT 5000
                             ",
                             $year);
    $query = DB::query(Database::SELECT, $sql);
    $result = $query->execute()->as_array();

    // return $result;
    $counter=0;
    foreach($result as $key => $val)
    {
        $res[$val['bairro']][$counter]['estado']=$val['estado'] ;
        $res[$val['bairro']][$counter]['cidade']=$val['cidade'] ;
        $res[$val['bairro']][$counter]['bairro']=$val['bairro'] ;
        $res[$val['bairro']][$counter]['mes']=$val['Mes'] ;
        $res[$val['bairro']][$counter]['qty']=$val['qty'] ;
        $counter++;

    }


  return $res;

    s($res);

    die();

    }

    public function action_delete()
    {
        $id = $this->request->param('division');
        // $id = $this->request->param('xtras');
        // s($id);

         echo $sql = sprintf("DELETE FROM Analytics.ssw  WHERE id = %s


                         ", $id  );
             $query = DB::query(Database::DELETE, $sql);
             $result = $query->execute();

            // echo 'ok';


    }

    public function action_delete_by_cnpj()
    {
        $id = $this->request->param('division');
        // $id = $this->request->param('xtras');
        // s($id);

      echo  $sql = sprintf("DELETE FROM Analytics.ssw  WHERE cnpj = '%s'

                            ", $id  );
             $query = DB::query(Database::DELETE, $sql);
             $result = $query->execute();

            // echo 'ok';


    }

    public function action_html()
    {
        $this->segid = $this->request->param('division');
        $id = $this->request->param('xtras');
      //  s($id);

         $sql = sprintf("SELECT html  FROM Analytics.ssw s

                            WHERE id = %s

                            LIMIT 1", $id  );
             $query = DB::query(Database::SELECT, $sql);
             $result = $query->execute()->as_array();

            // echo $result[0]['html'];

            $view = parent::mustache($result,'ssw/ssw-html');

            $this->response->body($view);

    }

    public function action_rolemak()
    {
        $this->segid = $this->request->param('division');
        $this->brand = $this->request->param('xtras');


        $order=" s.id desc ";
        $having="";
        $where="";
        $group="data,nfe";

        if($this->request->query('rrh')) $where.= sprintf(" AND  r.rolemak_ranking_history >0 AND r.rolemak_ranking_history <= %s ", $this->request->query('rrh'));
        if($this->request->query('rrn')) $where.= sprintf(" AND  r.rolemak_ranking_now >0 AND r.rolemak_ranking_now <= %s ", $this->request->query('rrn'));
        if($this->request->query('gr')) $where.= sprintf(" AND  r.global_ranking >0 AND r.global_ranking <= %s ", $this->request->query('gr'));
        if($this->request->query('meso')) $where.= sprintf(" AND mun.meso LIKE '%s' ", $this->request->query('meso').'%');
        if($this->request->query('micro')) $where.= sprintf(" AND Micro LIKE '%s' ", $this->request->query('micro').'%');
        if($this->request->query('nomemicro')) $where.= sprintf(" AND NomeMicro LIKE '%s' ", $this->request->query('nomemicro').'%');
        if($this->request->query('regiao')) $where.= sprintf(" AND regiao='%s' ", $this->request->query('regiao'));
        if($this->request->query('order')) $order= sprintf(" %s ", $this->request->query('order'));
        if($this->request->query('estado')) $where.= sprintf(" AND estado='%s' ", $this->request->query('estado'));
        if($this->request->query('cidade')) $where.= sprintf(" AND cidade='%s' ", $this->request->query('cidade'));
        if($this->request->query('bairro')) $where.= sprintf(" AND bairro='%s' ", $this->request->query('bairro'));
        if($this->request->query('nome')) $where.= sprintf(" AND c.nome LIKE '%s' ", '%'.$this->request->query('nome').'%');
        if($this->request->query('nfe'))
        {
            $nfes= explode(' or ',strtolower($this->request->query('nfe')));
            $where.= " AND ( 1=3 ";
            foreach($nfes as $k => $v)
            {
                $where.= sprintf(" OR nfe LIKE '%s' ", $v.'%');
            }
            $where.= " ) ";
        }
        // echo $where;
        // die();
        if($this->request->query('cnpj')) $where.= sprintf(" AND s.cnpj = '%s' ", $this->request->query('cnpj'));


        // $select22='';
        if($this->request->query('data'))
        {
            $where.= sprintf(" AND s.data LIKE '%s' ", $this->request->query('data').'%');
        }

        if($this->request->query('cep')) $where.= sprintf(" AND c.cep LIKE '%s' ", $this->request->query('cep').'%');
        if($this->request->query('qty')) $having= sprintf(" Having qty %s ", $this->request->query('qty'));
        if($this->request->query('vd')) $where.= sprintf(" AND nick LIKE '%s' ", $this->request->query('vd').'%');
        if($this->request->query('seller')) $where.= sprintf(" AND s.seller LIKE '%s' ", $this->request->query('seller').'%');


          $sql = sprintf("SELECT lcase(c.nome) Cliente,c.cnpj,c.cgc,estado, LCASE(cidade) cidade,lcase(bairro) bairro,  SUM(IF(hi.valor_base>1000,hi.quant,0))  as qtyAno, nick
                            FROM mak.hoje h
                            LEFT JOIN mak.clientes c ON (c.id=h.idcli)
                            LEFT JOIN mak.hist hi ON (hi.pedido=h.id)
                            LEFT JOIN mak.inv i ON (i.id=hi.isbn)
                            LEFT JOIN mak.produtos p ON (p.id=i.idcf)
                            LEFT JOIN mak.users u ON (u.id=c.vendedor)
                            WHERE    h.nop IN (27,28,51,76)
                                    AND h.idcli=c.id
                                    AND p.segmento='machines'
                                    AND cnpj<>''
                                    AND YEAR(h.data)=  YEAR(NOW())
                                    AND h.id > 1000000
                                    %s
                            GROUP BY h.idcli
                            ORDER BY qtyAno DESC
                            LIMIT 0,100
                             ",
                             $where
                             );
    $query = DB::query(Database::SELECT, $sql);
    $result = $query->execute()->as_array();

    // return $result;
    $counter=$dif=0;
    foreach($result as $key => $val)
    {
    //     $res[$val['bairro']][$counter]['estado']=$val['estado'] ;
    //     $res[$val['bairro']][$counter]['cidade']=$val['cidade'] ;
    //     $res[$val['bairro']][$counter]['bairro']=$val['bairro'] ;
    //     $res[$val['bairro']][$counter]['mes']=$val['Mes'] ;
    //     $res[$val['bairro']][$counter]['qty']=$val['qty'] ;
    //     $counter++;
            $ssw= self::getSsw($val['cnpj']);
            $res[$key] = $val;
            $res[$key]['sswAno'] = $ssw;

            $res[$key]['Index'] ='';
            if($val['qtyAno']>0)   $res[$key]['Index'] = round($ssw/$val['qtyAno'],2);
            $res[$key]['Diff'] = $ssw-$val['qtyAno'];

            $dif+=$res[$key]['Diff'];
            $res[$key]['Acumulado'] = $dif;
            $counter++;
            $res[$key]['counter'] = $counter;


    }


    $view = parent::mustache($res,'ssw/rolemak');

    $this->response->body($view);

    //  d( $res);
//   return $res;

    }

    private function getSswMonthly($var)
    {
        $var = self::accentCharsModifier($var);

        $where='';

        $select='';

        if('cnpj' == $this->sqlGroup)
        {
            $where.= sprintf(" AND s.cnpj='%s' ",$var );

            $select.= "s.cnpj";

        }

        if('estado' == $this->sqlGroup)
        {
            $where.= sprintf(" AND c.estado='%s' ",$var );

            $select.= "c.estado";
        }

        if('cidade' == $this->sqlGroup)
        {
            $where.= sprintf(" AND c.cidade='%s' ",str_replace("'","\'", $var) );

            $select.= "c.cidade";
        }

        if('bairro' == $this->sqlGroup)
        {
            $where.= sprintf(" AND c.bairro='%s' ",str_replace("'","\'", $var) );

            $select.= "c.bairro";
        }

        $sql = sprintf("SELECT
                                trim(lcase(%s)) AS Modelo,
                                EXTRACT(YEAR FROM s.data)  AS Ano,
								EXTRACT(MONTH FROM s.data)  AS Mes,
								EXTRACT(YEAR_MONTH FROM s.data)  AS DT,
                                SUM(IF( (s.peso/s.volumes*2)>50,round(s.volumes/2,0),0)) as QT


                            FROM Analytics.ssw s
                            LEFT JOIN mak.clientes c on (s.cnpj=c.cnpj)
                            WHERE 1=1 and s.seller='%s' %s
                            GROUP BY DT
                            ORDER BY s.data DESC

                            LIMIT 10000
                             ",
                             $select,
                             $this->sqlSeller,
                             $where

                             );

                            //  die();
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();



        if(count($result)==0)
        {
            return [];
            echo $sql;
            die('Sem registros!');
        }

        foreach($result as $key => $val)
        {
            $idx = self::accentCharsModifier($val["Modelo"]);
            //s($idx);

            if(!isset($vl[$val["Modelo"]])) $vl[$val["Modelo"]]=0;


            $yar[$idx][$val["Ano"]][$val["Mes"]]= $val["QT"];

        }

        foreach($yar as $key => $val)
        {
            for($y=date('Y'); $y>2021; $y--)
            {
                $start=1;
                $finish=12;

                if( $y==date('Y') ) $finish=(int) date('m');

                for($x=$start; $x<=$finish; $x++)
                {
                    if(!isset($yar[$key][$y][$x]))
                    {

                        $new[$key][$y.'-'.$x] = '0';

                    }else{
                        $new[$key][$y.'-'.$x] = $yar[$key][$y][$x];
                    }

                }

            }
        }

        $counter = 1;

        $counterYear = 0;

        $thisYear = date('Y');

        $qtyYear = 0;

        //  s($var, $new);

        foreach($new[$var] as $key => $val)
        {
            $currentYear = substr($key,0,4);

            if( $currentYear <> $thisYear )
            {

               $counterYear++;

               $resp[$counterYear]['year'][0]['date'] = $currentYear;

               $resp[$counterYear]['year'][0]['value'] = 0;

               $resp[$counterYear]['year'][0]['bg'] = true;

               $qtyYear = 0 ;

               $counter = 1 ;

               $thisYear = $currentYear;

            }else{

                $resp[$counterYear]['year'][0]['date'] = $currentYear;

                $resp[$counterYear]['year'][0]['value'] = 0;

                $resp[$counterYear]['year'][0]['bg'] = true;

            }

            $qtyYear+= $val;

            $resp[$counterYear]['year'][$counter]['date'] = $key;

            if($val==0) $val='-';
            $resp[$counterYear]['year'][$counter]['value'] = $val;

            $resp[$counterYear]['year'][0]['value'] = $qtyYear;

            $counter++;

        }

    //   s($resp);

     if(empty($resp)) return [];

     return $resp;

    }

    private function getMonthlyRolemak($var)
    {
        $var = strtolower(self::accentCharsModifier($var));

        $where='';

        $select='';

        if('cnpj' == $this->sqlGroup)
        {
            $where.= sprintf(" AND cnpj='%s' ",$var );

            $select.= "cnpj";

        }

        if('estado' == $this->sqlGroup)
        {
            $where.= sprintf(" AND uf='%s' ",$var );

            $select.= "uf";
        }

       if('cidade' == $this->sqlGroup)
        {
            $where.= sprintf(" AND nomeMunic='%s' ",str_replace("'","\'", $var) );

            $select.= "nomeMunic";
        }

       if('bairro' == $this->sqlGroup)
        {
            $where.= sprintf(" AND bairro='%s' ",str_replace("'","\'", $var) );

            $select.= "bairro";
        }

         $sql = sprintf("SELECT
                                nome,
                                lcase(%s) AS Modelo,
                                EXTRACT(YEAR FROM data)  AS Ano,
								                EXTRACT(MONTH FROM data)  AS Mes,
								                EXTRACT(YEAR_MONTH FROM data)  AS DT,
                                SUM(IF(valor > 1300, quant, 0 )) as QT


                            FROM  mak.`vPedidos`
                            WHERE Year(data)>2020 and segmento='machines' %s
                            GROUP BY DT
                            ORDER BY data DESC

                            LIMIT 1000
                             ",
                             $select,
                             $where
                             );
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

        //   s($result);
        //  die();
        if(count($result)==0)
        {
            return [];
            echo $sql;
            die('Sem registros!');
        }

        foreach($result as $key => $val)
        {
            if(!isset($vl[$val["Modelo"]])) $vl[$val["Modelo"]]=0;
            $yar[$val["Modelo"]][$val["Ano"]][$val["Mes"]]= $val["QT"];
            // $vl[$val["Modelo"]]+= $val["VL"];
        }

    // s($yar);
        foreach($yar as $key => $val)
        {
            for($y=date('Y'); $y>2020; $y--)
            {
                $start=1;
                $finish=12;

                if( $y==date('Y') ) $finish=(int) date('m');

                for($x=$start; $x<=$finish; $x++)
                {
                    $idx = self::accentCharsModifier($key);
                    // s($idx);

                    if(!isset($yar[$key][$y][$x]))
                    {

                        $new[$idx][$y.'-'.$x] = '0';

                    }else{
                        $new[$idx][$y.'-'.$x] = $yar[$key][$y][$x];
                    }

                }

            }
        }


        $counter = 1;

        $counterYear = 0;

        $thisYear = date('Y');

        $qtyYear = 0;

        // s($var,$yar,$new);

        foreach($new[$var] as $key => $val)
        {
            $currentYear = substr($key,0,4);

            if( $currentYear <> $thisYear )
            {

               $counterYear++;

               $resp[$counterYear]['year'][0]['date'] = $currentYear;

               $resp[$counterYear]['year'][0]['value'] = 0;

               $resp[$counterYear]['year'][0]['bg'] = true;

               $qtyYear = 0 ;

               $counter = 1 ;

               $thisYear = $currentYear;

            }else{

                $resp[$counterYear]['year'][0]['date'] = $currentYear;

                $resp[$counterYear]['year'][0]['value'] = 0;

                $resp[$counterYear]['year'][0]['bg'] = true;

            }

            $qtyYear+= $val;

            $resp[$counterYear]['year'][$counter]['date'] = $key;

             if($val==0) $val='-';
            $resp[$counterYear]['year'][$counter]['value'] = $val;

            $resp[$counterYear]['year'][0]['value'] = $qtyYear;

            $counter++;

        }

    //   s($resp);

    //   die();

     if(empty($resp)) return [];

     return $resp;

    }

    private function getRolemakOnly()
    {
        $where = $whereSub ='';
        if($this->sqlBairro) $where.= sprintf(" AND Bairro='%s' ", $this->sqlBairro);
        if($this->sqlCidade) $where.= sprintf(" AND NomeMunic='%s' ", $this->sqlCidade);
        if($this->sqlEstado) $where.= sprintf(" AND UF='%s' ", $this->sqlEstado);
        if($this->sqlSeller) $whereSub.= sprintf(" AND seller='%s' ", $this->sqlSeller);

         $sql = sprintf("SELECT  p.idcli,
                                p.nome Cliente,
                                p.Bairro,
                                p.NomeMunic cidade,
                                uf estado,
                                p.cnpj,
                                SUM(IF(p.valor > 1000, p.quant, 0 )) as QT


                            FROM  mak.`vPedidos` p
                            WHERE YEAR(p.data) >2021  and segmento='machines' %s
                                    AND p.cnpj NOT IN (
                                        SELECT s.cnpj
                                        FROM Analytics.ssw s
                                        LEFT JOIN mak.clientes c on (s.cnpj=c.cnpj)
                                        WHERE 1=1 %s
                                        )
                            GROUP BY p.cnpj
                            ORDER BY QT DESC
                            LIMIT 10
                             ",
                             $where,

                             $whereSub
                             );
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

        return $result;


        s($result);
        die();

    }


    private function accentCharsModifier($str)
    {
        if(($length=mb_strlen($str,"UTF-8"))<strlen($str)){
            $i=$count=0;
            while($i<$length){
                if(strlen($c=mb_substr($str,$i,1,"UTF-8"))>1){
                    $he=htmlentities($c);
                    if(($nC=preg_replace("#&([A-Za-z])(?:acute|cedil|caron|circ|grave|orn|ring|slash|th|tilde|uml);#", "\\1", $he))!=$he ||
                        ($nC=preg_replace("#&([A-Za-z]{2})(?:lig);#", "\\1", $he))!=$he ||
                        ($nC=preg_replace("#&[^;]+;#", "", $he))!=$he){
                        $str=str_replace($c,$nC,$str,$count);if($nC==""){$length=$length-$count;$i--;}
                    }
                }
                $i++;
            }
        }
        return strtolower(trim($str));
}


    public function action_daily()
    {



        if('rolemak' == $this->sqlSeller)
        {
            self::daily_rolemak() ;
        }else{
            self::daily_ssw() ;
        }
    }

    private function daily_ssw()
    {
        $where='';

        $select='';

        $title=' XRAY ';

        if('cnpj' == $this->sqlGroup)
        {
              $var = $this->cnpj ?? '07702018000184';
               $var = self::accentCharsModifier($var);

              // $where.= sprintf(" AND s.cnpj='%s' ",$var );

            $where.= sprintf(" AND ( s.cnpj='%s' OR s.cnpj in (SELECT cnpj FROM Analytics.`ssw_group` WHERE name = (SELECT name FROM Analytics.`ssw_group` WHERE cnpj='%s')))",$var,$var );

            // SELECT cnpj FROM `ssw_group` WHERE name = (SELECT name FROM `ssw_group` WHERE cnpj='09538079000183')

            $select.= "s.cnpj";

            $title=sprintf(" %s por cnpj : %s",$this->sqlSeller, $this->cnpj);

        }

        if('estado' == $this->sqlGroup)
        {
            $where.= sprintf(" AND estado='%s' ",$this->sqlEstado );

            $select.= "estado";

            $title=sprintf(" %s por %s : %s",$this->sqlSeller,$select, $this->sqlEstado);
        }

        if('cidade' == $this->sqlGroup)
        {
            $where.= sprintf(" AND cidade='%s' ",str_replace("'","\'", $this->sqlCidade) );

            $select.= "cidade";

            $title=sprintf(" %s por %s : %s",$this->sqlSeller,$select, $this->sqlCidade);
        }

        if('bairro' == $this->sqlGroup)
        {
            $where.= sprintf(" AND bairro='%s' ",str_replace("'","\'", $this->sqlBairro) );

            $select.= "bairro";

           $title=sprintf(" %s por %s : %s",$this->sqlSeller,$select, $this->sqlBairro);
        }

        $sql = sprintf("SELECT  '%s' as seller,
                                '%s' as grupo,
                                '%s' as title,
                                idcli,
                                nome as nome,
                                ender,
                                bairro,
                                cidade,
                                estado,
                                trim(lcase(%s)) AS Modelo,
                                EXTRACT(YEAR FROM s.data)  AS Ano,
								EXTRACT(MONTH FROM s.data)  AS Mes,
								EXTRACT(DAY FROM s.data)  AS Dia,
								EXTRACT(YEAR_MONTH FROM s.data)  AS DT,
								s.data  AS DATA,

                                SUM(IF( (s.peso/s.volumes*2)>50,round(s.volumes/2,0),0)) as QT


                            FROM Analytics.ssw_view s
                            WHERE 1=1 and s.seller='%s' %s
                            GROUP BY s.data
                            ORDER BY Ano Desc, Mes Desc, Dia Asc

                            LIMIT 4000
                             ",
                             $this->sqlSeller,
                             $this->sqlGroup,
                             $title,
                             $select,
                             $this->sqlSeller,
                             $where

                             );

                            //  die();
        $query = DB::query(Database::SELECT, $sql);

        $result = $query->execute()->as_array();

        $daily = new Daily($this);

        $daysArray = $daily->process($result);

        $daysArray['cnpjs'] = self::getSswCnpjRank();

        $view = parent::mustache($daysArray,'ssw/daily');

        $this->response->body($view);

    }

    private function daily_rolemak()
    {

        $where='';

        $select='';

        $title=' XRAY ';

        if('cnpj' == $this->sqlGroup)
        {
            $var = $this->cnpj ?? '07702018000184';

            $var = self::accentCharsModifier($var);

            $where.= sprintf(" AND ( s.cnpj='%s' OR s.cnpj in (SELECT cnpj FROM Analytics.`ssw_group` WHERE name = (SELECT name FROM Analytics.`ssw_group` WHERE cnpj='%s')))",$var,$var );

            $select.= "s.cnpj";

            $title=' Green por cnpj : '. $var;

        }

        if('estado' == $this->sqlGroup)
        {
            $where.= sprintf(" AND uf='%s' ",$this->sqlEstado );

            $select.= "uf";

            $title=' Green por estado : '.$this->sqlEstado;
        }

        if('cidade' == $this->sqlGroup)
        {
            $where.= sprintf(" AND nomeMunic='%s' ",str_replace("'","\'", $this->sqlCidade) );

            $select.= "nomeMunic";

            $title=' Green por cidade : '.$this->sqlCidade;
        }

        if('bairro' == $this->sqlGroup)
        {
            $where.= sprintf(" AND bairro='%s' ",str_replace("'","\'", $this->sqlBairro) );

            $select.= "bairro";

            $title=' Green por bairro : '.$this->sqlBairro;
        }

        $sql = sprintf("SELECT  '%s' as seller,
                                '%s' as grupo,
                                '%s' as title,
                                idcli id,
                                nome as nome,
                                '' ender,
                                bairro bairro,
                                nomemunic cidade,
                                uf estado,
                                trim(lcase(%s)) AS Modelo,
                                EXTRACT(YEAR FROM s.data)  AS Ano,
								EXTRACT(MONTH FROM s.data)  AS Mes,
								EXTRACT(DAY FROM s.data)  AS Dia,
								EXTRACT(YEAR_MONTH FROM s.data)  AS DT,
								s.data  AS DATA,
                                SUM(IF(valor > 1300, quant, 0 )) as QT

                            FROM  mak.`vPedidos` s
                            WHERE Year(data)>2020 and segmento='machines' %s
                            GROUP BY  Ano , Mes, Dia
                            ORDER BY Ano Desc, Mes Desc, Dia Asc

                            LIMIT 1000
                             ",
                              $this->sqlSeller,
                              $this->sqlGroup,
                             $title,
                             $select,
                             $where
                             );

        $query = DB::query(Database::SELECT, $sql);

        $result = $query->execute()->as_array();


        $daily = new Daily($this);

        $daysArray = $daily->process($result);

        $daysArray['cnpjs'] = self::getSswCnpjRank();

        $view = parent::mustache($daysArray,'ssw/daily');

        $this->response->body($view);

        // s($daysArray);


    }

    private function getSswCnpjRank()
    {
        $sql = sprintf("SELECT lcase(nome) nome, cnpj

                            FROM Analytics.`ssw_rank`

                            WHERE estado <> ''

                            LIMIT 50
                             "
                             );

        $query = DB::query(Database::SELECT, $sql);

        $result = $query->execute()->as_array();

        return $result;

    }
}