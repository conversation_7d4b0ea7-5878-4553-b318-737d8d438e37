<?php

class Controller_Stock extends Controller_Website
{


   public function before()
    {
         parent::before();
    }


    public function action_index()
    {

        $this->idx=1.30;

        // s($this->request->query());
        if($this->request->query('dx'))  $dx=$this->request->query('dx');
        // s($dx);
        // die();
        $view=$response='';
        foreach($dx as $key => $segid)
        {
           $view.=self::run($segid);
        }

         $response =  View::factory('pagefy/ajax')
		 				->set('content', $view )

										  ;
										  //parent::pagefy($view,$theme);
        // s($_SERVER);


        // return $response;

        $this->response->body($response);
    }

      public function action_rpa()
    {

        $this->idx=1.30;

        // s($this->request->query());
        if($this->request->query('dx'))  $dx=$this->request->query('dx');
        // s($dx);
        // die();
        // $view=$response='';
        // foreach($dx as $key => $segid)
        // {
           $view=self::run(0);
        // }

        $response =  View::factory('pagefy/tailwind-light')
		 				->set('content', $view )

										  ;
										  //parent::pagefy($view,$theme);
        // s($_SERVER);


        // return $response;

        $this->response->body($response);
    }
    private function run($segid)
    {
        $this->segment = $this->segments[$segid]['Name'];
        $theme='boot';
        $view = '';

        $raw = self::getStockAmount($segid);
        $data = self::classify($raw,$segid);


        /// Summarize
        // $sum['Estoque'];
        $data['sum'][] = 'Estoque';
        $data['sum'][] = 'Subtotal';
        $data['sum'][] = 'BarraFunda';
        $data['sum'][] = 'BluMenau';
        $data['sum'][] = 'BluMenau2';
        $data['sum'][] = 'TatuApé';
        $data['sum'][] = 'PesoTotal';

        //// Format Data
        $fmt['Revenda'] = $fmt['Diff'] = $fmt['ValorMedioAno'] = $fmt['P20'] = $fmt['P15'] = $fmt['F9'] = $fmt['F9_5'] = $fmt['F10'] = $fmt['F11'] =  '2us';
        $fmt['Fob'] = $fmt['LastFob'] = '3us';

        if($segid==1)
            $fmt['Fob'] = $fmt['LastFob'] =  $fmt['Revenda'] = $fmt['ValorMedioAno'] = '0br';

        $fmt['Subtotal'] = $fmt['PesoTotal']  = '0kb';
        $fmt['AGE'] = '0us';
        $data['format'] = $fmt;

        // field color
        $color['UltimaVendaDias'][0]['class'] = ' text-blue-700 text-xl font-bold ';
        $color['UltimaVendaDias'][0]['rule']  = ' < 31 ';
        $color['UltimaVendaDias'][1]['class'] = ' text-red-700 text-2xl font-bold ';
        $color['UltimaVendaDias'][1]['rule']  = ' > 30 ';
        $color['UltimaVendaDias'][2]['class'] = ' text-red-700 text-1xl font-bold ';
        $color['UltimaVendaDias'][2]['rule']  = ' UltimaVendaDias > IdadeDias ';

        // $color['IdadeDias']['class'] = ' text-red-500 text-1xl  font-semibold';
        // $color['IdadeDias']['rule']  = ' > 90 ';


        $color['IdadeDias'][0]['class'] = ' text-blue-200 text-xl  font-bold';
        $color['IdadeDias'][0]['rule']  = ' > 90 ';
        $color['IdadeDias'][1]['class'] = ' text-blue-300 text-2xl  font-semibold';
        $color['IdadeDias'][1]['rule']  = ' > 180';
        $color['IdadeDias'][2]['class'] = ' text-blue-600 text-2xl  font-bold';
        $color['IdadeDias'][2]['rule']  = ' > 365 ';


        $color['Subtotal'][0]['class'] = ' text-indigo-500 text-xl  font-bold';
        $color['Subtotal'][0]['rule']  = ' > 10000 ';
        $color['Subtotal'][1]['class'] = ' text-indigo-600 text-2xl  font-bold';
        $color['Subtotal'][1]['rule']  = ' > 100000 ';
        $color['Subtotal'][2]['class'] = ' text-indigo-700 text-3xl  font-bold';
        $color['Subtotal'][2]['rule']  = ' > 1000000 ';

        $color['V30']['class'] = ' text-red-700 text-xl  font-bold';
        $color['V30']['rule']  = ' == 0 ';
        $color['V60']['class'] = ' text-red-700 text-2xl  font-bold';
        $color['V60']['rule']  = ' == 0 ';
        $color['V90']['class'] = ' text-red-700 text-3xl  font-bold';
        $color['V90']['rule']  = ' == 0 ';

        // $color['VidaEstoqueMeses']['class'] = ' text-red-500 text-3xl  font-bold';
        // $color['VidaEstoqueMeses']['rule']  = ' > 6 ';

        $color['VidaEstoqueMeses'][0]['class'] = ' text-red-400 text-xl  font-bold';
        $color['VidaEstoqueMeses'][0]['rule']  = ' > 6 ';
        $color['VidaEstoqueMeses'][1]['class'] = ' text-red-600 text-2xl  font-semibold';
        $color['VidaEstoqueMeses'][1]['rule']  = ' > 12';
        $color['VidaEstoqueMeses'][2]['class'] = ' text-red-700 text-3xl  font-bold';
        $color['VidaEstoqueMeses'][2]['rule']  = ' > 24 ';



        $color['VidaEstoque+EmTransitoMeses']['class'] = ' text-red-300 text-2xl  font-bold';
        $color['VidaEstoque+EmTransitoMeses']['rule']  = ' > 6 ';
        $color['EmTransito']['class'] = ' text-blue-700 text-xl font-semibold ';
        $color['EmTransito']['rule']  = ' > 0 ';
          // field color
        $color['Estoque'][0]['class'] = ' text-red-500 text-xl font-bold ';
        $color['Estoque'][0]['rule']  = ' > 0 ';
        // $color['Estoque'][1]['class'] = ' text-red-700 text-xl font-bold ';
        // $color['Estoque'][1]['rule']  = ' Estoque < StkMin ';
        // $color['Estoque'][2]['class'] = ' text-purple-700 text-xl font-bold ';
        // $color['Estoque'][2]['rule']  = ' Estoque > StkMax ';

        $color['Revenda']['class'] = 'text-red-600 text-xl font-semibold ';
        $color['Revenda']['rule']  = ' Sugerido > Revenda ';

        $data['color'] = $color;

        // Aplicar cor de fundo verde para campos que começam com 'v' ou 'V' DEPOIS de definir $data['color']
        $data = $this->applyGreenBackgroundToVFields($data);


        //// Editable
        // inv.estoque_transito AS EstoqueDeTrânsito
        $edt['EstoqueMínimo'] = 'mak.inv|estoque_min|id|_ISBN';
        $edt['EstoqueMáximo'] = 'mak.inv|estoque_max|id|_ISBN';
        $edt['EstoqueDeProteção'] = 'mak.inv|estoque_protecao|id|_ISBN';

        $edt['vip'] = 'mak.inv|vip|id|_ISBN';
        $edt['EstoqueDeTrânsito'] = 'mak.inv|estoque_transito|id|_ISBN';
        $edt['HabilitadoCostura'] = 'mak.inv|habilitado|id|_ISBN';
        $edt['Juros'] = 'mak.inv|juros|id|_ISBN';
        $edt['Vezes'] = 'mak.inv|vezes|id|_ISBN';
        $edt['fob'] = 'mak.inv|fob|id|_ISBN';
        $edt['Revenda'] = 'mak.inv|revenda|id|_ISBN';
        $edt['Revenda2'] = 'mak.inv|revenda2|id|_ISBN';
        $edt['CostIndex'] = 'mak.inv|costindex|id|_ISBN';
        $edt['Peso'] = 'mak.inv|peso|id|_ISBN';
         $edt['PesoSet'] = 'mak.inv|pesoset|id|_ISBN';
        $edt['Volume'] = 'mak.inv|volume|id|_ISBN';
        $edt['VolumeSet'] = 'mak.inv|volumeset|id|_ISBN';
        $edt['Nome'] = 'mak.inv|nome|id|_ISBN';
         $edt['Obs'] = 'mak.inv|memoh|id|_ISBN';
        $data['editable'] = $edt;

        // s( $this->pagination);
        $data['pagination'] = $this->pagination;
        $data['theme'] = 1;

        $baseurl='/metrics/stocks/index';
        if($segid==1)
        {


            $theme='light';
            $data['theme'] = 2;
        }

        if($segid==2)
        {
            // $data['caption'][] = array('link'=>$baseurl.'/2/?brand=ppk', 'title' => 'PPK') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?brand=mak', 'title' => 'MAK') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?brand=dtb', 'title' => 'DTB') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?brand=mak automotive', 'title' => 'MAK AUTOMOTIVE') ;
            // // $data['caption'][] = array('link'=>$baseurl.'/2/?line=600', 'title' => '6000') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=620', 'title' => '6200') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=630', 'title' => '6300') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=302', 'title' => '30200') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=322', 'title' => '32200') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=UC', 'title' => 'UC') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=UCP', 'title' => 'UCP') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=222', 'title' => '222XX') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=12', 'title' => '12XX') ;

            // $data['caption'][] = array('link'=>$baseurl.'/2/?name=', 'title' => 'Agulhas') ;

            $data['theme'] = 2;
            $theme='light';

        }

        if($segid==3)
        {
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=zoje', 'title' => '<img class=" h-6   " src="https://cdn.rolemak.com.br/svg/marca/zoje.svg?version=7.73">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=strong h', 'title' => '<img class="h-8 " src="https://cdn.rolemak.com.br/svg/marca/strong-h.svg?version=7.73">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=Desheng', 'title' => '<img class="h-8 " src="https://cdn.rolemak.com.br/svg/marca/desheng.svg?version=7.73">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=jec', 'title' => '<img class=" h-6   " src="https://cdn.rolemak.com.br/svg/marca/jec.svg?version=7.73">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=Jinzen', 'title' => '<img class="h-6 " src="https://cdn.rolemak.com.br/svg/marca/jinzen.svg?version=7.73">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=pufei', 'title' => '<img class=" h-6   " src="https://cdn.rolemak.com.br/svg/marca/pufei.svg?version=7.73">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=Baludan', 'title' => '<img class=" h-8   " src="https://cdn.rolemak.com.br/svg/marca/baludan.svg?version=7.73">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=Dahao', 'title' => '<img class="h-8 " src="https://cdn.rolemak.com.br/svg/marca/dahao.svg?version=7.73">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=Qixing', 'title' => '<img class="h-8" src="https://cdn.rolemak.com.br/svg/marca/qixing.svg?version=7.73">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=HMC', 'title' => 'hmc') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=Golden Hook', 'title' => 'Golden Hook') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=messer', 'title' => '<img class=" h-8   " src="https://cdn.rolemak.com.br/svg/marca/messer.svg?version=7.73">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=kamel', 'title' => '<img class="h-6 " src="https://cdn.rolemak.com.br/svg/marca/kamel.svg?version=7.73">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=cerliani', 'title' => '<img class=" h-8   " src="https://cdn.rolemak.com.br/svg/marca/cerliani.svg?version=7.73">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?brand=hirose', 'title' => '<img class=" h-8   " src="https://cdn.rolemak.com.br/svg/marca/hirose.svg?version=7.73">') ;

            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Motor', 'title' => 'Motores<img src="https://cdn.rolemak.com.br/svg/icones/motor-industrial.svg?version=7.73" width="60" alt="Motor Industrial" title="Motor Industrial"><br>') ;


            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Agulha', 'title' => 'Agulhas<img src="https://cdn.rolemak.com.br/svg/icones/agulhas.svg?version=7.73" width="60" alt="Agulhas" title="Agulhas">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Alavanca', 'title' => 'Alavancas<img src="https://cdn.rolemak.com.br/svg/icones/alavancas.svg?version=7.73" width="60" alt="Alavancas" title="Alavancas">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Aparelho', 'title' => 'Aparelhos<img src="https://cdn.rolemak.com.br/svg/icones/aparelhos-para-costura.svg?version=7.73" width="60" alt="Aparelhos" title="Aparelhos">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=barra', 'title' => 'Barras<img src="https://cdn.rolemak.com.br/svg/icones/barras.svg?version=7.73" width="60" alt="Barras" title="Barras">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Biela', 'title' => 'Bielas<img src="https://cdn.rolemak.com.br/svg/icones/bielas.svg?version=7.73" width="60" alt="Bielas" title="Bielas">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Bitola', 'title' => 'Bitolas<img src="https://cdn.rolemak.com.br/svg/icones/prendedor-de-agulha.svg?version=7.73" width="60" alt="Prendedor de Agulha" title="Prendedor de Agulha">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Bloco', 'title' => 'Blocos<img src="https://cdn.rolemak.com.br/svg/icones/blocos.svg?version=7.73" width="60" alt="Blocos" title="Blocos">') ;

            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Borracha', 'title' => 'Borrachas<img src="https://cdn.rolemak.com.br/svg/icones/borrachas.svg?version=7.73" width="60" alt="Borrachas" title="Borrachas">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Bucha', 'title' => 'Buchas<img src="https://cdn.rolemak.com.br/svg/icones/buchas.svg?version=7.73" width="60" alt="Buchas" title="Buchas">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Caixa de Bobi', 'title' => 'Caixas de Bobinas<img src="https://cdn.rolemak.com.br/svg/icones/caixa-de-bobina.svg?version=7.73" width="60" alt="Caixa de Bobina" title="Caixa de Bobina">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Carretilha', 'title' => 'Carretilhas<img src="https://cdn.rolemak.com.br/svg/icones/carretilhas.svg?version=7.73" width="60" alt="Carretilhas" title="Carretilhas">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=calcado', 'title' => 'Calcadores<img src="https://cdn.rolemak.com.br/svg/icones/calcadores.svg?version=7.73" width="60" alt="Calcadores" title="Calcadores">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=chapa', 'title' => 'Chapas<img src="https://cdn.rolemak.com.br/svg/icones/chapas-agulha.svg?version=7.73" width="60" alt="Chapas" title="Chapas">') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Correia', 'title' => 'Correias') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=dente', 'title' => 'Dentes') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=lanc', 'title' => 'Lançadeiras') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=looper', 'title' => 'Loopers') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=maq', 'title' => 'Máquinas') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Parafuso', 'title' => 'Parafusos') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Pino', 'title' => 'Pinos') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Porca', 'title' => 'Porcas') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=Suporte', 'title' => 'Suportes') ;
            $data['caption'][] = array('link'=>$baseurl.'/3/?name=tesoura', 'title' => 'Tesoura') ;

            $data['theme'] = 2;
            $theme='light';
        }

        if($segid==5)
        {
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mtr-cv', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/junta-homocinetica.svg?version=7.73" width="35" alt="Junta Homocinética" title="Junta Homocinética">Juntas') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=msu-bj', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/terminal-de-direcao.svg?version=7.73" width="35" alt="Terminal de Direção" title="Terminal de Direção">Pivôs') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=msu-bj-f2', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/terminal-de-direcao.svg?version=7.73" width="35" alt="Terminal de Direção" title="Terminal de Direção">Pivôs Ford') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-wh', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/cubo-de-roda.svg?version=7.73" width="35" alt="Cubo de Roda" title="Cubo de Roda">Cubos') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=msu-ca', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/bandeja.svg?version=7.73" width="35" alt="Bandeja" title="Bandeja">Bandejas') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=men-tk', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/kit-corrente-de-comando.svg?version=7.73" width="35" alt="Kit Corrente de Comando" title="Kit Corrente de Comando">Kit Corrente') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mpp', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/bomba-d-agua.svg?version=7.73" width="35" alt="Bomba d Agua" title="Bomba d Agua">Bombas') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=msr-ax', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/barra-axial.svg?version=7.73" width="35" alt="Barra Axial" title="Barra Axial">Barras') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mac-wb', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/palheta.svg?version=7.73" width="35" alt="Palheta" title="Palheta">Palhetas') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mcl-rd', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/radiador.svg?version=7.73" width="35" alt="Radiador" title="Radiador">Radiadores') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mtr-ds', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/semieixo.svg?version=7.73" width="35" alt="Semieixo" title="Semieixo">Semi-Eixos') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-te', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/tensor-e-polia.svg?version=7.73" width="35" alt="Tensor e Polia" title="Tensor e Polia">Tensores') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mtr-tp', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/trizeta.svg?version=7.73" width="35" alt="Trizeta" title="Trizeta">Trizetas') ;

            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-kt', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/rolamentos.svg?version=7.73" width="35" alt="Rolamentos" title="Rolamentos">Kits de Rolamentos') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-wb', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/rolamentos.svg?version=7.73" width="35" alt="Rolamentos" title="Rolamentos">Duplo de Rodas') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-sb', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/rolamentos.svg?version=7.73" width="35" alt="Rolamentos" title="Rolamentos">Esferas') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-tr', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/rolamentos.svg?version=7.73" width="35" alt="Rolamentos" title="Rolamentos">Capa&Cones') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-ac', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/rolamentos.svg?version=7.73" width="35" alt="Rolamentos" title="Rolamentos">Ar Condicionado') ;
            $data['caption'][] = array('link'=>$baseurl.'/5/?line=mbr-al', 'title' => '<img src="https://cdn.rolemak.com.br/svg/icones/rolamentos.svg?version=7.73" width="35" alt="Rolamentos" title="Rolamentos">Alternadores') ;

            $data['theme'] = 2;
            $theme='light';
        }


        return $view.= parent::tablefy( $data );

        // s($data);

    //     $response =  View::factory('pagefy/ajax')
		 	// 			->set('content', $view )

				// 						  ;
				// 						  //parent::pagefy($view,$theme);
    //     // s($_SERVER);


    //     return $response;

    }


    function classify($result,$segid)
    {


        foreach ($result as $key => $row)
        {

            $rv[$row['_ISBN']]= $row;
            // $rv[$row['_ISBN']]['_ISBN'] = $row['_ISBN'];
            // $rv[$row['_ISBN']]['FOB'] = $row['Fob'];
            // $rv[$row['_ISBN']]['Estoque'] = $row['Estoque'];
            // $rv[$row['_ISBN']]['Subtotal'] = $row['Estoque']*$row['Fob']*_DOLAR*$row['CostIndex'];
        //      inv.id as _id,
						  //  inv.id as _ISBN,

						  //  inv.modelo as Modelo,
						  //  inv.marca as Marca,
						  //  inv.nome as Nome,
						  //  inv.fob,
						  //  inv.CostIndex,
						  //  inv.revenda,

            $fob = $row['Fob'];
            // $idx = $row['CostIndex'];
            $dolar = _DOLAR;
            // $costa =  round($fob*$idx*$dolar,0);
            // $costb = $costa/.85;
            // $TaxEven = round($costb,0);


            // $rv[$row['_ISBN']]['Marca'] = $row['marca'];
            // $rv[$row['_ISBN']]['Modelo'] = $row['modelo'];
            // $rv[$row['_ISBN']]['Nome'] = $row['nome'];
            // $rv[$row['_ISBN']]['Revenda'] = $row['revenda'];
            // $rv[$row['_ISBN']]['BlackFriday10%DescontoAvista'] = $row['revenda']*.90;
            // $rv[$row['_ISBN']]['BlackFriday5%Em-5x'] = $row['revenda']*.95;
            // $rv[$row['_ISBN']]['Diff'] = 0;
            $dec = 2;
            $x=20;
            // if($segid==1)
            // {
            //     $dec = 0;
            //     $x=20;
            // }
            // for ($i=$x; $i>=10;$i--)
            // {
            //     $rv[$row['_ISBN']]['M'.$i] = round($costb/((100-$i)/100),$dec);
            // }

            // if ($rv[$row['_ISBN']]['M17']>0) $rv[$row['_ISBN']]['Diff'] = round($row['Revenda']/$rv[$row['_ISBN']]['M17'],2);

            // $rv[$row['_ISBN']]['Clientes'] = $row['idclis'];

            // $rv[$row['_ISBN']]['Q2022'] =  ($row['Q22']<1) ? '-' : $row['Q22'];

            // $rv[$row['_ISBN']]['V'] =  round($row['V']/1000,0).'K';
            // // $rv[$row['_ISBN']]['V22'] = ($row['V22']< 1) ? '-' :  round($row['V22']/1000,0).'K';
            // // $rv[$row['_ISBN']]['PC'] =  ($row['PC']<= 0) ? '-' : round($row['PC'],1).'%';

            // $rv[$row['_ISBN']]['Idx'] = $row['CostIndex'];
        }



        foreach ($rv as $key => $row)
        {
            $resp[] = $row;

        }

        // d($rv);
        // die();
        return $resp;

    }

    public function action_brands()
    {
        echo 'mak prime';
    }

    private function getStockAmount($segment=1)
    {
        // $order ='Sub DESC';
        // s($this->segments);

        $this->segment ='';

        if($segment<>0)
        {
            $this->segment = sprintf(" produtos.segmento='%s' AND ",$this->segments[$segment]['Name']);
        }

        $order =' Subtotal DESC';

        if($this->request->post('sort')=='age') $order =' Age DESC';
        if($this->request->post('sort')=='last') $order =' UltimaVenda DESC';

        $this->order = $order;

        // s( $this->pagination);
        $this->pagination = Pagination::factory(array(
            'total_items'    => 100,
            'items_per_page' => 100,
        ));

        $this->idx=1.54;
        $this->mkp=1.42;

        $rows=500;
        if($segment==1) {
            //$order =' V60 DESC, Subtotal DESC';
            $this->idx=1.30;
            $mkp=1.42;
            if($this->request->post('name')) $rows = 100;

            $this->pagination = Pagination::factory(array(
                'total_items'    => 500,
                'items_per_page' => $rows,
            ));
        }
        if($segment==2) {
            $this->idx=1.53;
            $mkp=1.56;
            $this->pagination = Pagination::factory(array(
                'total_items'    => 200,
                'items_per_page' => $rows,
            ));
        }

        if($segment==3) {
            $this->idx=1.53;
            $mkp=1.55;
            $this->pagination = Pagination::factory(array(
                'total_items'    => 200,
                'items_per_page' => $rows,
            ));
        }
        if($segment==5) {
            $this->idx=1.53;
            $mkp=1.52;
            if($this->request->post('line')) $rows = 200;

           $this->pagination = Pagination::factory(array(
                'total_items'    => 500,
                'items_per_page' => $rows,
            ));
        }

        if($segment==0) {
            $this->idx=1.53;
            $mkp=1.52;
            if($this->request->post('line')) $rows = 200;

           $this->pagination = Pagination::factory(array(
                'total_items'    => 500,
                'items_per_page' => $rows,
            ));
        }
        //if($segment==3) $this->idx=30;

        $w="";

        $filters    = $_GET['filter'];
        $fields     = $_GET['field'];
        $operators  = $_GET['operator'];

        // foreach ($filters as $kix => $filter)
        // {
        //     if (isset($filter) and strlen($filter) > 0 and strlen($fields[$kix]) > 0)
        //     {

        //         if ('LIKE %...%' == $operators[$kix]) {
        //             if ('c.cep' == $fields[$kix]) {
        //                 $cp = $fields[$kix]." LIKE '".$filter."%'";
        //             }else{
        //                 $cp = $fields[$kix]." LIKE '%".$filter."%'";
        //             }
        //         } else {
        //             if ('LIKE ...%' == $operators[$kix])
        //             {
        //                 $cp = $fields[$kix]." LIKE '".$filter."%'";
        //             }else{
        //                 if ('LIKE !%...%' == $operators[$kix])
        //                 {
        //                     $cp = $fields[$kix]."! LIKE '%".$filter."%'";
        //                 }else{
        //                     $cp = $fields[$kix].' '.$operators[$kix]." '".$filter."'";
        //                 }
        //             }
        //         }

        //         $w .= ' AND '.$cp;
        //     }
        // }
        $having=' 1=1  ';

        foreach ($filters as $kix => $filter)
        {
            if (isset($filter) and strlen($filter) > 0 and strlen($fields[$kix]) > 0)
            {

                if ('LIKE %...%' == $operators[$kix]) {
                    if ('c.cep' == $fields[$kix]) {
                        $cp = $fields[$kix]." LIKE '".$filter."%'";
                    }else{
                        $cp = $fields[$kix]." LIKE '%".$filter."%'";
                    }
                } else {
                    if ('LIKE ...%' == $operators[$kix])
                    {
                        $cp = $fields[$kix]." LIKE '".$filter."%'";
                    }else{
                        if ('LIKE !%...%' == $operators[$kix])
                        {
                            $cp = $fields[$kix]."! LIKE '%".$filter."%'";
                        }else{
                            $cp = $fields[$kix].' '.$operators[$kix]." '".$filter."'";
                        }
                    }
                }

                $having .= ' AND '.$cp;
            }
        }

        //  echo $having;



        if(isset($_GET['nome'])) {
            $w.=sprintf(" AND inv.nome LIKE '%s' ", $_GET['nome'].'%' );
             $this->pagination = Pagination::factory(array(
            'total_items'    => 200,
            'items_per_page' => $rows,
        ));
        }

        if(isset($_GET['catalog'])) {
            $w.=sprintf(" AND inv.edescricao LIKE '%s' ", '%'.$_GET['catalog'].'%' );
             $this->pagination = Pagination::factory(array(
            'total_items'    => 200,
            'items_per_page' => $rows,
        ));
        }
        // $cache_id = 'main'.$this->segment.DBS;
        // $cache = Cache::instance('memcache');
        // $cache->delete($cache_id );
        // if ( ! $result = $cache->get($cache_id) )
        // {

        // s($this->idx,
        //                   _DOLAR,
        //                   $this->idx,
        //                   _DOLAR,
        //                   $this->idx,
        //                   _DOLAR,
        //                   $mkp,

        //                   $this->segments,
        //                   $w,

        //                   $order,
        //                   $this->pagination->offset,
        //                   $this->pagination->items_per_page);

        if($this->request->post('vip')) $w.=sprintf(" AND inv.vip =%s  ",     $this->request->post('vip'));
        if($this->request->post('unity')) $having.=sprintf(" AND %s > 0  ",     $this->request->post('unity'));
        if($this->request->post('brand')) $w.=sprintf(" AND inv.marca = '%s' ",     $this->request->post('brand'));
        if($this->request->post('line'))  $w.=sprintf(" AND inv.modelo LIKE '%s' ", $this->request->post('line').'%' );
        if($this->request->post('model'))  $w.=sprintf(" AND inv.modelo LIKE  '%s' ", '%'.  $this->request->post('model').'%' );
        if($this->request->post('name'))  $w.=sprintf(" AND inv.nome LIKE '%s' ",   $this->request->post('name').'%' );

        // s($this->request->query());
        if($this->request->query('vip')) $w.=sprintf(" AND inv.vip='%s' ",     $this->request->query('vip'));
        if($this->request->query('unity')) $having.=sprintf(" AND %s > 0  ",     $this->request->query('unity'));
        if($this->request->query('brand')) $w.=sprintf(" AND inv.marca = '%s' ",     $this->request->query('brand'));
        if($this->request->query('line'))  $w.=sprintf(" AND inv.modelo LIKE '%s' ", $this->request->query('line').'%' );
        if($this->request->query('model'))  $w.=sprintf(" AND inv.modelo LIKE  '%s' ", '%'.  $this->request->query('model').'%' );
        if($this->request->query('name'))  $w.=sprintf(" AND inv.nome LIKE '%s' ",   $this->request->query('name').'%' );

        // ,

							 //  	(SELECT TO_DAYS(now()) - TO_DAYS(shipments.status)+1 as dias
								//   FROM   next
								//   RIGHT  JOIN shipments ON shipments.id=next.shipment
								//   WHERE  month(shipments.status)>0 and next.isbn=inv.id and next.state <>9 and next.quant >0
								//   ORDER BY dias asc
								//   LIMIT 1) as Idade,
								//   (SELECT TO_DAYS(NOW()) - TO_DAYS(hoje.data)+1
					   //         FROM  hoje,hist
						  //      WHERE hoje.id=hist.pedido AND  hist.isbn =inv.id  AND hoje.nop in (27,28,51,76)
						  //      GROUP BY hoje.data
						  //      ORDER BY hist.id DESC
						  //      LIMIT 1 ) UltimaVenda,
						  //       (
							 //  (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							 //  +
							 //  (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							 //  +
							 //  (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							 //  +
							 //  (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							 //  +
							 //  (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

						  // ) as Estoque

       // echo $having;

 $sql = sprintf("
						SELECT
						 inv.id as _ISBN,
						 (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`) AS E1,
						 (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`) AS E3,
						 (SELECT EstoqueDisponivel FROM mak_0885.Estoque e8 WHERE inv.id=e8.`ProdutoPOID`) AS E8,
						 (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)   +  (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`) AS E6,

						(
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

						)
							   *fob*%s*%s AS Subtotal,

						(
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

						   ) as Estoque,
						   ROUND(
						(
						   (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

						   )
                        	/
                        	 round((SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 90 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                        	GROUP BY hist.isbn)/3,0)
                        ), 0) AS VidaEstoqueMeses,

						    (SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 365 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                			GROUP BY hist.isbn) AS V365,

                			(SELECT TO_DAYS(now()) - TO_DAYS(shipments.status)+1 as dias
								  FROM   next
								  RIGHT  JOIN shipments ON shipments.id=next.shipment
								  WHERE  month(shipments.status)>0 and next.isbn=inv.id and next.state <>9 and next.quant >0
								  ORDER BY dias asc
								  LIMIT 1) as IdadeDias,

							 (SELECT TO_DAYS(NOW()) - TO_DAYS(hoje.data)+1
					            FROM  hoje,hist
						        WHERE hoje.id=hist.pedido AND  hist.isbn =inv.id  AND hoje.nop in (27,28,51,76)
						        GROUP BY hoje.data
						        ORDER BY hist.id DESC
						        LIMIT 1 ) UltimaVendaDias,

                			produtos.ncm,
                			inv.estoque_min AS EstoqueMínimo,
				 		   inv.estoque_max AS EstoqueMáximo,
				 		   inv.estoque_protecao AS EstoqueDeProteção,
				 		   inv.estoque_transito AS EstoqueDeTrânsito


						FROM 	produtos,inv
						WHERE
								produtos.id=inv.idcf AND
								%s
								(
								inv.modelo NOT LIKE  '%%DOL%%'  AND
								inv.modelo NOT LIKE  '%%TS%%' AND
								inv.modelo NOT LIKE  '%%WR%%' AND
								inv.modelo NOT LIKE  '%%HVP%%'  AND
								inv.modelo NOT LIKE  '%%YL%%'
								)
								%s
					    HAVING   %s
						ORDER  BY %s
						LIMIT %s,%s",
                           $this->idx,
            _DOLAR,

                           $this->segment,
                           $w,
                           $having,
                           $order,
                           $this->pagination->offset+$this->pagination->current_page-1,
                           $this->pagination->items_per_page);

            // $result = $this->action_connect_SELECT($sql);

// die();
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

        $ids = [];
            foreach( $result as $key => $val)
            {

                $ids[]= $val['_ISBN'];

                foreach( $val as $k => $v)
                {
                    // if($v['Next'] < 0 or is_null($v['Next']))
                    // {
                    //     $result[$k]['Next'] = 'x';
                    // }else{
                    //   $result[$k]['Next'] = 'y';

                    if($v < 0 or is_null($v))  $result[$key][$k] = 0;
                    if($k=='Subtotal')  $result[$key][$k] = round($v,2);
                    // }
                }
            //     // $resp[$v['_ISBN']]= $v;
            // //$resp[$v['id']]['STKT'] = $v['STK1']+$v['STK2']+$v['STK3']+$v['STK5']+$v['STK6'];
            //     //$resp[$v['id']]['Subtotal'] = $resp[$v['id']]['STK']["STKT"]*$v['Subtotal'];
            //     //if($vtotal>0)	$resp[$v['product_id']]['share']= ($v['Subtotaltotal']/$vtotal)*100;
            //     //	$resp[$v['id']]['Age'] = Request::factory('produto/age/index/'.$this->segment_id.'/'.$v['id'])->execute()->response;
            //     //	$resp[$v['id']]['30'] = Request::factory('produtos/30/'.$v['id'])->execute()->response;
            //   // $resp[$v['id']]['costs'] = Request::factory('produto/cost_calc/'.$v['id'])->execute()->response;
            //     // $resp[$v['id']]['V30'] = $this->action_V30D($v['id']);
            //     // $resp[$v['id']]['V90'] = $this->action_V90D($v['id']);
            //     // $resp[$v['id']]['comprar'] = ($v['V60'] / 2 * 3) - ($v['next1'] + $v['qty']);

            }



        if(empty($ids)) return []; //die('<div class="h-screen bg-gray-900 text-white" >Sem Registros</div>');

        $products = implode(',',$ids);
            //s($ids,$products);
            //arsort($resp);

            // Cache $products for seconds
        //     $cache->set($cache_id, $resp, 60*60*1);
        // }

        //		print('<pre>');
        //		print_r($resp);
        //		exit;

         $data  = self::getDetails($segment,$products,$result);


         return  $data;

        // d($view);
        // $this->response->body($view);


        // $view='main';
        // if(isset($_GET['view']))  $view=$_GET['view'];
        // $response =  View::factory('products/'.$view)
        //     ->set('title',$this->segment)
        //     ->set('pagination',$this->pagination )
        //     ->set('produtos', $resp)
        //     ->set('mkp', $mkp)
        //     ->set('idx', $this->idx)
        //     ;

        // $this->request->response =$response;
    }

    private function getDetails($segment=1,$products=[],$data)
    {

        // s($data);
            // $this->segment = $this->segments[$segment]['Name'];
        $order = $this->order;

        $this->pagination = Pagination::factory(array(
            'total_items'    => 100,
            'items_per_page' => 10,
        ));

        $this->idx=1.54;
        $mkp=1.42;


        if($segment==1) {
            //$order =' V60 DESC, Sub DESC';
            $this->idx=1.27;
            $mkp=1.42;
            $this->pagination = Pagination::factory(array(
                'total_items'    => 500,
                'items_per_page' => 10,
            ));
        }
        if($segment==2) {
            $this->idx=1.60;
            $mkp=1.6;
            $this->pagination = Pagination::factory(array(
                'total_items'    => 200,
                'items_per_page' => 10,
            ));
        }
          if($segment==5) {
            $this->idx=1.25;
            $mkp=1.52;
            $this->pagination = Pagination::factory(array(
                'total_items'    => 200,
                'items_per_page' => 10,
            ));
        }
        //if($segment==3) $this->idx=30;

        $w="";
        // if(isset($_GET['filter'])) {
        //     $w.=sprintf(" AND inv.modelo LIKE '%s' ", $_GET['filter'].'%' );
        //      $this->pagination = Pagination::factory(array(
        //     'total_items'    => 200,
        //     'items_per_page' => 5,
        // ));
        // }

        if(isset($_GET['nome'])) {
            $w.=sprintf(" AND inv.nome LIKE '%s' ", $_GET['nome'].'%' );
             $this->pagination = Pagination::factory(array(
            'total_items'    => 200,
            'items_per_page' => 5,
        ));
        }

        if(isset($_GET['catalog'])) {
            $w.=sprintf(" AND inv.edescricao LIKE '%s' ", '%'.$_GET['catalog'].'%' );
             $this->pagination = Pagination::factory(array(
            'total_items'    => 200,
            'items_per_page' => 5,
        ));
        }

        // 						    inv.Revenda*.85 AS P15,
						  //  inv.Revenda*.80 AS P20,
						  //  inv.fob,
						  //  inv.fob*9 F9,
						  //  inv.fob*9.5 F9_5,
						  //  inv.fob*10 F10,
						  //  inv.fob*11 F11,
						  //  inv.fob*12 F12,

        $sql = sprintf("
						SELECT
						    inv.id as _id,
						    inv.id as _ISBN,
						    inv.modelo as Modelo,
						    inv.marca as Brand,
						    inv.Fob,
						    inv.Revenda,
						    inv.fob*10 AS RV100,
						    inv.fob*9.5 AS RV95,
						    inv.fob*9 AS RV90,
						    inv.fob*8.5 AS RV85,
						    inv.vip as vip,
						    inv.rfm as RFM,
                            inv.abc as ABC,
                             (SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 0 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                        	GROUP BY hist.isbn) AS V0,
                             (SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 7 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                        	GROUP BY hist.isbn) AS V7,
                            (SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 30 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                        	GROUP BY hist.isbn) AS V30,

				            (SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 60 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                			GROUP BY hist.isbn) AS V60,

                	        (SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 90 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                			GROUP BY hist.isbn) AS V90,
                                		 (SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 365 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                			GROUP BY hist.isbn) AS V365,

                			(SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	YEAR(NOW()) = YEAR(hoje.datae) AND                        			        
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                			GROUP BY hist.isbn) AS V25,


                			(SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	YEAR(NOW())-1 = YEAR(hoje.datae) AND                        			        
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                			GROUP BY hist.isbn) AS V24,


                            (SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	YEAR(NOW())-2 = YEAR(hoje.datae) AND                        			        
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                			GROUP BY hist.isbn) AS V23,

						    		(
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							    +
							   (SELECT EstoqueDisponivel FROM mak_0966.Estoque e9 WHERE inv.id=e9.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

							)*fob*%s*%s AS Subtotal,

						    CONCAT(inv.modelo,' (', if(inv.Embalagem=1,'cx','ind'),')' ) as _Modelo,
						    pq.Quality AS _Quality,
						    if(inv.Embalagem=1,'ind. box','bulk') as _Packing,


						    						                            							         (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0966.Estoque e9 WHERE inv.id=e9.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							    (SELECT EstoqueReservado FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueReservado FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueReservado FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueReservado FROM mak_0966.Estoque e9 WHERE inv.id=e9.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0966.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)
							   +
							   (SELECT EstoqueReservado FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

						   ) as Estoque,

						      (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`) as BarraFunda,
						    (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6 WHERE inv.id=e6.`ProdutoPOID`) as BluMenau,
						    (SELECT EstoqueDisponivel FROM mak_0966.Estoque e9 WHERE inv.id=e9.`ProdutoPOID`)+
							   (SELECT EstoqueDisponivel FROM mak_0966.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`) as BluMenau2,
						    (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`) as TatuApé,


						     (SELECT TO_DAYS(NOW()) - TO_DAYS(hoje.data)+1
					            FROM  hoje,hist
						        WHERE hoje.id=hist.pedido AND  hist.isbn =inv.id  AND hoje.nop in (27,28,51,76)
						        GROUP BY hoje.data
						        ORDER BY hist.id DESC
						        LIMIT 1 ) UltimaVendaDias,


				    		(SELECT TO_DAYS(now()) - TO_DAYS(shipments.status)+1 as dias
								  FROM   next
								  RIGHT  JOIN shipments ON shipments.id=next.shipment
								  WHERE  month(shipments.status)>0 and next.isbn=inv.id and next.state <>9 and next.quant >0
								  ORDER BY dias asc
								  LIMIT 1) as IdadeDias,

						ROUND(
						(
						   (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

						   )
                        	/
                        	 round((SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 90 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                        	GROUP BY hist.isbn)/3,0)
                        ), 0) AS VidaEstoqueMeses,
						   (SELECT   sum(n.quant)
						        FROM next n
                                LEFT JOIN  shipments sh on (sh.id=n.shipment)
                                LEFT JOIN  next_stage ns on (ns.id=n.stage)
				                WHERE   n.quant>0 AND
                                    n.state <>9 AND
                                    MONTH( sh.status)=0 AND
                                    n.isbn = inv.id AND
                                    ns.stage='shipping'
                                GROUP BY ns.stage) as InTransit,


						   	(SELECT SUM(n.quant)  FROM next n ,shipments s WHERE s.id=n.shipment AND month(s.status)=0
						    and  n.isbn =inv.id and n.state<>9 GROUP BY n.isbn) -  (SELECT   sum(n.quant)
						        FROM next n
                                LEFT JOIN  shipments sh on (sh.id=n.shipment)
                                LEFT JOIN  next_stage ns on (ns.id=n.stage)
				                WHERE   n.quant>0 AND
                                    n.state <>9 AND
                                    MONTH( sh.status)=0 AND
                                    n.isbn = inv.id AND
                                    ns.stage='shipping'
                                GROUP BY ns.stage)
                                AS InFactory ,

						    	(SELECT SUM(n.quant)  FROM next n ,shipments s WHERE s.id=n.shipment AND month(s.status)=0
						    and  n.isbn =inv.id and n.state<>9 GROUP BY n.isbn)
                                AS TotalNext ,


                                  
                			'' as Buffer3,
                			'' as Buffer6,
                			'' as Buffer12,


                			  inv.Peso,
                			  inv.PesoSet,
                			  inv.VolumeSet,







				 		   inv.estoque_transito AS _EstoqueDeTrânsito,
						    inv.nome as _Nome,
						    	inv.estoque_min AS _EstoqueMínimo,
				 		   inv.estoque_max AS _EstoqueMáximo,
				 		   inv.estoque_protecao AS _EstoqueDeProteção,


						    round((SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 90 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                        	GROUP BY hist.isbn)/3,0)  AS Avg90,

                        	(SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	YEAR(NOW()) = YEAR(hoje.data)  AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                        	GROUP BY hist.isbn)  AS VendasAno,




							inv.memoh as _Obs,



							(fob) AS Sugerido,






                            '' as PesoTotal,

						    inv.eDescricao as _Descricao,
					        inv.mARCA AS _Marca,

						  inv.CostIndex _CostIndex,

							 (SELECT
							   	sum(hist.valor_base*hist.quant)/sum(hist.quant)
								FROM  hist,hoje
								WHERE 	hoje.nop in (27,28,51,76)  AND
						 		 		hist.pedido=hoje.id  AND
        						 		inv.id=hist.isbn AND YEAR(hoje.data)=YEAR(NOW())
								GROUP BY hist.isbn

				    		) AS _ValorMedioAno ,

							inv.volume as Volume,
							inv.volumeset as _VolumeSet,






						    ( select if (MONTH(shipments.arrival)>0, TO_DAYS(shipments.arrival)+15, TO_DAYS(shipments.date)+45) - TO_DAYS(now()) from shipments, next where shipments.id=next.shipment AND inv.id=next.isbn AND month(shipments.status)=0 and month(shipments.date)>0  limit 1 ) AS PreVisao,


                            (SELECT   sum(n.quant)
						        FROM next n
                                LEFT JOIN  shipments sh on (sh.id=n.shipment)
                                LEFT JOIN  next_stage ns on (ns.id=n.stage)
				                WHERE   n.quant>0 AND
                                    n.state <>9 AND
                                    MONTH( sh.status)=0 AND
                                    n.isbn = inv.id AND
                                    ns.stage='factory'
                                GROUP BY ns.stage) as _PrograMados,



						     (SELECT 	SUM(hist.quant) as qProd
                        			FROM 	hist,hoje
                        			WHERE 	TO_DAYS(NOW()) - TO_DAYS(hoje.datae) <= 0 AND
                        				  	hoje.nop in (27,28,51,76)  AND
                        					hist.isbn=inv.id AND
                        					hist.pedido=hoje.id  AND
                        					hist.idcli <>707602 AND
                        					hist.valor_base>0
                        	GROUP BY hist.isbn) AS _V1















						FROM 	inv

                           LEFT JOIN Catalogo.packing on (packing.id=inv.embalagem)
                           LEFT JOIN  packing pk ON (pk.id=inv.embalagem)
                             LEFT JOIN  products_specifications ps ON (ps.id=inv.id)
                             LEFT JOIN  product_quality pq ON (pq.id=ps.quality_id)
						WHERE   inv.id in (%s)
						ORDER  BY %s


						",
					    $this->idx,
            _DOLAR,

                        $products,
                           $order

                        );

            // $result = $this->action_connect_SELECT($sql);
        //      inv.juros  as _Juros,
						  //  inv.vezes as _Vezes,
						  //  inv.habilitado as HabilitadoCostura,

				// 			inv.revenda2 as Varejo,
				// 			  inv.estoque_min AS EstoqueMínimo,
				// 		   inv.estoque_max AS EstoqueMáximo,
				// 		   inv.estoque_protecao AS EstoqueDeProteção,
				// 		   inv.estoque_transito AS EstoqueDeTrânsito



        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

            foreach( $result as $key => $val)
            {
                //  if(isset($val['Revenda2']) and $segment==5 ) $result[$key]['MercadoLivre'] = $val['Revenda2'] ;

                //  $result[$key]['StkMin']  = round(max($val['V30'],$val['V60']/2,$val['V90']/3,$val['V365']/12),0) ;

                $result[$key]['StkMin']  = round(max($val['V30'],$val['V60']/2,$val['V90']/3,$val['V365']/12),0) ;

                $result[$key]['StkMin6x']  = $result[$key]['StkMin']*6 ;
                $result[$key]['Buffer3']  = ($result[$key]['StkMin6x']/2)-$result[$key]['Estoque']-$result[$key]['TotalNext'] ;
                $result[$key]['Buffer6']  = $result[$key]['StkMin6x']-$result[$key]['Estoque']-$result[$key]['TotalNext'] ;
                $result[$key]['Buffer12']  = ($result[$key]['StkMin6x']*2)-$result[$key]['Estoque']-$result[$key]['TotalNext'] ;

                $result[$key]['PesoTotal']  = round($result[$key]['Peso'] *$result[$key]['Estoque'],0);

                if($segment==1) {
                    $cost=1.20;
                    $tax=1.0;
                    $mkp=1.42;
                    $result[$key]['Sugerido']  = round($result[$key]['Sugerido'] * $cost*$tax*_DOLAR*$mkp,0);
                    // $result[$key]['BurnNfEmSp']  = round($result[$key]['Sugerido'] /$mkp,2);
                    // $result[$key]['BurnNfForaSp']  = round($result[$key]['Sugerido'] /$mkp*.97,2);
                    // $result[$key]['SugeridoScBr']  = round($result[$key]['Sugerido']*0.9644 ,0);
                    // $result[$key]['SugeridoScSp'] = round($result[$key]['Sugerido']*1 ,0);
                    // $result[$key]['SugeridoTela']  = round($result[$key]['Sugerido']/0.9,0);
                 }

                //  if($segment==2) {
                //     $cost=1.25;
                //     $tax=1.43;
                //     $mkp=1.25;
                //     $result[$key]['Sugerido']  = round($result[$key]['Sugerido'] * $cost*$tax*_DOLAR*$mkp,2);
                //   $result[$key]['BurnNfEmSp']  = round($result[$key]['Sugerido'] /$mkp,2);
                //     $result[$key]['BurnNfForaSp']  = round($result[$key]['Sugerido'] /$mkp*.86,2);
                //     $result[$key]['SugeridoScBr']  = round($result[$key]['Sugerido']*0.86 ,2);
                //     $result[$key]['SugeridoScSp'] = round($result[$key]['Sugerido']*0.9 ,2);
                //     $result[$key]['SugeridoTela']  = round($result[$key]['Sugerido']/0.7,2);
                //  }

                //  if($segment==3) {
                //     $cost=1.25;
                //     $tax=1.43;
                //     $mkp=1.25;
                //     $result[$key]['Sugerido']  = round($result[$key]['Sugerido'] * $cost*$tax*_DOLAR*$mkp,2);
                //   $result[$key]['BurnNfEmSp']  = round($result[$key]['Sugerido'] /$mkp,2);
                //     $result[$key]['BurnNfForaSp']  = round($result[$key]['Sugerido'] /$mkp*.86,2);
                //     $result[$key]['SugeridoScBr']  = round($result[$key]['Sugerido']*0.86 ,2);
                //     $result[$key]['SugeridoScSp'] = round($result[$key]['Sugerido']*0.9 ,2);
                //     $result[$key]['SugeridoTela']  = round($result[$key]['Sugerido']/1,2);
                //  }


                //                   if($segment==5) {
                //     $cost=1.25;
                //     $tax=1.43;
                //     $mkp=1.25;
                //     $result[$key]['Sugerido']  = round($result[$key]['Sugerido'] * $cost*$tax*_DOLAR*$mkp,2);
                //   $result[$key]['BurnNfEmSp']  = round($result[$key]['Sugerido'] /$mkp,2);
                //     $result[$key]['BurnNfForaSp']  = round($result[$key]['Sugerido'] /$mkp*.86,2);
                //     $result[$key]['SugeridoScBr']  = round($result[$key]['Sugerido']*0.86 ,2);
                //     $result[$key]['SugeridoScSp'] = round($result[$key]['Sugerido']*0.9 ,2);
                //     $result[$key]['SugeridoTela']  = round($result[$key]['Sugerido']/1,2);
                //  }

                foreach( $val as $k => $v)
                {
                    if($v < 0 or is_null($v))  $result[$key][$k] = 0;

                    $result[$key][$k] = $result[$key][$k];
                }

                //  $result[$key]['VidaEstoqueMeses'] = $result[$key]['VidaEstoque+EmTransitoMeses'] = 0;
                //  if($val['Avg90']>0) $result[$key]['VidaEstoque+EmTransitoMeses']    = round((($val['Estoque']+$val['EmTransito'])/$val['Avg90'])*1,1);
                //  if($val['Avg90']>0) $result[$key]['VidaEstoqueMeses'] = round(($val['Estoque']/$val['Avg90'])*1,1);

                // 	inv.revenda2 as MercadoLivre,

            //	$resp[$v['id']]['Age'] = Request::factory('produto/age/index/'.$this->segment_id.'/'.$v['id'])->execute()->response;
            //  $resp[$v['id']]['costs'] = Request::factory('produto/cost_calc/'.$v['id'])->execute()->response;

            }

        return  $result;

        // d($view);
    }

    public function action_nextStages($productId=1649975)
    {
        $sql = sprintf(
                "SELECT
                        ns.stage            as stage,
                        sum(n.quant)             as Qty,
                        n.fob               as Price

                 FROM 	produtos p,next n
                 LEFT JOIN  shipments sh on (sh.id=n.shipment)
                 LEFT JOIN  next_stage ns on (ns.id=n.stage)
                 LEFT JOIN  inv i ON (i.id=n.isbn)
				 WHERE  p.id=i.idcf AND
                        n.quant>0 AND
                        n.state <>9 AND
                        MONTH( sh.status)=0 AND
                        i.id = %s
                 GROUP BY stage
                 ORDER BY stage
                 LIMIT 15
            ", $productId);

        $result  =  $this->action_connect_SELECT($sql);
        s($result);
}

    public function action_next_all($productId=1649975)
    {
        $sql = sprintf(
                "SELECT
                        n.data              as Date_Request,
                        n.timestamp         as Timestamp,
                        UCASE(p.segmento)   as Division,
                        n.excel             as File,
                        n.excel             as QuotationID,
                        n.order_number      as OrderID,
                        UCASE(s.brand)      as SupplierName,
                        s.id                as SupplierId,
                        COUNT(n.id)         as Itens,
                        ns.stage            as stage,
                        ns.dsc_chinese      as dsc_chinese,
                        ns.color            as color,
                        n.stage             as stageID,
                        sh.invoice          as Invoice,
                        n.id                as ID,
                        i.id                as ISBN,
                        i.revenda           as Revenda,
                        n.shipment          as SHID,
                        i.modelo            as Model,
                        i.nome              as Name,
                        UCASE(n.brand)      as Brand,
                        i.peso              as Weight,
                        n.quant             as Qty,
                        n.fob               as Price,
                        n.myfob             as Target,
                        n.qualidade         as Quality,
                        n.blindagem         as Shield,
                        pk.description      as Packing,
                        i.codebar           as Barcode,
                        n.obs               as Requirement,
                        n.etd               as ETD,
                        pq.quality          as ps_quality,
                        pq.description      as ps_name,
                        ps.id               as PSID,
                        (SELECT nick FROM `users` u WHERE  u.id=n.user) as user

                 FROM 	produtos p,next n
                 LEFT JOIN  shipments sh on (sh.id=n.shipment)
			     LEFT JOIN  supplier s on (s.id=n.supplier)
                 LEFT JOIN  next_stage ns on (ns.id=n.stage)
                 LEFT JOIN  inv i ON (i.id=n.isbn)
                 LEFT JOIN  packing pk ON (pk.id=i.embalagem)
                 LEFT JOIN  products_specifications ps ON (ps.id=n.isbn)
                 LEFT JOIN  product_quality pq ON (pq.id=ps.quality_id)

				 WHERE  p.id=i.idcf AND
                        n.quant>0 AND
                        n.state <>9 AND
                        MONTH( sh.status)=0 AND
                        i.id = %s

                 GROUP BY n.id
                 ORDER BY Model, Requirement asc
                 LIMIT 1
            ", $productId);

        $result  =  $this->action_connect_SELECT($sql);
        s($result);
}

    private function getTotalAmount($segment=1)
    {
        $sql = sprintf("
						SELECT

					    SUM(	   (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

							   )

						) AS QuantidaddeTotal,

						SUM(	   (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

							   )
							   *fob*%s*%s
						) AS ValorTotal,
						SUM(	   (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

							   )
							   *peso
						) AS PesoTotal,
							SUM(	   (
							   (SELECT EstoqueDisponivel FROM mak_0109.Estoque e1 WHERE inv.id=e1.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0370.Estoque e3 WHERE inv.id=e3.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0885.Estoque e5 WHERE inv.id=e5.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque e6 WHERE inv.id=e6.`ProdutoPOID`)
							   +
							   (SELECT EstoqueDisponivel FROM mak_0613.Estoque_TTD_1 e6t WHERE inv.id=e6t.`ProdutoPOID`)

							   )
							   *volume
						) AS VolumeTotal



						FROM 	produtos,inv
						WHERE
								produtos.id=inv.idcf AND
								produtos.segmento='%s' AND
								(
								inv.modelo NOT LIKE  '%%DOL%%'  AND
								inv.modelo NOT LIKE  '%%TS%%' AND
								inv.modelo NOT LIKE  '%%WR%%' AND
								inv.modelo NOT LIKE  '%%HVP%%'  AND
								inv.modelo NOT LIKE  '%%YL%%'
								)


					   ",

                           $this->idx,
                           _DOLAR,
                           $this->segment


                          );

                $query = DB::query(Database::SELECT, $sql);
                $result = $query->execute()->as_array();

            foreach( $result as $key => $val)
            {

                if($this->segment=='machines') $result[$key]['Containers'] =  round($val['VolumeTotal']/56,2);
                if($this->segment=='bearings') $result[$key]['Containers'] = round($val['PesoTotal']/25000,2);
                if($this->segment=='parts') $result[$key]['Containers'] =  round($val['VolumeTotal']/56,2);
                if($this->segment=='faucets') $result[$key]['Containers'] = round($val['PesoTotal']/15000,2);
                if($this->segment=='auto') $result[$key]['Containers'] = round($val['PesoTotal']/25000,2);



                foreach( $val as $k => $v)
                {
                    if($v < 0 or is_null($v))  $result[$key][$k] = 0;
                    $result[$key][$k] = round($v,2);


                }
            }

                return $result;

    }


    public function action_moto()
    {
        define('AUTH', base64_encode('Rolteam:test'));


        // AND catalogoTipoMaquina:["" TO *]
        $cats[5] = '%20AND%20produtoModelo:63*';
        $cats[6] = '%20AND%20produtoModelo:62*';
        $cats[7] = '%20AND%20produtoModelo:60*';
        $cats[8] = '%20AND%20produtoModelo:K*';
        $cats[9] = '%20AND%20produtoModelo:HK*';
        $cats[10] = '%20AND%20produtoModelo:F*';
        $cats[11] = '%20AND%20produtoModelo:68*';
        $cats[12] = '%20AND%20produtoModelo:69*';
        $cats[13] = '%20AND%20produtoModelo:32*';
        $cats[14] = '%20AND%20 ( produtoPOID:15015%20OR%20produtoPOID:30039 ) ';

        $cats[1] = '%20AND%20categoriaNome:%20"Rolamentos%20para%20Moto"';
        $cats[2] = '%20AND%20categoriaNome:%20"Caixa%20de%20Direção"';
        $cats[3] = '%20AND%20categoriaNome:%20"Pedal%20de%20Partida"';
        $cats[4] = '%20AND%20categoriaNome:%20"Pedal%20de%20Câmbio"';

        // if($this->request->post('brand')) $w.=sprintf(" AND inv.marca = '%s' ",     $this->request->post('brand'));
        // if($this->request->post('line'))  $w.=sprintf(" AND inv.modelo LIKE '%s' ", $this->request->post('line').'%' );
        // if($this->request->post('name'))  $w.=sprintf(" AND inv.nome LIKE '%s' ",   $this->request->post('name').'%' );



        if($this->request->post('division') and $this->request->post('division')>0 and $this->request->post('division')<20)
        {
            $cat[] = $cats[$this->request->post('division')];
        }else{
            $cat = $cats;
        }

        // s($cat);


        $view='';
        $theme = 3;
        foreach($cat as $k => $v)
        {
            $view.=self::scan($v,$theme);
            $theme++;
            if($theme>3) $theme=1;

        }

        $response = parent::pagefy($view );
        // s($_SERVER);


        $this->response->body($response);


    }

    public function scan($cat,$theme=1)
    {

        $json = self::solr_moto($cat);

        $arr = json_decode($json,true);

        $group = $counter=0;
        foreach($arr['response'][ 'docs'] as $k => $v)
        {
            $ids[] = $v['produtoPOID'];
        }

        $segid = 6;
        $products = implode(',',$ids);
        $this->order = ' Subtotal DESC ';
        $details = self::getDetails(6,$products,[]);

         foreach($details as $k => $v)
        {
            if($k>50) continue;
            $data[$k] = $v;

        }

        //// Format Data
        $fmt['Sugerido'] = $fmt['Revenda'] = $fmt['Diff'] = $fmt['ValorMedioAno'] = '2us';
        $fmt['Fob'] = $fmt['LastFob'] = '3us';

        if($segid==1)
            $fmt['Fob'] = $fmt['LastFob'] = $fmt['Sugerido'] = $fmt['Revenda'] = $fmt['ValorMedioAno'] = '0br';

        $fmt['Subtotal'] = $fmt['Acumulado']  = '1kb';
        $fmt['AGE'] = '0us';

        $data['format'] = $fmt;

        //// Editable
         $edt['Obs'] = 'mak.inv|memoh|id|_ISBN';
        $edt['vip'] = 'mak.inv|vip|id|_ISBN';
        $edt['Juros'] = 'mak.inv|juros|id|_ISBN';
        $edt['Vezes'] = 'mak.inv|vezes|id|_ISBN';
        $edt['EstoqueMínimo'] = 'mak.inv|estoque_min|id|_ISBN';
        $edt['EstoqueMáximo'] = 'mak.inv|estoque_max|id|_ISBN';
        $edt['EstoqueDeProteção'] = 'mak.inv|estoque_protecao|id|_ISBN';
        $edt['EstoqueDeTrânsito'] = 'mak.inv|estoque_transito|id|_ISBN';

        $data['editable'] = $edt;

        // s( $this->pagination);
        $data['pagination'] = $this->pagination;
        $data['theme'] = $theme;

        $data['caption'][] = array('link'=>'/metrics/stocks/moto/2', 'title' => 'Caixas de Direção') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/3', 'title' => 'Pedal de Partida') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/4', 'title' => 'Pedal de Câmbio') ;
        // $data['caption'][] = array('link'=>'/metrics/stocks/moto/1', 'title' => 'Rolamentos') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/5', 'title' => '6300') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/6', 'title' => '6200') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/7', 'title' => '6000') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/11', 'title' => '6800') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/12', 'title' => '6900') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/13', 'title' => '32000') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/8', 'title' => 'K') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/9', 'title' => 'HK') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/10', 'title' => 'F') ;
        $data['caption'][] = array('link'=>'/metrics/stocks/moto/14', 'title' => 'Virabrequim') ;


        $color['UltimaVendaDias']['class'] = ' text-red-700 text-2xl font-bold ';
        $color['UltimaVendaDias']['rule']  = ' > 30 ';
        $color['IdadeDias']['class'] = ' text-red-500 text-1xl  font-semibold';
        $color['IdadeDias']['rule']  = ' > 90 ';


        // $color['Subtotal'][0]['class'] = ' text-green-400 text-1xl  font-bold';
        // $color['Subtotal'][0]['rule']  = ' > 10000 ';

        // $color['Subtotal'][1]['class'] = ' text-green-600 text-2xl  font-bold';
        // $color['Subtotal'][1]['rule']  = ' > 100000 ';

        // $color['Subtotal'][2]['class'] = ' text-green-900 text-3xl  font-bold';
        // $color['Subtotal'][2]['rule']  = ' > 1000000 ';


        $color['V30']['class'] = ' text-red-700 text-1xl  font-bold';
        $color['V30']['rule']  = ' == 0 ';
        $color['V60']['class'] = ' text-red-700 text-2xl  font-bold';
        $color['V60']['rule']  = ' == 0 ';
        $color['V90']['class'] = ' text-red-700 text-3xl  font-bold';
        $color['V90']['rule']  = ' == 0 ';

         // field color
        $color['Estoque'][0]['class'] = ' text-green-700 text-xl font-bold ';
        $color['Estoque'][0]['rule']  = ' > 0 ';
        // $color['Estoque'][1]['class'] = ' text-red-700 text-xl font-bold ';
        // $color['Estoque'][1]['rule']  = ' Estoque < StkMin ';
        // $color['Estoque'][2]['class'] = ' text-purple-700 text-xl font-bold ';
        // $color['Estoque'][2]['rule']  = ' Estoque > StkMax ';

        $data['color'] = $color;

        // Aplicar cor de fundo verde para campos que começam com 'v' ou 'V' DEPOIS de definir $data['color']
        $data = $this->applyGreenBackgroundToVFields($data);

        $view= parent::tablefy( $data );

        return $view;
        // s($data);
        // exit;

    }
    public function solr_moto($cat)
    {



        $url = ('http://solr.rolemak.com.br/solr/catalogo/select?indent=on&q=*:*&wt=json&fq=segmentoPOID:6%20AND%20produtoAtivo:1'.$cat .'&fl=produtoPOID&start=0&rows=300' );
        $moto = Request::factory( $url )
                ->headers('Authorization', 'Basic '.AUTH)
                ->method('GET')
                ->execute()->body();

        return $moto;
    }


    public  function action_jeditable()
    {
        $w="";

        $date = date("Y-m-d");
        $date1 = str_replace('-', '/', $date);
         $day = date('Y-m-d',strtotime($date1 . "-1 days"));

        if($this->request->post('user')) $w.=sprintf(" AND j.UserId='%s' ", $this->request->post('user') );
        if($this->request->post('day')) $w.=sprintf(" AND Datetime LIKE '%s' ", $day.'%' );

        $sql = sprintf("SELECT `j`.`id`,
                                    i.id as _ISBN  ,
                                    i.modelo as Modelo,
                                    i.marca as Marca,
                                    RecordName as Field,
                                    u.nick as 'By',
                                    Datetime as 'On',
                                    PreviousContent as 'From' ,
                                    NewContent as 'To'

                            FROM history.`jeditable` j
                            LEFT JOIN mak.inv i ON (i.id=j.`PrimaryRecordId`)
                            LEFT JOIN mak.users u ON (u.id=j.UserId)

                        WHERE (`TableName` = 'inv' or `TableName` = 'mak.inv' )  AND i.id=%s %s
                        ORDER BY j.id DESC
                        LIMIT 50",$this->request->post('division'),$w);

        $result = $this->action_connect_SELECT($sql);




        //// Editable


        $data = $result;

        $edt['Peso'] = 'mak.inv|peso|id|_ISBN';
        $edt['PesoSet'] = 'mak.inv|pesoset|id|_ISBN';

        $edt['Volume'] = 'mak.inv|volume|id|_ISBN';
        $edt['VolumeSet'] = 'mak.inv|volumeset|id|_ISBN';
        $data['editable'] = $edt;

        $theme='light';
        $data['theme'] = 2;


        $view= parent::tablefy( $data );

        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);


        // s($result);
    }

    /**
     * Aplica cor de fundo verde para campos que começam com 'v' ou 'V'
     * @param array $data Array de dados completo incluindo configurações de cor
     * @return array Array de dados atualizado
     */
    private function applyGreenBackgroundToVFields($data)
    {
        if (!empty($data) && is_array($data) && isset($data['color'])) {
            // Encontrar uma amostra dos dados para identificar campos
            $sampleRow = null;
            foreach ($data as $key => $value) {
                if (is_array($value) && $key !== 'color' && $key !== 'format' && $key !== 'editable' && $key !== 'sum' && $key !== 'theme' && $key !== 'pagination' && $key !== 'caption') {
                    $sampleRow = $value;
                    break;
                }
            }

            if ($sampleRow && is_array($sampleRow)) {
                foreach ($sampleRow as $fieldName => $fieldValue) {
                    if (strtolower(substr($fieldName, 0, 1)) === 'v') {
                        // Verificar se o campo já tem configuração de cor
                        if (isset($data['color'][$fieldName])) {
                            // Se já existe, verificar se é estrutura de array ou simples
                            if (isset($data['color'][$fieldName][0])) {
                                // Estrutura de array - adicionar nova regra no final
                                $nextIndex = count($data['color'][$fieldName]);
                                $data['color'][$fieldName][$nextIndex]['class'] = ' bg-green-200 text-green-800 font-semibold ';
                                $data['color'][$fieldName][$nextIndex]['rule'] = ' > -999999 '; // Sempre verdadeiro
                            } else {
                                // Estrutura simples - manter mas adicionar fundo verde
                                $existingClass = isset($data['color'][$fieldName]['class']) ? $data['color'][$fieldName]['class'] : '';
                                $data['color'][$fieldName]['class'] = ' bg-green-200 text-green-800 font-semibold ' . $existingClass;
                            }
                        } else {
                            // Campo não tem configuração - criar estrutura simples
                            $data['color'][$fieldName]['class'] = ' bg-green-200 text-green-800 font-semibold ';
                            // Não definir 'rule' para que a cor seja sempre aplicada
                        }
                    }
                }
            }
        }
        return $data;
    }

}