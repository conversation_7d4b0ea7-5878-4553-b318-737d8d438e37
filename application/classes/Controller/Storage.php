<?php

class Controller_Storage extends Controller_Website
{

   
   public function before()
    {
         parent::before();
    }


    public function action_index()
    {     
     
    }

    public function action_files()
    {
      // s($this->request->param('xtras'));
        // $container = base64_decode($this->request->query('container'));
        $container = 'iac-fargate.'.base64_decode($this->request->param('xtras'));
        // echo $container;
        // die();
        
        $base = sprintf($_ENV['s3']."/api/containers/%s/files", $container);
        $url= sprintf($base);
        $request = Request::factory($url)->execute()->body();
        $resp= json_decode($request,true);
        
        // echo $request;
        // die();
        
        $view = count($resp);
        
        $this->response->body($view);
        //s($resp);
    }

  
   
}