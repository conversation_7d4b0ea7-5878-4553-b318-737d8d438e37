<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Tablebootfy  extends Controller {

	public function Tablebootfy($arr)
	{
		// s($arr[0]);
		// s(count($arr[0]));
		$this->subtotal=0;
		$this->qty=0;
		$this->pagination = Pagination::factory(array(
				'total_items'    => 100,
				'items_per_page' => 10,
				// 'current_page' => $arr['pagination']->current_page,
				// 'current_first_item' => 2,
				// 'current_last_item' => 2,
				// 'first_page' => 2,
				// 'next_page' => 2,
				// 'offset' => 2,
				
			));		
			
		$this->theme = 1;
		// $this->pagination->offset;
			
			
		// $arr = $this->request->post('array');
		
		if(empty($arr)) return '';

		if(isset($arr['sum']))
		{
			$this->sum =  $arr['sum'];
			unset($arr['sum']);
		}
		
		if(isset($arr['format']))
		{
			$this->format =  $arr['format'];
			unset($arr['format']);
		}
		
		if(isset($arr['color']))
		{
			$this->color =  $arr['color'];
			unset($arr['color']);
		}
		
		if(isset($arr['editable']))
		{
			$this->editable =  $arr['editable'];
			unset($arr['editable']);
		}
		
		if(isset($arr['theme']))
		{
			$this->theme =  $arr['theme'];
			unset($arr['theme']);
		}
		
			$this->menu='';
		if(isset($arr['menu']))
		{
			$this->menu =  $arr['menu'];
			unset($arr['menu']);
		}
		
		
// <nav class="bg-white px-2 sm:px-4 py-2.5 dark:bg-gray-900 fixed w-full z-20 top-0 left-0 border-b border-gray-200 dark:border-gray-600">
//   <div class="container flex flex-wrap justify-between items-center mx-auto">
 
//   <div class="flex md:order-2">
//       <button type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center mr-3 md:mr-0 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Get started</button>
//       <button data-collapse-toggle="navbar-sticky" type="button" class="inline-flex items-center p-2 text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600" aria-controls="navbar-sticky" aria-expanded="false">
//         <span class="sr-only">Open main menu</span>
//         <svg class="w-6 h-6" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>
//     </button>
//   </div>
//   <div class="hidden justify-between items-center w-full md:flex md:w-auto md:order-1" id="navbar-sticky">
//     <ul class="flex flex-col p-4 mt-4 bg-gray-50 rounded-lg border border-gray-100 md:flex-row md:space-x-8 md:mt-0 md:text-sm md:font-medium md:border-0 md:bg-white dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700">
//       <li>
//         <a href="#" class="block py-2 pr-4 pl-3 text-white bg-blue-700 rounded md:bg-transparent md:text-blue-700 md:p-0 dark:text-white" aria-current="page">Home</a>
//       </li>
//       <li>
//         <a href="#" class="block py-2 pr-4 pl-3 text-gray-700 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-white dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700">About</a>
//       </li>
//       <li>
//         <a href="#" class="block py-2 pr-4 pl-3 text-gray-700 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-white dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700">Services</a>
//       </li>
//       <li>
//         <a href="#" class="block py-2 pr-4 pl-3 text-gray-700 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-white dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700">Contact</a>
//       </li>
//     </ul>
//   </div>
//   </div>
// </nav>

		$menu='';
		$this->caption ="Tabela de dados";
				if(isset($arr['caption']))
		{
			
			$menu = '<nav class="bg-white px-2 sm:px-4 py-2.5 dark:bg-gray-900 fixed w-full z-20 top-0 left-0 border-b border-gray-200 dark:border-gray-600">
					  <div class="lg:flex lg:items-center lg:justify-between">
  <div class="min-w-0 flex-1">
    <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">Carteira de Clientes</h2>
    <div class="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
      <div class="mt-2 flex items-center text-sm text-gray-500">
        <!-- Heroicon name: mini/briefcase -->
        <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M6 3.75A2.75 2.75 0 018.75 1h2.5A2.75 2.75 0 0114 3.75v.443c.572.055 1.14.122 1.706.2C17.053 4.582 18 5.75 18 7.07v3.469c0 1.126-.694 2.191-1.83 2.54-1.952.599-4.024.921-6.17.921s-4.219-.322-6.17-.921C2.694 12.73 2 11.665 2 10.539V7.07c0-1.321.947-2.489 2.294-2.676A41.047 41.047 0 016 4.193V3.75zm6.5 0v.325a41.622 41.622 0 00-5 0V3.75c0-.69.56-1.25 1.25-1.25h2.5c.69 0 1.25.56 1.25 1.25zM10 10a1 1 0 00-1 1v.01a1 1 0 001 1h.01a1 1 0 001-1V11a1 1 0 00-1-1H10z" clip-rule="evenodd" />
          <path d="M3 15.055v-.684c.*************.39.142 2.092.642 4.313.987 6.61.987 2.297 0 4.518-.345 6.61-.987.135-.041.264-.089.39-.142v.684c0 1.347-.985 2.53-2.363 2.686a41.454 41.454 0 01-9.274 0C3.985 17.585 3 16.402 3 15.055z" />
        </svg>
        Full-time
      </div>
      <div class="mt-2 flex items-center text-sm text-gray-500">
        <!-- Heroicon name: mini/map-pin -->
        <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M9.69 18.933l.003.001C9.89 19.02 10 19 10 19s.11.02.308-.066l.002-.001.006-.003.018-.008a5.741 5.741 0 00.281-.14c.186-.096.446-.24.757-.433.62-.384 1.445-.966 2.274-1.765C15.302 14.988 17 12.493 17 9A7 7 0 103 9c0 3.492 1.698 5.988 3.355 7.584a13.731 13.731 0 002.273 1.765 11.842 11.842 0 00.976.544l.***************.006.003zM10 11.25a2.25 2.25 0 100-4.5 2.25 2.25 0 000 4.5z" clip-rule="evenodd" />
        </svg>
        Remote
      </div>
      <div class="mt-2 flex items-center text-sm text-gray-500">
        <!-- Heroicon name: mini/currency-dollar -->
        <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path d="M10.75 10.818v2.614A3.13 3.13 0 0011.888 13c.482-.315.612-.648.612-.875 0-.227-.13-.56-.612-.875a3.13 3.13 0 00-1.138-.432zM8.33 8.62c.**************.**************.46.284.736.363V6.603a2.45 2.45 0 00-.35.13c-.14.065-.27.143-.386.233-.377.292-.514.627-.514.909 0 .**************.592.**************.128.152z" />
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-6a.75.75 0 01.75.75v.316a3.78 3.78 0 011.653.713c.426.33.744.74.925 1.2a.75.75 0 01-1.395.55 1.35 1.35 0 00-.447-.563 2.187 2.187 0 00-.736-.363V9.3c.698.093 1.383.32 1.959.696.787.514 1.29 1.27 1.29 2.13 0 .86-.504 1.616-1.29 2.13-.576.377-1.261.603-1.96.696v.299a.75.75 0 11-1.5 0v-.3c-.697-.092-1.382-.318-1.958-.695-.482-.315-.857-.717-1.078-1.188a.75.75 0 111.359-.636c.08.173.245.376.54.569.313.205.706.353 1.138.432v-2.748a3.782 3.782 0 01-1.653-.713C6.9 9.433 6.5 8.681 6.5 7.875c0-.805.4-1.558 1.097-2.096a3.78 3.78 0 011.653-.713V4.75A.75.75 0 0110 4z" clip-rule="evenodd" />
        </svg>
        $120k &ndash; $140k
      </div>
      <div class="mt-2 flex items-center text-sm text-gray-500">
        <!-- Heroicon name: mini/calendar -->
        <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z" clip-rule="evenodd" />
        </svg>
        Closing on January 9, 2020
      </div>
    </div>
  </div>
  <div class="mt-5 flex lg:mt-0 lg:ml-4">
    <span class="hidden sm:block">
      <button type="button" class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
        <!-- Heroicon name: mini/pencil -->
        <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path d="M2.695 14.763l-1.262 3.154a.5.5 0 00.65.65l3.155-1.262a4 4 0 001.343-.885L17.5 5.5a2.121 2.121 0 00-3-3L3.58 13.42a4 4 0 00-.885 1.343z" />
        </svg>
        Edit
      </button>
    </span>

    <span class="ml-3 hidden sm:block">
      <button type="button" class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
        <!-- Heroicon name: mini/link -->
        <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path d="M12.232 4.232a2.5 2.5 0 013.536 3.536l-1.225 1.224a.75.75 0 001.061 1.06l1.224-1.224a4 4 0 00-5.656-5.656l-3 3a4 4 0 00.225 5.865.75.75 0 00.977-1.138 2.5 2.5 0 01-.142-3.667l3-3z" />
          <path d="M11.603 7.963a.75.75 0 00-.977 1.138 2.5 2.5 0 01.142 3.667l-3 3a2.5 2.5 0 01-3.536-3.536l1.225-1.224a.75.75 0 00-1.061-1.06l-1.224 1.224a4 4 0 105.656 5.656l3-3a4 4 0 00-.225-5.865z" />
        </svg>
        View
      </button>
    </span>

    <span class="sm:ml-3">
      <button type="button" class="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
        <!-- Heroicon name: mini/check -->
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
        </svg>
        Publish
      </button>
    </span>

    <!-- Dropdown -->
    <div class="relative ml-3 sm:hidden">
      <button type="button" class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" id="mobile-menu-button" aria-expanded="false" aria-haspopup="true">
        More
        <!-- Heroicon name: mini/chevron-down -->
        <svg class="-mr-1 ml-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
        </svg>
      </button>

      <!--
        Dropdown menu, show/hide based on menu state.

        Entering: "transition ease-out duration-200"
          From: "transform opacity-0 scale-95"
          To: "transform opacity-100 scale-100"
        Leaving: "transition ease-in duration-75"
          From: "transform opacity-100 scale-100"
          To: "transform opacity-0 scale-95"
      -->
      <div class="absolute right-0 z-10 mt-2 -mr-1 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="mobile-menu-button" tabindex="-1">
        <!-- Active: "bg-gray-100", Not Active: "" -->
        <a href="#" class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1" id="mobile-menu-item-0">Edit</a>
        <a href="#" class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1" id="mobile-menu-item-1">View</a>
      </div>
    </div>
  </div>
</div>

					  <div class="container flex flex-wrap justify-between items-center">
					     <div class="hidden justify-between items-center w-full md:flex md:w-auto md:order-1" id="navbar-sticky">
					      <ul class="flex flex-col p-4 mt-4 bg-gray-50 rounded-lg border border-gray-100 md:flex-row md:space-x-4 md:mt-0 md:text-sm md:font-medium md:border-0 md:bg-white dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700">';
			
			foreach($arr['caption'] as $cap) 
			{
				$menu.= sprintf('<li><a href="%s" class="block py-2 pr-4 pl-3 text-white bg-blue-700 rounded md:bg-transparent md:text-blue-700 md:p-0 dark:text-white" aria-current="page">%s</a></li>', $cap['link'],$cap['title']);
			}
			
			$menu.= '</ul>    </div>  </div></nav>';
			
			 unset($arr['caption']);
		}
		if(isset($arr['caption']))
		{
			
			
			$this->caption = '<ul class="flex">';
			
			foreach($arr['caption'] as $cap) 
			{
				$this->caption.= sprintf('<li class="mr-6"><a class="text-blue-500 hover:text-blue-800" href="%s">%s</a></li>', $cap['link'],$cap['title']);
			}
			
			$this->caption.= '</ul>';
			
			unset($arr['caption']);
		}
		
		
		if(isset($arr['htmx']))
		{
			
			$this->caption = '<ul class="flex">';
			
			foreach($arr['htmx'] as $cap) 
			{
				$this->caption.= sprintf('<li class="mr-6">%s</li>',$cap['title']);
			}
			
			$this->caption.= '</ul>';
			
			unset($arr['htmx']);
		}
		
		
		if(isset($arr['pagination']))
		{
			
			// foreach($arr['pagination'] as $kpg -> $pg)
			// {
			// 	 $this->pagination->$kpg =  $pg;
			// }
			
			 $this->pagination =  $arr['pagination'];
			// $this->pagination = Pagination::factory( $arr['pagination']);	
			unset($arr['pagination']);
			
		}
		
	

		// $themeChosen = 1;

		$theme[1]['tb-bg-color']	= 'bg-gray-600';
		$theme[1]['tb-txt-color'] = 'text-white';
		
		$theme[1]['th-bg-color']	= 'bg-gray-700';
		$theme[1]['th-txt-color'] = 'text-gray-100';
		
		$theme[1]['tbody-bg-color']	= 'bg-gray-700';
		$theme[1]['tbody-txt-color'] = 'text-gray-100';
		
	
		$theme[2]['tb-bg-color']	= 'bg-white';
		$theme[2]['tb-txt-color'] = 'text-gray-900';
		
		$theme[2]['th-bg-color']	= 'bg-gray-100';
		$theme[2]['th-txt-color'] = 'text-gray-700';
		
		$theme[2]['tbody-bg-color']	= 'bg-gray-100';
		$theme[2]['tbody-txt-color'] = 'text-gray-700';
		
		
		$theme[3]['tb-bg-color']	= 'bg-dark';
		$theme[3]['tb-txt-color'] = 'text-gray-900';
		
		$theme[3]['th-bg-color']	= 'bg-green-900';
		$theme[3]['th-txt-color'] = 'text-green-100';
		
		$theme[3]['tbody-bg-color']	= 'bg-green-100';
		$theme[3]['tbody-txt-color'] = 'text-green-700';	
		
		$themeChosen = $this->theme;

	
	  
		
		$this->table=$menu.$this->menu;
		
		if(!$arr)
		{
			 return $this->caption ;
		}
		// $this->table.= sprintf('<table   border="1" class="table tablesorter  %s %s border-separate space-y-6 text-sm">', $theme[$themeChosen]['tb-bg-color'], $theme[$themeChosen]['tb-txt-color'], $theme[$themeChosen]['tb-txt-color']    );
		
		$this->table.= sprintf('<table id="tb"   border="1" class="table tablesorter  %s %s border-separate space-y-6 text-sm"><caption class="%s mt-20 py-10 px-10">%s</caption> ', $theme[$themeChosen]['tb-bg-color'], $theme[$themeChosen]['tb-txt-color'], $theme[$themeChosen]['tb-txt-color'],''    );


		$thead = $arr[0];
		
		/// THEAD
		$this->table.=sprintf('<thead class="%s %s">', $theme[$themeChosen]['th-bg-color'], $theme[$themeChosen]['th-txt-color']   );
		$this->table.= '<tr>';
		$this->table.= '<th>#</th>';
		
		$colCounter=0;
		foreach( $thead as $key => $value)
		{
			if(substr($key,0,1)=='_' and $key<>'_ISBN') continue;
			$colCounter++;
			
			list($key) = self::head($key);	
			
			$this->table.= $key;
		}
		
		$this->table.= '</tr>';
		$this->table.='</thead>';
		
		/// TBODY
		$this->table.=sprintf('<tbody class="%s %s font-snall">', $theme[$themeChosen]['tbody-bg-color'], $theme[$themeChosen]['tbody-txt-color']   );
		
		$accum = 0;
		
		$row = 0;
		foreach( $arr as $key => $value)
		{
			$row++;
			$this->table.= '<tr>';
			$this->table.=sprintf('<td class="">%s</td>',$row+$this->pagination->offset);
			// self::tdfy($key,$value,$k,$v);	
			
			foreach( $value as $k => $v)
			{
			
			$sub=false;
			
			if($k=='Sub')  $sub= true;
				
			$class = '';
			
			list($td,$show) = self::tdfy($key,$value,$k,$v);		
		    
		    
		    
			if($show == false ) continue;

			// if($k=='Modelo' and isset( $value['_ISBN'])) 
			// 	$v=sprintf('<a class="nav-link winbox-iframe pl-4 cursor-pointer" rel="/K3/tip/vproduct/%s/" title="%s"> %s </a>', $value['_ISBN'], $v, $v );
			
			$this->table.= $td;
			
			if($sub==true) 
			{
				$accum += $v;
				// $this->table.= '<th  class="p-3">'.number_format($accum,0,',','.').'</th>';
			}
			
		}
			
			$this->table.= '</tr>';
		}

		$this->table.='</tbody>';
		// $this->table.= sprintf('<tfoot><tr><td colspan=%s>', $colCounter-1) ;
		// if($this->qty > 0) $this->table.= sprintf('<td colspan=b1><span class="text-right">%s</span></td>', $this->qty) ;
		// if($this->subtotal>0) $this->table.= sprintf('<td colspan=b1><span class="text-right">%s</span></td>', $this->subtotal) ;
		// $this->table.= sprintf('</tr></tfoot>') ;
		$this->table.='</table> ';
// 		$this->table.='<div class="pager"> 
//         <img src="https://mottie.github.io/tablesorter/addons/pager/icons/first.png" class="first"/> 
//         <img src="https://mottie.github.io/tablesorter/addons/pager/icons/prev.png" class="prev"/> 
//         <span class="pagedisplay"></span>
//         <img src="https://mottie.github.io/tablesorter/addons/pager/icons/next.png" class="next"/> 
//         <img src="https://mottie.github.io/tablesorter/addons/pager/icons/last.png" class="last"/> 
//         <select class="pagesize" title="Select page size"> 
//             <option selected="selected" value="10">10</option> 
//             <option value="20">20</option> 
//             <option value="30">30</option> 
//             <option value="all">All</option> 
//         </select>
//         <select class="gotoPage" title="Select page number"></select>
// </div>';


		// $this->table.= $this->pagination;
		// echo $this->table.='';
		return $this->table;
		// $this->response->body($this->table);
		
	}

	function head($key)
	{
		$sub= false;
		 
		$exp = preg_split("/[\.]+/", $key);
        
        $field = $exp[0];
        
		$fieldName=  ( preg_replace(["/([A-Z]+)/", "/_([A-Z]+)([A-Z][a-z])/"], ["<br>$1", "_$1_$2"], ($field) ) );
		
		if($key=='_ISBN') $fieldName='Imagem';
		
		$nkey = '<th  class="p-3">'.$fieldName.'</th>';	
		
		if($field=='Sub')  $sub= true;
			
		// if($sub==true) $nkey.= '<th  class="p-3">Acumu<BR>Lado</th>';	
		
		return array ($nkey);
	}


	function recursive($arr)
	{
		  //return "";
		
		  if(!isset($arr[0]))  return "";
		
		 $tbl='<table border="1" class="table border-separate space-y-6 text-sm">';
		 $tbl.='<thead>';
		 
		 
		 foreach($arr[0] as $title => $var)
			{
					$tbl.='<th>'.$title.'</th>';
			}
			$tbl.='</thead>';		
			
		 foreach($arr as $key => $val)
		 {
		    if(is_array($val))
		    {
		    		$tbl.='<tr>';		
				 	  foreach($val as $k => $v)
						{
									if(is_array($v))
									{
						 				$tbl.='<td>'.implode('-',$v).'</td>';
									}else{
									    $tbl.='<td>'.$v.'</td>';
									}
						}
						$tbl.='</tr>';
		    }
		 }
		 
	
		 
		 $tbl.='</table>';
		 
		 return $tbl;
		
	}
	
	function tdfy($key,$value,$k,$v)
	{
		 if($k=='Subtotal') $this->subtotal+=$v;
		 if($k=='QTD') $this->qty+=$v;
		// if(is_array($v)) return array ('<td>array</td>', true);
		
		if(is_array($v))
		{ 
			  $str = self::recursive($v);
			  $td = sprintf('<td>%s</td>',$str);
			  return array ($td, true);
			  
			 // $nv = implode(',', array_map('implode', $v, array_fill(0, count($v), '')));
			 // $td = sprintf('<td>%s</td>','$str');
				// return array ($td, $show);
		}
		
		
		$class = '';
		$color = '';
		$rule = '';
		$editable = '';
		$format =  '';
		$comma = '.';
		$separator = ',';
		$decimals =  0;
		$posfix = '';
		$show = true;
		
		// if(is_numeric($v)) $class.=' text-right';
		
		$nv = $v;
		
	
		if(isset($this->format[$k]) )
		{
			$format = $this->format[$k];
		}
		
		
		$span_color='';
		
		if(isset($this->color[$k]) and 	!is_null($v))
		{
			
			if(isset($this->color[$k][0]) )
			{
				 //s($this->color[$k]);
				 foreach($this->color[$k] as $kc => $cv)
				 {
				 	
				 	$color = $cv['class'];
					$rule  = $cv['rule'];
					$rules= explode(' ', trim($rule));
					
					if(count($rules)==2)
					{
						
						eval(" if($v $rule) { \$span_color = '$color' ; } ") ;	
					}
					
					if(count($rules)==3  )
					{
						if(is_numeric($rules[2]) )
						{
							$code= $value[$rules[0]].$rules[1].$rules[2];
						}else{
							$code= $value[$rules[0]].$rules[1].$value[$rules[2]];
						}
						eval(" if($code) { \$span_color = '$color' ; } ") ;	
						
					}

				 
				 }
			// s($rule);

			}else{
				
				$color = $this->color[$k]['class'];
				
				
		
				if(isset($this->color[$k]['rule'])) 
				{
					$rule  = $this->color[$k]['rule'];

			
					if(!empty($rule) )
					{
						$rules= explode(' ', trim($rule));
						
						if(count($rules)==2)
						{
							eval(" if($v $rule) { \$span_color = '$color' ; } ") ;	
						}
						
						if(count($rules)==3)
						{
							// s($rules);
							$code= $value[$rules[0]].$rules[1].$value[$rules[2]];
							eval(" if($code) { \$span_color = '$color' ; } ") ;	
						}
					}
				}else{
						eval("  \$span_color = '$color' ;  ") ;	
				}	
		 	 //$php = " $v $rule ";
		 	 //eval("  if($v $rule) { echo\"$php\"; } ") ;
		 	 
		 	 
		 	// / if( $r == true ) $span_color = $color ;
		 	
		 }

		}
		
		

		if(isset($this->editable[$k]) )
		{
			$edtExp = explode('|',$this->editable[$k]);
			$edtExp[3] = $value[$edtExp[3]];
			$edt =  implode('|',$edtExp);
			$editable = sprintf(" id='%s' ",$edt);
			$class.=  'editable text-green-500';
		}
		
		// <td class="pl-4 cursor-pointer">
  //                              <div class="flex items-center">
  //                                  <div class="w-10 h-10">
  //                                      <img class="w-full h-full" src="https://cdn.tuk.dev/assets/templates/olympus/projects.png" alt="UX Design and Visual Strategy">
  //                                  </div>
  //                                  <div class="pl-4">
  //                                      <p class="font-medium">UX Design &amp; Visual Strategy</p>
  //                                      <p class="text-xs leading-3 text-gray-600 pt-2">Herman Group</p>
  //                                  </div>
  //                              </div>
  //                          </td>
                            
                            
                            
		if(isset( $value['_ISBN']) and $k=='_ISBN') 
		{
			$nv=sprintf('<img class="rounded-full h-30 w-30  object-cover" src="https://img.rolemak.com.br/id/w150/%s.jpg"/>', $value['_ISBN'] );
		 }else{
			
			if(substr($k,0,1)=='_')  $show = false;  // hide field starting with _
		}	
		
		if(isset( $value['Marca']) and $k=='Marca') 
		{
			// https://cdn.rolemak.com.br/svg/marca/zoje.svg?version=7.73
			if($value['Marca']<>'')
			$nv=sprintf('<img class="h-6 " src="https://cdn.rolemak.com.br/svg/marca/%s.svg?version=7.73"/>', str_replace(' ','-',strtolower($value['Marca'])) );
		}	
			
		if(isset( $value['Img']) and $k=='Img') 
		{
			// https://cdn.rolemak.com.br/svg/marca/zoje.svg?version=7.73
			$nv=sprintf('<img class="rounded-full h-20   object-cover" src="%s"/>', $value['Img'] );
		}		
			
	
		if($format <> '' ) 
		{
			$class.=  ' text-right';
				
			$decimals =  substr($format,0,1);
				
			if(substr($format,1,2) == 'br' )
			{
				$comma = ',';
				$separator = '.';
			}
			
			if(substr($format,1,2) == 'kb' and $v > 0)
			{
				$posfix = 'K';
				$v = $v/1000;
				$comma = ',';
				$separator = '.';
			}
		
			// s($v,$decimals,$comma,$separator);
			$nv = number_format($v,$decimals,$comma,$separator).$posfix;
			
		
		 }else{
		 	if(is_numeric($v)) $class.=' text-right';
		 }
		 
		 if($k=='Modelo' and isset( $value['_ISBN']))
		 {
				$nv=sprintf('<a  color="#FFA500" width="60%%" height="100%%" class="nav-link winbox-iframe cursor-pointer" rel="/K3/tip/vproduct/%s/" title="%s">%s</a>', $value['_ISBN'], $v, $v );
		 }
				
		 if($k=='Cliente' and isset( $value['_IDCLI']))
		 {
				$nv=sprintf('<a  color="#FFA500"  width="90%%" height="90%%"  class="nav-link winbox-iframe cursor-pointer" rel="/K3/tip/vcustomer/%s/" title="%s">%s</a>', $value['_IDCLI'], $v, ucwords($v) );
		 }
		 
		 if($k=='Seller' and isset( $value['Seller']))
		 {
				$nv=sprintf('<a class="text-blue-600 cursor-pointer" href="%s" target="_new" title="%s">Ver</a>', $value['Seller'], $v );
		 }
		 
		 if( $k=='Anúncio' and isset( $value['Anúncio']) )
		 {
				$nv=sprintf('<a class="text-blue-600 cursor-pointer" href="%s" target="_new" title="%s">Ver</a>', $value['Anúncio'], $v );
		 }
				
		 if($k=='Pedido')
		 {
				// $nv=sprintf('<a class="nav-link winbox-iframe cursor-pointer" rel="/K3/tip/order/%s/" title="%s">%s</a>', $value['Pedido'], $v, $v );				
				$nv=sprintf('<a class="nav-link winbox-iframe cursor-pointer" rel="/crm/v4/orders/order/%s" title="%s">%s</a>', base64_encode($value['Pedido']), $v, $v );				
		 }
		 
		 	 if($k=='IDPAG' and isset( $value['IDPAG']))
		 {
				$nv=sprintf('<a  color="#FFA500" width="30%%" height="30%%" class="nav-link winbox-iframe cursor-pointer" rel="/sage/tip/payment/%s/" title="%s">%s</a>', $value['IDPAG'], $v, $v );
		 }
		 
		 	 if($k=='Fornecedor' and isset( $value['_IDFORN']))
		 {
				$nv=sprintf('<a  color="#FFA500" width="30%%" height="30%%" class="nav-link winbox-iframe cursor-pointer" rel="/sage/tip/supplier/%s/" title="%s">%s</a>', $value['_IDFORN'], $v, $v );
		 }
			
		 if($k=='MercadoLivre' and isset( $value['_ISBN']))
		 {
			
                                        
				$nv=sprintf('<button class="bg-green-900 text-white rounded px-5 py-5 mx-10 my-10" hx-swap="innerHTML" _="on click fetch /metrics/mercadolivre/index/%s
                                        put the result into me then transition opacity to 1
                                        wait 1s then transition opacity to 0
                                        put \'Atualizado Me!\' into me then transition opacity to 1" style="opacity: 1;">Ver ML</button> ', $value['_ISBN'] );
                                        
				$nv.=sprintf('<button class="bg-gray-900 text-white rounded px-5 py-5 mx-10 my-10" hx-swap="innerHTML" _="on click fetch /nodered/mercadolivre/edit?id=%s&field=price&value=%s
                                        put the result into me then transition opacity to 1
                                        wait 1s then transition opacity to 0
                                        put \'Atualizado Me!\' into me then transition opacity to 1" style="opacity: 1;">Atualizar ML</button> ', $value['_ISBN'], $v  );                                        
                                        
                                        
                                        // https://office.vallery.com.br/metrics/mercadolivre/index/100476
		 }	
				
		// $string = 'taça';
		// $name = 'café';
		// $str = 'Esta é uma $string com o meu $name nela.';
		// echo $str. "\n";
		// eval("\$str = \"$str\";");
		// echo $str . "\n";

		
		// s($nv);
		 //if($nv==0) $nv='-';
		 if($nv=="0") $nv='';
		 
		 $td = sprintf('<td style="max-width:400px;"  %s class="%s "><span class="%s" >%s</span></td>',
						$editable, 
						$class,
						$span_color, 
						$nv);
		 
		 
		
		return array ($td,$show);
	}

	function xplode($char,$str)
	{
		$expl =  explode($char,$str);
		
		return $expl;
	}

} 
