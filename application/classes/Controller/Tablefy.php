<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Tablefy extends Controller {

	public function tablefy($arr)
	{
		// s($arr[0]);
		// s(count($arr[0]));
		$this->subtotal=0;
		$this->qty=0;
		$this->pagination = Pagination::factory(array(
				'total_items'    => 100,
				'items_per_page' => 10,
				// 'current_page' => $arr['pagination']->current_page,
				// 'current_first_item' => 2,
				// 'current_last_item' => 2,
				// 'first_page' => 2,
				// 'next_page' => 2,
				// 'offset' => 2,

			));

		$this->theme = 1;
		// $this->pagination->offset;


		// $arr = $this->request->post('array');

		if(empty($arr)) return '';

		$this->sum_fields=[];
		if(isset($arr['sum']))
		{
			$this->sum_fields =  $arr['sum'];
			unset($arr['sum']);
			// s($this->sum_fields);
		}

		if(isset($arr['format']))
		{
			$this->format =  $arr['format'];
			unset($arr['format']);
		}

		if(isset($arr['color']))
		{
			$this->color =  $arr['color'];
			unset($arr['color']);
		}

		if(isset($arr['editable']))
		{
			$this->editable =  $arr['editable'];
			unset($arr['editable']);
		}

		if(isset($arr['theme']))
		{
			$this->theme =  $arr['theme'];
			unset($arr['theme']);
		}

			$this->menu='';
		if(isset($arr['menu']))
		{
			$this->menu =  $arr['menu'];
			unset($arr['menu']);
		}


		$menu='';
		$this->caption ="Tabela de dados";
				if(isset($arr['caption']))
		{

			$menu = '
				<nav class="bg-white px-2 sm:px-4 py-2.5 dark:bg-gray-900  w-full z-20 top-0 left-0 border-b border-gray-200 dark:border-gray-600">
					  <div class="lg:flex lg:items-center lg:justify-between">
  						<div class="min-w-0 flex-1">
    						<h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">Resultado</h2>
  						</div>
  					</div>

					  <div class=" flex flex-wrap justify-between items-center">
					     <div class="hidden justify-between items-center w-full md:flex md:w-auto md:order-1" id="navbar-sticky">
					      <ul class="flex flex-col flex-wrap  p-4 mt-4 bg-gray-50 rounded-lg border border-gray-100 md:flex-row md:space-x-4 md:mt-0 md:text-sm md:font-medium md:border-0 md:bg-white dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700">';

			foreach($arr['caption'] as $cap)
			{
				$menu.= sprintf('<li><a href="%s" class="block py-2 pr-1 pl-1 text-dark bg-blue-700 rounded md:bg-transparent md:text-blue-700 md:p-0 dark:text-white" aria-current="page">%s</a></li>', $cap['link'],$cap['title']);
			}

			$menu.= '</ul> </div> </div></nav>';

			 unset($arr['caption']);
		}
		if(isset($arr['caption']))
		{


			$this->caption = '<ul class="flex">';

			foreach($arr['caption'] as $cap)
			{
				$this->caption.= sprintf('<li class="mr-6"><a class="text-blue-500 hover:text-blue-800" href="%s">%s</a></li>', $cap['link'],$cap['title']);
			}

			$this->caption.= '</ul>';

			unset($arr['caption']);
		}


		if(isset($arr['htmx']))
		{

			$this->caption = '<ul class="flex">';

			foreach($arr['htmx'] as $cap)
			{
				$this->caption.= sprintf('<li class="mr-6">%s</li>',$cap['title']);
			}

			$this->caption.= '</ul>';

			unset($arr['htmx']);
		}


		if(isset($arr['pagination']))
		{

			// foreach($arr['pagination'] as $kpg -> $pg)
			// {
			// 	 $this->pagination->$kpg =  $pg;
			// }

			 $this->pagination =  $arr['pagination'];
			// $this->pagination = Pagination::factory( $arr['pagination']);
			unset($arr['pagination']);

		}



		// $themeChosen = 1;

		$theme[1]['tb-bg-color']	= 'bg-gray-600';
		$theme[1]['tb-txt-color'] = 'text-white';

		$theme[1]['th-bg-color']	= 'bg-gray-700';
		$theme[1]['th-txt-color'] = 'text-gray-100  text-xs';

		$theme[1]['tbody-bg-color']	= 'bg-gray-100';
		$theme[1]['tbody-txt-color'] = 'text-gray-700 text-xs';


		$theme[2]['tb-bg-color']	= 'bg-white';
		$theme[2]['tb-txt-color'] = 'text-gray-900';

		$theme[2]['th-bg-color']	= 'bg-gray-100';
		$theme[2]['th-txt-color'] = 'text-gray-700  text-xs';

		$theme[2]['tbody-bg-color']	= 'bg-gray-100';
		$theme[2]['tbody-txt-color'] = 'text-gray-700  text-xs';


		$theme[3]['tb-bg-color']	= 'bg-dark';
		$theme[3]['tb-txt-color'] = 'text-gray-900';

		$theme[3]['th-bg-color']	= 'bg-green-900';
		$theme[3]['th-txt-color'] = 'text-green-100  text-xs';

		$theme[3]['tbody-bg-color']	= 'bg-green-100';
		$theme[3]['tbody-txt-color'] = 'text-green-700  text-xs antialiased tracking-tight';

		$themeChosen = $this->theme;




		$this->table=$menu.$this->menu;

		if(!$arr)
		{
			 return $this->caption ;
		}
		// $this->table.= sprintf('<table   border="1" class="table tablesorter  %s %s border-separate space-y-6 text-sm">', $theme[$themeChosen]['tb-bg-color'], $theme[$themeChosen]['tb-txt-color'], $theme[$themeChosen]['tb-txt-color']    );

		$this->table.= sprintf('<table id="tb"   border="1" class="table tablesorter  %s %s border-separate space-y-6 text-sm"><caption class="%s mt-20 py-10 px-10">%s</caption> ', $theme[$themeChosen]['tb-bg-color'], $theme[$themeChosen]['tb-txt-color'], $theme[$themeChosen]['tb-txt-color'],''    );



		$thead = $arr[0];

		/// THEAD
		$this->table.=sprintf('<thead class="%s %s">', $theme[$themeChosen]['th-bg-color'], $theme[$themeChosen]['th-txt-color']   );
		$this->table.= '<tr>';
		$this->table.= '<th>#</th>';

		$colCounter=0;
		foreach( $thead as $key => $value)
		{
			if(substr($key,0,1)=='_' and $key<>'_ISBN') continue;
			$colCounter++;

			list($key) = self::head($key);

			$this->table.= $key;
		}

		$this->table.= '</tr>';
		$this->table.='</thead>';

		/// TBODY
		$this->table.=sprintf('<tbody class="%s %s font-snall">', $theme[$themeChosen]['tbody-bg-color'], $theme[$themeChosen]['tbody-txt-color']   );

		$accum = 0;

		$row = 0;
		$columnSums = array(); // Array para armazenar somas das colunas

		foreach( $arr as $key => $value)
		{
			$row++;
			$this->table.= '<tr>';
			$this->table.=sprintf('<td class="">%s</td>',$row+$this->pagination->offset);
			// self::tdfy($key,$value,$k,$v);

			$colCounter=1;
			foreach( $value as $k => $v)
			{
				// if(substr($k,0,1)=='_' and $k<>'_ISBN') continue;
					// s($k);
				$colCounter++;

				// $sub=false;

				if($k=='Sub')  $sub= true;

				$class = '';

				list($td,$show) = self::tdfy($key,$value,$k,$v,$colCounter);

				if($show == false ) continue;

				// Acumular somas para campos que começam com 'rv' ou 'stk'
				$fieldLower = strtolower($k);
				if (substr($fieldLower, 0, 2) === 'rv' || substr($fieldLower, 0, 3) === 'stk') {
					// Limpar valor numérico (remover formatação)
					$numericValue = floatval(preg_replace('/[^0-9.-]/', '', $v));
					if (!isset($columnSums[$k])) {
						$columnSums[$k] = 0;
					}
					$columnSums[$k] += $numericValue;
				}

				// if($k=='Modelo' and isset( $value['_ISBN']))
				// 	$v=sprintf('<a class="nav-link winbox-iframe pl-1 cursor-pointer" rel="/K3/tip/vproduct/%s/" title="%s"> %s </a>', $value['_ISBN'], $v, $v );

				$this->table.= $td;

				// if($sub==true)
				// {
				// 	$accum += $v;
				// 	// $this->table.= '<th  class="p-3">'.number_format($accum,0,',','.').'</th>';
				// }



			}

			$this->table.= '</tr>';

		}



		$this->table.='</tbody>';
		 //s($this->foot_values) ;

		// Adicionar rodapé com somas dos campos RV e STK
		if (!empty($columnSums)) {
			$this->table.='<tfoot>';
			$this->table.='<tr>';
			$this->table.='<td class="text-right"><span class="text-teal-700 text-xl font-bold">TOTAL:</span></td>';

			// Obter cabeçalhos das colunas para alinhar com as somas
			$headers = array_keys($arr[0]);

			foreach ($headers as $header) {
				if (isset($columnSums[$header])) {
					// Aplicar formatação baseada no tipo de campo
					$formattedSum = number_format($columnSums[$header], 2, ',', '.');

					// Aplicar cores baseadas no prefixo
					$headerLower = strtolower($header);
					$colorClass = 'text-teal-700';
					if (substr($headerLower, 0, 2) === 'rv') {
						$colorClass = 'text-blue-700 bg-blue-100';
					} elseif (substr($headerLower, 0, 3) === 'stk') {
						$colorClass = 'text-yellow-700 bg-yellow-100';
					}

					$this->table.= sprintf('<td class="text-right"><span class="%s text-xl font-bold">%s</span></td>',
						$colorClass, $formattedSum);
				} else {
					$this->table.='<td class="text-right"><span class="text-gray-400">-</span></td>';
				}
			}

			$this->table.='</tr>';
			$this->table.='</tfoot>';
		} else {
			// Rodapé original se não houver somas
			$this->table.='<tfoot>';
			$this->table.='<td class=" text-right"><span class=" text-teal-700 text-xl font-bold "></span</td>';
				$this->table.='<td class=" text-right"><span class=" text-teal-700 text-xl font-bold "></span</td>';
			foreach( $this->foot_values as $key => $arr)
			{

				// if($arr['value']==0) $value='';
				$this->table.= sprintf('<td class=" text-right"><span class=" text-teal-700 text-xl font-bold ">%s</span</td>',
				$this->format($arr['title'],$arr['value']) );
			}
			$this->table.='</tfoot>';
		}

		$this->table.='</table> ';

		// Adicionar indicador HTMX
		$this->table.= '<div class="htmx-indicator" style="display:none;">Carregando...</div>';

		// Adicionar JavaScript para ordenação da tabela
		$this->table.= '
		<script>
		let sortDirection = {};

		function sortTable(headerElement, fieldName) {
			const table = document.getElementById("tb");
			const tbody = table.querySelector("tbody");
			const rows = Array.from(tbody.querySelectorAll("tr"));

			// Determinar direção da ordenação
			const currentDirection = sortDirection[fieldName] || "asc";
			const newDirection = currentDirection === "asc" ? "desc" : "asc";
			sortDirection[fieldName] = newDirection;

			// Encontrar o índice da coluna
			const headers = Array.from(table.querySelectorAll("thead th"));
			const columnIndex = headers.indexOf(headerElement);

			// Ordenar as linhas
			rows.sort((a, b) => {
				const aValue = getCellValue(a, columnIndex);
				const bValue = getCellValue(b, columnIndex);

				// Verificar se são números
				const aNum = parseFloat(aValue.replace(/[^0-9.-]/g, ""));
				const bNum = parseFloat(bValue.replace(/[^0-9.-]/g, ""));

				let comparison = 0;
				if (!isNaN(aNum) && !isNaN(bNum)) {
					// Comparação numérica
					comparison = aNum - bNum;
				} else {
					// Comparação de string
					comparison = aValue.localeCompare(bValue);
				}

				return newDirection === "asc" ? comparison : -comparison;
			});

			// Reordenar as linhas na tabela
			rows.forEach(row => tbody.appendChild(row));

			// Atualizar ícones de ordenação
			updateSortIcons(headers, headerElement, newDirection);
		}

		function getCellValue(row, columnIndex) {
			const cell = row.cells[columnIndex];
			return cell ? cell.textContent.trim() : "";
		}

		function updateSortIcons(headers, activeHeader, direction) {
			// Resetar todos os ícones
			headers.forEach(header => {
				const icon = header.querySelector("span");
				if (icon) icon.textContent = "⇅";
			});

			// Atualizar ícone do cabeçalho ativo
			const activeIcon = activeHeader.querySelector("span");
			if (activeIcon) {
				activeIcon.textContent = direction === "asc" ? "↑" : "↓";
			}
		}
		</script>';

		// $this->table.= $this->pagination;
		// echo $this->table.='';
		return $this->table;
		// $this->response->body($this->table);

	}

	function format($k,$v)
	{

		if($v==0) return '';
		$class='';
		$format =  '';
		$comma = '.';
		$separator = ',';
		$decimals =  0;
		$posfix = '';
		$show = true;

		// if(is_numeric($v)) $class.=' text-right';

		$nv = $v;


		if(isset($this->format[$k]) )
		{
			$format = $this->format[$k];
		}


		if($format <> '' )
		{
			$class.=  ' text-right';

			$decimals =  substr($format,0,1);

			if(substr($format,1,2) == 'br' )
			{
				$comma = ',';
				$separator = '.';
			}

			if(substr($format,1,2) == 'kb' and $v > 0)
			{
				$posfix = 'K';
				$v = $v/1000;
				$comma = ',';
				$separator = '.';
			}

			if(substr($format,1,2) == 'ms' and $v > 0)
			{
				$posfix = '';
				$v = $v/1000;
				$comma = '.';
				$separator = ',';
			}

			// s($v,$decimals,$comma,$separator);
			$nv = number_format($v,$decimals,$comma,$separator).$posfix;


		 }else{

		 	if(is_numeric($v)) $class.=' text-right';
		 }

		 return $nv;
	}
	function head($key)
	{
		$sub= false;

		$exp = preg_split("/[\.]+/", $key);

        $field = $exp[0];

		$fieldName=  ( preg_replace(["/([A-Z]+)/", "/_([A-Z]+)([A-Z][a-z])/"], ["<br>$1", "_$1_$2"], ($field) ) );

		if($key=='_ISBN') $fieldName='Imagem';

		// Aplicar cores de fundo baseadas no nome do campo
		$headerClass = 'p-3';
		$fieldLower = strtolower($field);

		if (substr($fieldLower, 0, 3) === 'stk') {
			// Fundo amarelo para campos que começam com 'stk' (usando yellow pois orange pode não estar disponível)
			$headerClass .= ' bg-yellow-200 text-yellow-800 font-semibold';
			$fieldName .= '<!-- STK DETECTED: '.$field.' -->';
		} elseif (substr($fieldLower, 0, 2) === 'rv') {
			// Fundo azul para campos que começam com 'rv'
			$headerClass .= ' bg-blue-200 text-blue-800 font-semibold';
			$fieldName .= '<!-- RV DETECTED: '.$field.' -->';
		} elseif (substr($fieldLower, 0, 1) === 'v') {
			// Fundo verde para campos que começam com 'v' (mas não 'rv')
			$headerClass .= ' bg-green-200 text-green-800 font-semibold';
			$fieldName .= '<!-- V DETECTED: '.$field.' -->';
		}

		// Adicionar funcionalidade de ordenação clicável
		$sortableClass = $headerClass . ' cursor-pointer hover:opacity-80 select-none transition-all duration-200';
		$sortIcon = '<span class="ml-1 text-xs opacity-60">⇅</span>'; // Ícone de ordenação

		$nkey = '<th class="'.$sortableClass.'" onclick="sortTable(this, \''.$field.'\')" title="Clique para ordenar por '.$field.'">'.$fieldName.$sortIcon.'</th>';

		if($field=='Sub')  $sub= true;

		// if($sub==true) $nkey.= '<th  class="p-3">Acumu<BR>Lado</th>';

		return array ($nkey);
	}


	function recursive($arr)
	{
		  //return "";

		  if(!isset($arr[0]))  return "";

		 $tbl='<table border="1" class="table border-separate space-y-6 text-sm">';
		 $tbl.='<thead>';


		 foreach($arr[0] as $title => $var)
			{
					$tbl.='<th>'.$title.'</th>';
			}
			$tbl.='</thead>';

		 foreach($arr as $key => $val)
		 {
		    if(is_array($val))
		    {
		    		$tbl.='<tr>';
				 	  foreach($val as $k => $v)
						{
									if(is_array($v))
									{
						 				$tbl.='<td>'.implode('-',$v).'</td>';
									}else{
									    $tbl.='<td>'.$v.'</td>';
									}
						}
						$tbl.='</tr>';
		    }
		 }



		 $tbl.='</table>';

		 return $tbl;

	}

	function tdfy($key,$value,$k,$v,$cf)
	{

		 if(!isset($this->foot_values[$cf]['value']) and substr($k,0,1)<>'_' )
		 {

		 	$this->foot_values[$cf]['title']=$k;
		 	$this->foot_values[$cf]['value']=0;
		 }


		 if(in_array($k,$this->sum_fields) and substr($k,0,1)<>'_' )
		 {
		 	$this->foot_values[$cf]['value']+=$v;
		 }


		//  if($k=='Subtotal') $this->subtotal+=$v;
		//  if($k=='QTD') $this->qty+=$v;
		// if(is_array($v)) return array ('<td>array</td>', true);

		if(is_array($v))
		{
			  $str = self::recursive($v);
			  $td = sprintf('<td>%s</td>',$str);
			  return array ($td, true);

			 // $nv = implode(',', array_map('implode', $v, array_fill(0, count($v), '')));
			 // $td = sprintf('<td>%s</td>','$str');
				// return array ($td, $show);
		}


		$class = '';
		$color = '';
		$rule = '';
		$editable = null;
		$format =  '';
		$comma = '.';
		$separator = ',';
		$decimals =  0;
		$posfix = '';
		$show = true;

		// if(is_numeric($v)) $class.=' text-right';

		$nv = $v;


		if(isset($this->format[$k]) )
		{
			$format = $this->format[$k];
		}


		$span_color='';

		if(isset($this->color[$k]) and 	!is_null($v))
		{

			if(isset($this->color[$k][0]) )
			{
				 //s($this->color[$k]);
				 foreach($this->color[$k] as $kc => $cv)
				 {

				 	$color = $cv['class'];
					$rule  = $cv['rule'];
					$rules= explode(' ', trim($rule));

					if(count($rules)==2)
					{

						eval(" if($v $rule) { \$span_color = '$color' ; } ") ;
					}

					if(count($rules)==3  )
					{
						if(is_numeric($rules[2]) )
						{
							$code= $value[$rules[0]].$rules[1].$rules[2];
						}else{
							$code= $value[$rules[0]].$rules[1].$value[$rules[2]];
						}
						eval(" if($code) { \$span_color = '$color' ; } ") ;

					}


				 }
			// s($rule);

			}else{

				$color = $this->color[$k]['class'];



				if(isset($this->color[$k]['rule']))
				{
					$rule  = $this->color[$k]['rule'];


					if(!empty($rule) )
					{
						$rules= explode(' ', trim($rule));

						if(count($rules)==2)
						{
							eval(" if($v $rule) { \$span_color = '$color' ; } ") ;
						}

						if(count($rules)==3)
						{
							// s($rules);
							$code= $value[$rules[0]].$rules[1].$value[$rules[2]];
							eval(" if($code) { \$span_color = '$color' ; } ") ;
						}
					}
				}else{
						eval("  \$span_color = '$color' ;  ") ;
				}
		 	 //$php = " $v $rule ";
		 	 //eval("  if($v $rule) { echo\"$php\"; } ") ;


		 	// / if( $r == true ) $span_color = $color ;

		 }

		}



		if(isset($this->editable[$k]) )
		{
			$edtExp = explode('|',$this->editable[$k]);
			$edtExp[3] = $value[$edtExp[3]];
			$edt =  implode('|',$edtExp);
			$editable = sprintf(" id='%s' ",$edt);
			$class.=  'editable text-green-500';
		}

		// <td class="pl-1 cursor-pointer">
  //                              <div class="flex items-center">
  //                                  <div class="w-10 h-10">
  //                                      <img class="w-full h-full" src="https://cdn.tuk.dev/assets/templates/olympus/projects.png" alt="UX Design and Visual Strategy">
  //                                  </div>
  //                                  <div class="pl-1">
  //                                      <p class="font-medium">UX Design &amp; Visual Strategy</p>
  //                                      <p class="text-xs leading-3 text-gray-600 pt-2">Herman Group</p>
  //                                  </div>
  //                              </div>
  //                          </td>



		if(isset( $value['_ISBN']) and $k=='_ISBN')
		{
			$nv=sprintf('<img class="rounded-full h-30 w-30  object-cover" src="https://img.rolemak.com.br/id/w64/%s.jpg"/>', $value['_ISBN'] );

		 }else{

			if(substr($k,0,1)=='_')  $show = false;  // hide field starting with _
		}

		if(isset( $value['Marca']) and $k=='Marca')
		{
			// https://cdn.rolemak.com.br/svg/marca/zoje.svg?version=7.73
			if($value['Marca']<>'')
			$nv=sprintf('<img class="h-6 " src="https://cdn.rolemak.com.br/svg/marca/%s.svg?version=7.73"/>', str_replace(' ','-',strtolower($value['Marca'])) );
		}

		if(isset( $value['Img']) and $k=='Img')
		{
			// https://cdn.rolemak.com.br/svg/marca/zoje.svg?version=7.73
			$nv=sprintf('<img class="rounded-full h-20   object-cover" src="%s"/>', $value['Img'] );
		}


		if($format <> '' )
		{
			$class.=  ' text-right';

			$decimals =  substr($format,0,1);

			if(substr($format,1,2) == 'br' )
			{
				$comma = ',';
				$separator = '.';
			}

			if(substr($format,1,2) == 'kb' and $v > 0)
			{
				$posfix = 'K';
				$v = $v/1000;
				$comma = ',';
				$separator = '.';
			}

			if(substr($format,1,2) == 'ms' and $v > 0)
			{
				$posfix = '';
				$v = $v/1000;
				$comma = '.';
				$separator = ',';
			}

			// s($v,$decimals,$comma,$separator);
			$nv = number_format($v,$decimals,$comma,$separator).$posfix;


		 }else{
		 	if(is_numeric($v)) $class.=' text-right';
		 }




		 if($k=='Modelo' and isset( $value['_ISBN']))
		 {
				$nv=sprintf('<a  color="#FFA500" width="60%%" height="100%%" class="nav-link winbox-iframe cursor-pointer" rel="/K3/tip/vproduct/%s/" title="%s">%s</a>', $value['_ISBN'], $v, $v );
		 }

		 if($k=='Cliente' and isset( $value['_IDCLI']))
		 {
				$nv=sprintf('<a  color="#FFA500"  width="90%%" height="90%%"  class="nav-link winbox-iframe cursor-pointer" rel="/K3/tip/vcustomer/%s/" title="%s">%s</a>', $value['_IDCLI'], $v, ucwords($v) );
		 }

		 if($k=='Seller' and isset( $value['Seller']))
		 {
				$nv=sprintf('<a class="text-blue-600 cursor-pointer" href="%s" target="_new" title="%s">Ver</a>', $value['Seller'], $v );
		 }

		 if( $k=='Anúncio' and isset( $value['Anúncio']) )
		 {
				$nv=sprintf('<a class="text-blue-600 cursor-pointer" href="%s" target="_new" title="%s">Ver</a>', $value['Anúncio'], $v );
		 }

		 if($k=='Pedido')
		 {
				// $nv=sprintf('<a class="nav-link winbox-iframe cursor-pointer" rel="/K3/tip/order/%s/" title="%s">%s</a>', $value['Pedido'], $v, $v );
				$nv=sprintf('<a class="nav-link winbox-iframe cursor-pointer" rel="/crm/v4/orders/order/%s" title="%s">%s</a>', base64_encode($value['Pedido']), $v, $v );
		 }

		 	 if($k=='IDPAG' and isset( $value['IDPAG']))
		 {
				$nv=sprintf('<a  color="#FFA500" width="30%%" height="30%%" class="nav-link winbox-iframe cursor-pointer" rel="/sage/tip/payment/%s/" title="%s">%s</a>', $value['IDPAG'], $v, $v );
		 }

		 	 if($k=='Fornecedor' and isset( $value['_IDFORN']))
		 {
				$nv=sprintf('<a  color="#FFA500" width="30%%" height="30%%" class="nav-link winbox-iframe cursor-pointer" rel="/sage/tip/supplier/%s/" title="%s">%s</a>', $value['_IDFORN'], $v, $v );
		 }

		 if($k=='MercadoLivre' and isset( $value['_ISBN']))
		 {


				$nv=sprintf('<button class="bg-green-900 text-white rounded px-5 py-5 mx-10 my-10" hx-swap="innerHTML" _="on click fetch /metrics/mercadolivre/index/%s
                                        put the result into me then transition opacity to 1
                                        wait 1s then transition opacity to 0
                                        put \'Atualizado Me!\' into me then transition opacity to 1" style="opacity: 1;">Ver ML</button> ', $value['_ISBN'] );

				$nv.=sprintf('<button class="bg-gray-900 text-white rounded px-5 py-5 mx-10 my-10" hx-swap="innerHTML" _="on click fetch /nodered/mercadolivre/edit?id=%s&field=price&value=%s
                                        put the result into me then transition opacity to 1
                                        wait 1s then transition opacity to 0
                                        put \'Atualizado Me!\' into me then transition opacity to 1" style="opacity: 1;">Atualizar ML</button> ', $value['_ISBN'], $v  );


                                        // https://office.vallery.com.br/metrics/mercadolivre/index/100476
		 }


		if($k=='Ações' and isset( $value['_ISBN']))
		 {

			 $nv=sprintf(' <button hx-get="/K3/produto/cost/%s/"
							    hx-trigger="click"

							>custo</button>',$value['_ISBN'] );

			 $nv.=sprintf(' <button hx-get="/K3/produto/manager/%s/"
							    hx-trigger="click"

							>mng</button>',$value['_ISBN'] );

			 $nv.=sprintf(' <button hx-get="/purchase/history/tip/%s/"
							    hx-trigger="click"

							>history</button>',$value['_ISBN'] );

			 $nv.=sprintf(' <button hx-get="/metrics/prices/pricing/%s/"
				    hx-trigger="click"

				>cost2</button>',$value['_ISBN'] );
		 }

		  if($k=='Revenda' and isset( $value['_ISBN']) and (isset($value['EstoqueTotal']) and $value['EstoqueTotal']<1 and $value['Next']<1) )
		 {

			 $nv=sprintf(' %s <button
								class="btn danger"
								hx-post="/K3/global/crud/update/"
								hx-vals=\'{"id":"mak.inv|revenda|id|%s","value":"0"}\'
								hx-target="closest tr"
								hx-swap="outerHTML swap:0.1s">Zerar</button>', $v,$value['_ISBN'], $v );
		 }

		  if(isset($value['UltimoFob']) and $k=='Fob' and isset( $value['_ISBN']) and $value['UltimoFob']<>$v  and $value['UltimoFob']>0)
		 {

			 $nv=sprintf(' %s <button
								class="btn danger"
								hx-post="/K3/global/crud/update/"
								hx-vals=\'{"id":"mak.inv|fob|id|%s","value":"%s"}\'
								hx-target="closest td"
							>set</button>', $v,$value['_ISBN'], $value['UltimoFob'] );
		 }
		// s($nv);
		 //if($nv==0) $nv='-';
		 if($nv=="0") $nv='';

		 $td = sprintf('<td style="max-width:400px;"   class=" "><div %s class="%s %s" >%s</div></td>',

						$editable,
							$class,
						$span_color,
						$nv);



		return array ($td,$show);
	}

	function xplode($char,$str)
	{
		$expl =  explode($char,$str);

		return $expl;
	}





}
