<?php
class Controller_Tablerize extends Controller
{
    
    public function action_index()
	{
		$arr = Request::current()->post('array');
		$this->table='<table border="1">';

		$thead = $arr[0];
		$this->table.='<thead>';
			$this->table.= '<tr>';
		foreach( $thead as $key => $value)
		{
				$this->table.= '<th>'.$key.'</th>';			
		
		}
			$this->table.= '</tr>';
		$this->table.='</thead>';
		
		foreach( $arr as $key => $value)
		{
			$this->table.= '<tr>';
			foreach( $value as $k => $v)
			{
				$this->table.= '<td>'.htmlspecialchars($v).'</td>';			
				
			}
			$this->table.= '</tr>';
		}

		echo $this->table.='</table>';
		
		 $this->view = View::factory('tablerize/tablerize')	
		 							->set('array', Request::current()->post('array') )	
										  ;	
	
		$this->response->body($this->view);
	}
}