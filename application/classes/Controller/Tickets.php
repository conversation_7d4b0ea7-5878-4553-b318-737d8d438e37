<?php
class Controller_Tickets extends Controller_Websession
{

    public function action_today()
    {
        $this->auto_render = false;

        $where = null;
        $total = 0;

        $response = array();

        if ( $this->request->param('division') )
        {
            $where.=sprintf(" AND u.segmento = '%s' ", $this->request->param('division') );
        }

        $sql= sprintf("SELECT count(DISTINCT h.id) as totalTickets, e.nome, u.segmento, u.nick
                       FROM crm.historico as h
                       LEFT JOIN crm.eventos as e ON ( e.id = h.evento_id )
                       LEFT JOIN mak.users u ON (u.id=h.user_id)
                       WHERE DATE(h.`data`) = DATE(CURDATE())
                       %s
                       GROUP BY user_id
                       ORDER BY totalTickets DESC
                       LIMIT 100", $where);

        $query = DB::query(Database::SELECT, $sql);
        $response = $query->execute()->as_array();

        //s($sql, $response);

        if ( isset( $response[0] ) )
        {
            $total = $response[0]['totalTickets'];
        }

        return $this->response->body( $total );
    }

}