<?php

class Controller_Users extends Controller_Website
{

   
   public function before()
    {
         parent::before();
    }


    public function action_index()
    {     
     
        $sql = sprintf("SELECT id,nick,email,depto FROM mak.rolemak_users LIMIT %s,%s", 
                           $this->pagination->offset+$this->pagination->current_page-1,
                           $this->pagination->items_per_page
                           );
        
        
        $query = DB::query(Database::SELECT, $sql);
        
        $data = $query->execute()->as_array();	
        
        // s($data);
        // return  $result;
        
        $view = parent::tablefy( $data );
        $response = parent::pagefy($view);
        
        $this->response->body($response);

     
        
    }


  
   
}