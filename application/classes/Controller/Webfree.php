<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Webfree extends Controller  {


    public function before()
    {
         parent::before();
         
            $this->pagination = Pagination::factory(array(
            'total_items'    => 1000,
            'items_per_page' => 10,
            'view'           => 'pagination/digg',
            'auto_hide'      => TRUE,
        ));	
        
       
        // if(!isset($_SESSION['Authorization'] ))     self::api_login();	
         $this->action_constants();
         $this->segments = self::action_get_segment();
         
        //  s(_DOLAR, $this->segments);
        //  DIE();
        
       
         

    }

    public function pagefy( $data )
    {
        $url = $_SERVER["HTTP_X_FORWARDED_PROTO"].'://'.$_SERVER["HTTP_X_FORWARDED_HOST"].'/engine/pagefy';
        // echo $post  = json_encode($data);
        $post['content'] =  $data;
     
        $response= Request::factory($url)
            ->method('POST')
            // ->headers('Authorization', $_SESSION['Authorization'])
            ->post($post)
            ->execute()
            ->body();
            
        return $response;           

    }
    
  public function tablefy( $data )
    {
       echo  $url = $_SERVER["HTTP_X_FORWARDED_PROTO"].'://'.$_SERVER["HTTP_X_FORWARDED_HOST"].'/notifications/componentfy/table/';
        // echo $post  = json_encode($data);
        $post['array'] =  $data;
     
        $response= Request::factory($url)
            ->method('POST')
            // ->headers('Authorization', $_SESSION['Authorization'])
            ->post($post)
            ->execute()
            ->body();
            
        return $response;            
          //  die();

        // $response = json_decode($json, true);

        // if (count($response) > 0) {
        //     return true;
        // }

    }

 public function chartfy( $data )
    {
       echo  $url = $_SERVER["HTTP_X_FORWARDED_PROTO"].'://'.$_SERVER["HTTP_X_FORWARDED_HOST"].'/notifications/componentfy/table/';
        // echo $post  = json_encode($data);
        $post['array'] =  $data;
     
        $response= Request::factory($url)
            ->method('POST')
            // ->headers('Authorization', $_SESSION['Authorization'])
            ->post($post)
            ->execute()
            ->body();
            
        return $response;            
          //  die();

        // $response = json_decode($json, true);

        // if (count($response) > 0) {
        //     return true;
        // }

    }
    public function action_connect_SELECT($sql)
    {
        //echo DBS;
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();
        //echo Kohana::debug((array) $result);
        return  $result;


    }

    public function action_connect_INSERT($sql)
    {
        $query = DB::query(Database::INSERT, $sql);
        $result = $query->execute(DBS);
        return $result;
    }		

    public function action_connect_UPDATE($sql)
    {
        $query = DB::query(Database::UPDATE, $sql);
        $result = $query->execute(DBS);
        return $result;
    }		

    public function action_connect_DELETE($sql)
    {
        $query = DB::query(Database::DELETE, $sql);
        $result = $query->execute(DBS);
        return $result;
    }		

    function api_login()
    {
        $json = Request::factory($_ENV["api"].'/v1/Users/<USER>')
            ->method('POST')
            ->post( array(
                'email' 		=> '<EMAIL>',
                'password' 		=> 'er4y5ha7*',
            )
                  )
            ->execute()
            ->body();

        $token = json_decode($json,true);

        $_SESSION['Authorization'] = $token['id'];
    }

   public function action_constants()
    {

        if (!defined('DBS')) {
            define('_DOLAR',$this->dolar());
           // if ( $_SERVER['HTTP_HOST'] =='playa') {
           //     define('DBS','mak_playa');
           // }else{
                define('DBS','mak');
           // }
        }			

        // if (!defined('NFE')) {
        //     include_once(APPPATH."/config/nfe.php");
        // }	

        if (!defined('_NEXT')) {
            define('_NEXT',"if (
							  (SELECT SUM(next.quant) FROM next RIGHT  JOIN shipments ON shipments.id=next.shipment WHERE  month(shipments.status)=0 and next.isbn=inv.id) >0 ,
							  (
								SELECT SUM(next.quant)   
								FROM   next 
								RIGHT  JOIN shipments ON shipments.id=next.shipment 
								WHERE  month(shipments.status)=0 and next.isbn=inv.id and next.state <>9)
							  ,0) 
							  AS Next");

        }	

    }

   public function dolar()	
    {	
        $sql = sprintf("SELECT Dolar  FROM   Vars	");
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

        //echo Kohana::debug((array) $result);

        return  $result[0]['Dolar'];
    }	

    public function avg_dolar($date='2012-01')	
    {	
        $cache_id = 'avg_dolar_'.$date;
        $cache = Cache::instance('memcache');
        //$cache->delete($cache_id );
        if ( ! $response = $cache->get($cache_id) )
        {
            $sql = sprintf("SELECT *  FROM  estatisticas.dolar_medio WHERE mes='%s'", $date	);
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute()->as_array();

            //echo Kohana::debug((array) $result);
            if(count($result)>0)
            { 
                $response=  $result[0]['valor'];
            }else{
                $response= 0;	
            }

            $cache->set($cache_id, $response, 60*60*12);

        }

        return $response;

    }		    

    public function action_get_segment()	
    {	
     
     $sql= sprintf("SELECT * FROM  %s",  'mak.Segmentos') ;
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute('mak')->as_array();
            foreach($result as $key => $val){
                $response[$val['SegmentoPOID']]= $val ;
            }
        return   $response;    
    }


}