<?php defined('SYSPATH') or die('No direct script access.');

class Controller_Website extends Controller_Websession  {


    public function before()
    {
         parent::before();

            $this->pagination = Pagination::factory(array(
            'total_items'    => 1000,
            'items_per_page' => 10,
            'view'           => 'pagination/digg',
            'auto_hide'      => TRUE,
        ));


        // if(!isset($_SESSION['Authorization'] ))     self::api_login();
         $this->action_constants();
         $this->segments = self::action_get_segment();
        //  $this->companies = $this->action_get_emit() ;
         $this->segs = $this->action_get_seg() ;
        //  $this->sellers = $this->action_get_sellers() ;

        //  s(_DOLAR, $this->segments);
        //  DIE();




    }

    public function action_get_seg()
    {

        $cache_id = 'action_get_seg';
        $cache = Cache::instance('file');
        //$cache->delete($cache_id );
        if ( ! $response = $cache->get($cache_id) )
        {

            $sql= sprintf("SELECT * FROM  %s",  'mak.Segmentos') ;
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute('mak')->as_array();
            foreach($result as $key => $val){
                $response[$val['SegmentoPOID']]= $val ;
            }


            $cache->set($cache_id, $response, 60*60*18);

        }

        return $response;
    }

    public function action_get_emit()
    {

        //echo  $this->request->uri() ;

        if( substr($this->request->uri(),0,5)== 'leads')
        {
            $where=' ' ;
        }else{
            $where=' WHERE tpAmb=1'		;
        }
        //die();

        $cache_id = 'action_get_emit_'.DBS;
        $cache = Cache::instance('file');
        $cache->delete($cache_id );
        if ( ! $response = $cache->get($cache_id) )
        {

            $sql= sprintf("SELECT *,  %s as ID, %s AS Empresa FROM  %s %s", EMITENTE_ID, EMITENTE_FANTASIA, EMITENTES, $where) ;
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute('mak')->as_array();

            foreach($result as $key => $val){
                $result[$key]['NFE']['nfe']= $this->table('nfe',  $val['CNPJ']);
                $result[$key]['NFE']['pedidos']= $this->table('pedidos',  $val['CNPJ']);
                $result[$key]['NFE']['docs']= $this->table('docs',  $val['CNPJ']);
                $result[$key]['NFE']['total']= $this->table('total',  $val['CNPJ']);
                $result[$key]['NFE']['entradas']= $this->table('entradas',  $val['CNPJ']);
                $result[$key]['NFE']['entradas_det']= $this->table('entradas_det',  $val['CNPJ']);
                $result[$key]['DB']['mak']= 'mak_'. substr($val['CNPJ'],10,4);
                $result[$key]['Transporter']= 'mak_'. substr($val['CNPJ'],10,4).'.transportadora';

                $this->global_cnpj[$val['CNPJ']]['NFE']['nfe']= $this->table('nfe',  $val['CNPJ']);
                $this->global_cnpj[$val['CNPJ']]['NFE']['pedidos']= $this->table('pedidos',  $val['CNPJ']);
                $this->global_cnpj[$val['CNPJ']]['NFE']['docs']= $this->table('docs',  $val['CNPJ']);
                $this->global_cnpj[$val['CNPJ']]['NFE']['total']= $this->table('total',  $val['CNPJ']);
                $this->global_cnpj[$val['CNPJ']]['NFE']['entradas']= $this->table('entradas',  $val['CNPJ']);
                $this->global_cnpj[$val['CNPJ']]['NFE']['entradas_det']= $this->table('entradas_det',  $val['CNPJ']);
                $this->global_cnpj[$val['CNPJ']]['DB']['mak']= 'mak_'. substr($val['CNPJ'],10,4);
                $this->global_cnpj[$val['CNPJ']]['Transporter']= 'mak_'. substr($val['CNPJ'],10,4).'.transportadora';
                $this->global_cnpj[$val['CNPJ']]['DATA']= $val;
            }

            foreach($result as $key => $val){
                $emit[$val['EmitentePOID']] = $val;


                if($val['EmitentePOID']==6) {
                    $emit[$val['EmitentePOID']]['logo16']= "<img title='".$val['Fantasia']."' src='https://icons.iconarchive.com/icons/ph03nyx/super-mario/16/Flower-Ice-icon.png'> ";
                    $emit[$val['EmitentePOID']]['logo24']= "<img title='".$val['Fantasia']."' src='https://icons.iconarchive.com/icons/ph03nyx/super-mario/24/Flower-Ice-icon.png'> ";
                    $emit[$val['EmitentePOID']]['logo32']= 'https://icons.iconarchive.com/icons/ph03nyx/super-mario/32/Flower-Ice-icon.png';
                    $emit[$val['EmitentePOID']]['logo48']= 'https://icons.iconarchive.com/icons/ph03nyx/super-mario/48/Flower-Fire-icon.png';
                    $emit[$val['EmitentePOID']]['logo64']= 'https://icons.iconarchive.com/icons/ph03nyx/super-mario/64/Flower-Fire-icon.png';
                }else{

                    if($val['EmitentePOID']==5) {
                        $emit[$val['EmitentePOID']]['logo16']= "<img title='".$val['Fantasia']."' src='https://icons.iconarchive.com/icons/ph03nyx/super-mario/16/Flower-Ice-icon.png'> ";
                        $emit[$val['EmitentePOID']]['logo24']= "<img title='".$val['Fantasia']."' src='https://www.veryicon.com/icon/32/Leisure/At%20the%20Beach/sandcastle.png'> ";
                        $emit[$val['EmitentePOID']]['logo32']= 'https://www.veryicon.com/icon/32/Leisure/At%20the%20Beach/sandcastle.png';
                        $emit[$val['EmitentePOID']]['logo48']= 'https://www.veryicon.com/icon/48/Leisure/At%20the%20Beach/sandcastle.png';
                        $emit[$val['EmitentePOID']]['logo64']= 'https://www.veryicon.com/icon/64/Leisure/At%20the%20Beach/sandcastle.png';
                    }else{

                        $emit[$val['EmitentePOID']]['logo16']= $val['Fantasia'];
                        $emit[$val['EmitentePOID']]['logo24']= $val['Fantasia'];
                        $emit[$val['EmitentePOID']]['logo32']= $val['Fantasia'];
                        $emit[$val['EmitentePOID']]['logo48']= '';
                        $emit[$val['EmitentePOID']]['logo64']= '';
                    }
                }

                if($val['EmitentePOID']==7) {
                    $emit[$val['EmitentePOID']]['logo24']=
                        "<img  title='".$val['Fantasia']."' height='24px' src='https://imagepng.org/wp-content/uploads/2017/06/bandeira-de-goias-7.png' > ";




                }
                if($val['EmitentePOID']==1) {
                    $emit[$val['EmitentePOID']]['logo24']=
                        "<img  title='".$val['Fantasia']."' height='24px' src='https://imagepng.org/wp-content/uploads/2017/05/bandeira-do-estado-de-sao-paulo-8.png' > ";


                }
            }
            //echo Kohana::debug((array) $emit);
            $response= $emit;
            $cache->set($cache_id, $response, 60*60*18);

        }

        return $response;
    }

    public function mustache($data, $view)
    {
       Mustache_Autoloader::register();

        $mustache = new Mustache_Engine(array(
            'template_class_prefix' => '__MyTemplates_',
            'cache' => APPPATH.'/cache'.'/tmp/cache/mustache',
            'cache_file_mode' => 0666, // Please, configure your umask instead of doing this :)
            'cache_lambda_templates' => true,
            'loader' => new Mustache_Loader_FilesystemLoader(APPPATH.'/views'),
            'partials_loader' => new Mustache_Loader_FilesystemLoader(APPPATH.'/views/partials'),
            'helpers' => array('i18n' => function($text) {
                // do something translatey here...
            }),
            'escape' => function($value) {
                return htmlspecialchars($value, ENT_COMPAT, 'UTF-8');
            },
            'charset' => 'ISO-8859-1',
            'logger' => new Mustache_Logger_StreamLogger('php://stderr'),
            'strict_callables' => true,
            'pragmas' => [Mustache_Engine::PRAGMA_FILTERS],
        ));

        $tpl = $mustache->loadTemplate($view); // loads __DIR__.'/views/foo.mustache';
        return $tpl->render(array('data' => $data));
   }

    public function bootstrapfy( $data )
    {
         return View::factory('pagefy/bootstrap5')
		 							->set('content', $data )

										  ;

        return $response;

    }

    public function pagefy( $data, $theme='dark' )
    {
         return View::factory('pagefy/tailwind-'.$theme)
		 							->set('content', $data )

										  ;

        // $data =str_replace('/index.php/Componentfy/Table',$_SERVER['HTTP_X_FORWARDED_PREFIX'].$_SERVER['REQUEST_URI'],$data);
        // $url = $_SERVER["HTTP_X_FORWARDED_PROTO"].'://'.$_SERVER["HTTP_X_FORWARDED_HOST"].'/notifications/pagefy';
        // // echo $post  = json_encode($data);
        // $post['content'] =  $data;
        // $post['theme'] =  $theme;

        // $response= Request::factory($url)
        //     ->method('POST')
        //     // ->headers('Authorization', $_SESSION['Authorization'])
        //     ->post($post)
        //     ->execute()
        //     ->body();

        return $response;

    }

    public function tablefy2( $data )
    {




        // if($this->request->query('page')) $page=$this->request->query('page');
        $url = sprintf($_SERVER["HTTP_X_FORWARDED_PROTO"].'://'.$_SERVER["HTTP_X_FORWARDED_HOST"].'/engine/componentfy/table/)');
        // echo $post  = json_encode($data);
        $post['array'] =  $data;

        $response= Request::factory($url)
            ->method('POST')
            // ->headers('Authorization', $_SESSION['Authorization'])
            ->post($post)
            ->execute()
            ->body();

        return $response;
          //  die();

        // $response = json_decode($json, true);

        // if (count($response) > 0) {
        //     return true;
        // }

    }

    public function action_connect_SELECT($sql)
    {
        //echo DBS;
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();
        //echo Kohana::debug((array) $result);
        return  $result;


    }

    public function action_connect_INSERT($sql)
    {
        $query = DB::query(Database::INSERT, $sql);
        $result = $query->execute(DBS);
        return $result;
    }

    public function action_connect_UPDATE($sql)
    {
        $query = DB::query(Database::UPDATE, $sql);
        $result = $query->execute(DBS);
        return $result;
    }

    public function action_connect_DELETE($sql)
    {
        $query = DB::query(Database::DELETE, $sql);
        $result = $query->execute(DBS);
        return $result;
    }

    function api_login()
    {
        $json = Request::factory($_ENV["api"].'/v1/Users/<USER>')
            ->method('POST')
            ->post( array(
                'email' 		=> '<EMAIL>',
                'password' 		=> 'er4y5ha7*',
            )
                  )
            ->execute()
            ->body();

        $token = json_decode($json,true);

        $_SESSION['Authorization'] = $token['id'];
    }

    public function action_constants()
    {

        if (!defined('DBS')) {
            define('_DOLAR',$this->dolar());
           // if ( $_SERVER['HTTP_HOST'] =='playa') {
           //     define('DBS','mak_playa');
           // }else{
                define('DBS','mak');
           // }
        }

        // if (!defined('NFE')) {
        //     include_once(APPPATH."/config/nfe.php");
        // }

        if (!defined('_NEXT')) {
            define('_NEXT',"if (
							  (SELECT SUM(next.quant) FROM next RIGHT  JOIN shipments ON shipments.id=next.shipment WHERE  month(shipments.status)=0 and next.isbn=inv.id) >0 ,
							  (
								SELECT SUM(next.quant)
								FROM   next
								RIGHT  JOIN shipments ON shipments.id=next.shipment
								WHERE  month(shipments.status)=0 and next.isbn=inv.id and next.state <>9)
							  ,0)
							  AS Next");

        }

    }

    public function dolar()
    {
        $sql = sprintf("SELECT Dolar  FROM   Vars	");
        $query = DB::query(Database::SELECT, $sql);
        $result = $query->execute()->as_array();

        //echo Kohana::debug((array) $result);

        return  $result[0]['Dolar'];
    }

    public function avg_dolar($date='2012-01')
    {
        $cache_id = 'avg_dolar_'.$date;
        $cache = Cache::instance('file');
        //$cache->delete($cache_id );
        if ( ! $response = $cache->get($cache_id) )
        {
            $sql = sprintf("SELECT *  FROM  estatisticas.dolar_medio WHERE mes='%s'", $date	);
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute()->as_array();

            //echo Kohana::debug((array) $result);
            if(count($result)>0)
            {
                $response=  $result[0]['valor'];
            }else{
                $response= 0;
            }

            $cache->set($cache_id, $response, 60*60*12);

        }

        return $response;

    }

    public function action_get_segment()
    {

     $sql= sprintf("SELECT * FROM  %s",  'mak.Segmentos') ;
            $query = DB::query(Database::SELECT, $sql);
            $result = $query->execute('mak')->as_array();
            foreach($result as $key => $val){
                $response[$val['SegmentoPOID']]= $val ;
            }
        return   $response;
    }


}