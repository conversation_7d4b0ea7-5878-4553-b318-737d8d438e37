<?php

class Controller_Zap extends Controller_Website
{

   
   public function before()
    {
         parent::before();
         
    }



    public function action_index()
    {
        $segid = $this->request->param('division');
        $brand = $this->request->param('xtras');

        $data = self::get($brand);

       $theme='dark';
        $view = '';
        
      
        $color['UltimaCompraDias'][0]['class'] = ' text-green-400 text-md font-bold';
        $color['UltimaCompraDias'][0]['rule']  = ' > 0 ';
        $color['UltimaCompraDias'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['UltimaCompraDias'][1]['rule']  = ' > 30 ';
        $color['UltimaCompraDias'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        $color['UltimaCompraDias'][2]['rule']  = ' > 60';
        $color['UltimaCompraDias'][3]['class'] = ' text-red-700 text-md  font-bold';
        $color['UltimaCompraDias'][3]['rule']  = ' > 90 ';
        
         $color['Subtotal'][0]['class'] = ' text-green-400 text-md font-bold';
        $color['Subtotal'][0]['rule']  = ' > 1000 ';
        $color['Subtotal'][1]['class'] = ' text-blue-400 text-md font-bold';
        $color['Subtotal'][1]['rule']  = ' > 10000 ';
        $color['Subtotal'][2]['class'] = ' text-yellow-600 text-md  font-bold';
        $color['Subtotal'][2]['rule']  = ' > 50000';
        $color['Subtotal'][3]['class'] = ' text-red-700 text-md  font-bold';
        $color['Subtotal'][3]['rule']  = ' > 10000 ';
        
        $data['color'] = $color;
        
        //// Editable
        
        $edt['nome']      = 'Chatbots.contacts|nome|id|id';
        $edt['posicao']   = 'Chatbots.contacts|posicao|id|id';
        $edt['dominios']  = 'Chatbots.contacts|dominios|id|id';
        $edt['fone']      = 'Chatbots.contacts|fone|id|id';
        $edt['idcli']     = 'Chatbots.contacts|idcli|id|id';
        // $edt['ParecerComercial'] = 'mak.clientes|ParecerComercial|id|_IDCLI';
        $data['editable'] = $edt;
        
        // $idven =  $this->request->query('idven');
        // $baseurl='/metrics/customers/index';
        
        
            
        // s( $this->pagination);
        // $data['pagination'] = $this->pagination;
        $data['theme'] = 3;
        
    //   $data['found_rows'] = $found_rows;
        // $data['pager']['pagination'] = $this->pagination;
        // // die();
            $baseurl=$_SERVER["HTTP_X_FORWARDED_PROTO"].'://'.$_SERVER["HTTP_X_FORWARDED_HOST"].'/metrics/zap/addform';
            $data['htmx'][] = array('title' => ' <button hx-get="/metrics/zap/addform"  hx-swap="outerHTML"  class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">Adicionar</button>') ;
            $data['htmx'][] = array('title' => ' <button hx-get="/metrics/zap/messageform"  hx-swap="outerHTML"  class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Enviar Mensagem</button>') ;
            // $data['caption'][] = array('link'=>$baseurl.'/1/?name=reta', 'title' => 'Retas') ;
            // $data['caption'][] = array('link'=>$baseurl.'/2/?line=620', 'title' => '6200') ;


        
        $view.= parent::tablefy( $data );
        
        // s($data['pagination']);
       
        $response = parent::pagefy($view,$theme);
        // s($_SERVER);


        $this->response->body($response);
     
    }

    private function get($brand)
    {
        $sql = sprintf("SELECT  w.*,c.nome as Cliente, c.id as _IDCLI
                        FROM Chatbots.contacts w
                     
                        LEFT JOIN clientes c ON (c.id=w.idcli)
                        ");

        return $result = $this->action_connect_SELECT($sql);
    }


  public function action_sendmessage()
  {
    // s($this->request->post());
    if($this->request->post('message') > '')  $message =  $this->request->post('message');
    if($this->request->post('fone') > 0 )     $fone =  $this->request->post('fone');
    // https://office.vallery.com.br/notifications/index/send/?phone=5511995070209&message=Bom%20dia!
    
    $response = Request::factory( $_SERVER["HTTP_X_FORWARDED_PROTO"].'://'.$_SERVER["HTTP_X_FORWARDED_HOST"].'/notifications/index/send/' )
                ->method('GET')
                ->query(array(
                    'phone'   => $fone,
                    'message' =>  $message
                ))->execute()->body();
                
     $this->response->body($response);
                
                
  }
    
  public function action_add()
  {
      // s($this->request->post());
       
       if($this->request->post('nome') > '')    $nome =  $this->request->post('nome');
       if($this->request->post('fone') > 0 )    $fone =  $this->request->post('fone');
       if($this->request->post('posicao') > '') $posicao =  $this->request->post('posicao');
       if($this->request->post('idcli') > 0)    $idcli =  $this->request->post('idcli');
       
       $sql = sprintf("INSERT INTO Chatbots.`contacts` (`id`, `timestamp`, `nome`, `posicao`, `fone`, `idcli`, `ativo`) 
                              VALUES (NULL, CURRENT_TIMESTAMP, '%s', '%s', '%s', '%s', '1')",
                              $nome,
                              $posicao,
                              $fone,
                              $idcli
                      );
                      
       $result = $this->action_connect_INSERT($sql);
       
       $response = sprintf('Whatsapp de %s (%s) número %s como %s cadastrado com sucesso!',  $nome,$idcli,$fone, $posicao);
      
       $this->response->body($response);
       
       
  }
  
 
  public function action_messageform()
  {
 
        $response = ' <form class="w-full max-w-lg" hx-post="/metrics/zap/sendmessage/" hx-target="this" hx-swap="outerHTML">
  <div class="flex flex-wrap -mx-3 mb-6">
  
    <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0">
    
      <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="nome">
        Mensagem
      </label>
      <input class="appearance-none block w-full bg-gray-200 text-gray-700 border border-red-500 rounded py-3 px-4 mb-3 leading-tight focus:outline-none focus:bg-white" name="message" type="text" placeholder="Mensagem" value="Olá, como vai?">
    </div>
    <div class="w-full md:w-1/2 px-3">
      <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="fone">
        Telefone
      </label>
      <input class="appearance-none block w-full bg-gray-200 text-gray-700 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500" name="fone" type="text" placeholder="5511999999"  value="5511995070209">
    </div>
  </div>

    <button  class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Submeter</button>
   
    <button  class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" hx-get="/metrics/zap/messageform/">Cancelar</button>
    
  </form>
 
</form> ';

  

        $this->response->body($response);
  }
 
  
  public function action_addform()
  {
 
        $response = ' <form class="w-full max-w-lg" hx-post="/metrics/zap/add/" hx-target="this" hx-swap="outerHTML">
  <div class="flex flex-wrap -mx-3 mb-6">
  
    <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0">
      <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="nome">
        Nome
      </label>
      <input class="appearance-none block w-full bg-gray-200 text-gray-700 border border-red-500 rounded py-3 px-4 mb-3 leading-tight focus:outline-none focus:bg-white" name="nome" type="text" placeholder="Nome do Contato" value="">
    </div>
    <div class="w-full md:w-1/2 px-3">
      <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="fone">
        Telefone
      </label>
      <input class="appearance-none block w-full bg-gray-200 text-gray-700 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500" name="fone" type="text" placeholder="5511999999"  value="">
    </div>
  </div>

  <div class="flex flex-wrap -mx-3 mb-2">
   
    <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0">
      <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="posicao">
        Posição
      </label>
      <div class="relative">
        <select class="block appearance-none w-full bg-gray-200 border border-gray-200 text-gray-700 py-3 px-4 pr-8 rounded leading-tight focus:outline-none focus:bg-white focus:border-gray-500" name="posicao"  value="">
          <option>Diretor</option>
          <option>Gerente</option>
          <option>Técnico</option>
          <option>Comprador</option>
          <option>Financeiro</option>
        </select>
      </div>
    </div>
    <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0">
      <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="idcli">
        Empresa
      </label>
      <input class="appearance-none block w-full bg-gray-200 text-gray-700 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500" name="idcli" type="text" placeholder="30000"  value="">
    </div>
  </div>
   
     
    <button  class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Submeter</button>
   
    <button  class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" hx-get="/metrics/zap/addform/">Cancelar</button>
    
  </form>
 
</form> ';

  

        $this->response->body($response);
  }
  
  
  
}