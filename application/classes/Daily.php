<?php

class Daily 
{
    function __construct($vars) 
    {
        
        $this->v = $vars;
       
    }
    
    public function process($array)
    {
       
        $this->seller = $array['blue'][0]['seller'] ?? 'welttec';
        
        if(count($array)==0)
        {
            return [];
            
            echo $sql;
            
            die('Sem registros!');
        }
        
        // s($array);
        // die();
        if(!empty($array['rolemak']))
        {
            $newArray =  self::head($array['rolemak']);
            $green = self::assemble($array['rolemak'],'green');
        }else{    
            $newArray =  self::head($array['blue']);
           
        }
        
        
         $blue= self::assemble($array['blue'],'blue');
        
        if(isset($green['rows']))
        {
            
            $newArray['rows'] = $green['rows']; 
        }else{
            $newArray['rows'] = []; 
            $green=[];
        }
        
        foreach($green as $ak => $av)
        {
            foreach($av as $bk => $bv)
            {
                foreach($bv['yearly'] as $ck => $cv)
                {
                    foreach($cv['monthly'] as $dk => $dv)
                    { 
                        $qtYearBlue7 = $blue[$ak][$bk]['qty_blue']/7;
                        $qtYearGreen7=0;
                        if($qtYearBlue7>0)  $qtYearGreen7 = round($green[$ak][$bk]['qty_green']/$qtYearBlue7,1);
                        $newArray[$ak][$bk]['qty_green7'] = $qtYearGreen7;
                        
                        $qtMonthBlue7 =  $blue[$ak][$bk]['yearly'][$ck]['qty_blue']/7;
                        $qtMonthGreen7='';
                        if($qtMonthBlue7>0) $qtMonthGreen7 = round($green[$ak][$bk]['yearly'][$ck]['qty_green']/$qtMonthBlue7,1);
                        $newArray[$ak][$bk]['yearly'][$ck]['qty_green7']= $qtMonthGreen7;
                        

                        
                        
                        
                        $newArray[$ak][$bk]['qty_blue'] = $blue[$ak][$bk]['qty_blue'];

                        $newArray[$ak][$bk]['yearly'][$ck]['qty_blue'] = $blue[$ak][$bk]['yearly'][$ck]['qty_blue'];
                        
                        $newArray[$ak][$bk]['yearly'][$ck]['monthly'][$dk]['qty_blue'] = $blue[$ak][$bk]['yearly'][$ck]['monthly'][$dk]['qty_blue'];
                    } 
                }
            }
        }
        
    //   s($newArray);
    
        return $newArray;
        
    }
   
    private function head($array)
    {
         $daysArray['link-blue'] = $this->v->uri_blue;
                        
                        $daysArray['link-red'] = $this->v->uri_red;
                        
                        $daysArray['link-green'] = $this->v->uri_green;
                        
                        $daysArray['seller'] = $this->seller;
                        
                        if('rolemak' == $this->seller ) $daysArray['seller-rolemak'] = true;
                        
                        if('welttec' == $this->seller ) $daysArray['seller-welttec'] = true;
                        
                        if('ss' == $this->seller ) $daysArray['seller-ss'] = true;
                        
                        if(isset($array[0]['grupo'])) 
                        {
                            $daysArray['grupo'] = $array[0]['grupo'];
                        
                            if('cnpj' == $array[0]['grupo'] ) $daysArray['group-cnpj'] = true;
                            
                        }
                        
                        $daysArray['title'] = $array[0]['title'];
                        
                        $daysArray['nome'] = $array[0]['nome'];
                        
                        $daysArray['bairro'] = $array[0]['bairro'];
                        
                        $daysArray['cidade'] = $array[0]['cidade'];
                        
                        $daysArray['estado'] = $array[0]['estado'];
                        
        return $daysArray;                
                        
    }
    
    private function assemble($array,$seller)
    {
        
        foreach($array as $key => $val) 
        {
            $idx = self::accentCharsModifier($val["Modelo"]);
            
            if(!isset($vl[$val["Modelo"]])) $vl[$val["Modelo"]]=0;
            
            $yearArray[$val["Ano"]][$val["Mes"]][$val["Dia"]]= $val["QT"];
            
        }
        
        $counterYear=0;
        
        $qtyYear= 0;   
        
        for($ano = date('Y'); $ano >= 2022; $ano--) 
        {
               
            $mesFim=12;
            
            if( $ano==date('Y') ) $mesFim=(int) date('m');
            
            $counterMonth=0;
              
            $qtyMonth= 0;   
              
            for($mes=$mesFim; $mes > 0; $mes--) 
            {
               
                    $counterDays=0;
                    
                    for($dia=1; $dia<=31; $dia++)
                    {
                        
                        // $daysArray['link-blue'] = $this->v->uri_blue;
                        
                        // $daysArray['link-red'] = $this->v->uri_red;
                        
                        // $daysArray['link-green'] = $this->v->uri_green;
                        
                        // $daysArray['seller'] = $this->seller;
                        
                        // if('rolemak' == $this->seller ) $daysArray['seller-rolemak'] = true;
                        
                        // if('welttec' == $this->seller ) $daysArray['seller-welttec'] = true;
                        
                        // if('ss' == $this->seller ) $daysArray['seller-ss'] = true;
                        
                        // $daysArray['grupo'] = $array[0]['grupo'];
                        
                        // if('cnpj' == $array[0]['grupo'] ) $daysArray['group-cnpj'] = true;
                        
                        // $daysArray['title'] = $array[0]['title'];
                        
                        // $daysArray['nome'] = $array[0]['nome'];
                        
                        // $daysArray['bairro'] = $array[0]['bairro'];
                        
                        // $daysArray['cidade'] = $array[0]['cidade'];
                        
                        // $daysArray['estado'] = $array[0]['estado'];
                        
                        $daysArray['rows'][$counterYear]['year'] = $ano;
                        
                        $daysArray['rows'][$counterYear]['yearly'][$counterMonth]['month'] =  $mes;
                        
                        $daysArray['rows'][$counterYear]['yearly'][$counterMonth]['monthly'][$counterDays]['day'] =  $dia;
                        
                        $daysArray['rows'][$counterYear]['yearly'][$counterMonth]['monthly'][$counterDays]['qty_'.$seller] = '';
                        
                        $diasemana_numero = date('w', strtotime($ano.'-'.$mes.'-'.$dia));

                        if($diasemana_numero==0 or $diasemana_numero==6)
                        {
                            $daysArray['rows'][$counterYear]['yearly'][$counterMonth]['monthly'][$counterDays]['qty_'.$seller] = '';
                            
                            $daysArray['rows'][$counterYear]['yearly'][$counterMonth]['monthly'][$counterDays]['weekend'] =  true;
                        }         

                        
                        if(isset($yearArray[$ano][$mes][$dia]))
                        {
                            
                            $daysArray['rows'][$counterYear]['yearly'][$counterMonth]['monthly'][$counterDays]['qty_'.$seller] = $yearArray[$ano][$mes][$dia];
                            
                            $qtyMonth+= $yearArray[$ano][$mes][$dia];
                            
                        }
                        
                        $counterDays++;
                    }

                    $daysArray['rows'][$counterYear]['yearly'][$counterMonth]['qty_'.$seller] =  $qtyMonth;
                    
                    $qtyYear+= $qtyMonth;
                    
                    $qtyMonth= 0;   
                    
                    $counterMonth++;    
                   
            }
            
            $daysArray['rows'][$counterYear]['qty_'.$seller] =  $qtyYear;
            
            $qtyYear= 0;   
            
            $counterYear++;
        }
        
        return $daysArray;
    }
   
    private function accentCharsModifier($str)
    {
        if(($length=mb_strlen($str,"UTF-8"))<strlen($str)){
            $i=$count=0;
            while($i<$length){
                if(strlen($c=mb_substr($str,$i,1,"UTF-8"))>1){
                    $he=htmlentities($c); 
                    if(($nC=preg_replace("#&([A-Za-z])(?:acute|cedil|caron|circ|grave|orn|ring|slash|th|tilde|uml);#", "\\1", $he))!=$he ||
                        ($nC=preg_replace("#&([A-Za-z]{2})(?:lig);#", "\\1", $he))!=$he ||
                        ($nC=preg_replace("#&[^;]+;#", "", $he))!=$he){
                        $str=str_replace($c,$nC,$str,$count);if($nC==""){$length=$length-$count;$i--;}
                    }
                }
                $i++;
            }
        }
        return strtolower(trim($str));
}  

}