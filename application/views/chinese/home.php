<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mandarim ChatGPT Interativo</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body class="bg-gray-900">
    
    <div class="container mx-auto p-4 max-w-lg">
        <h1 class="text-3xl font-bold text-center text-gray-500 mb-4">Mandarim ChatGPT Interativo</h1>

        <div id="chatWindow" class="mb-4 bg-white rounded shadow p-4 h-64 overflow-y-auto">
            <!-- Mensagens serão exibidas aqui -->
        </div>

        <div class="mb-4">
            <textarea id="userInput" class="w-full p-4 border border-gray-300 rounded" placeholder="Digite sua frase em Mandarim..."></textarea>
        </div>

        <div class="flex justify-between mb-4">
            <button id="sendMessage" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Enviar
            </button>
            <button id="clearMessage" class="bg-red-300 hover:bg-red-400 text-gray-800 font-bold py-2 px-4 rounded">
                Limpar
            </button>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $('#sendMessage').click(function() {
                var userInput = $('#userInput').val();
                if(userInput) {
                    $.post('chinese/chatgpt', { userInput: userInput }, function(data) {
                        var response = JSON.parse(JSON.stringify(data));
                        // alert(response);
                        // return true;
                        $('#chatWindow').append('<p class="text-blue-700">Você: ' + userInput + '</p>');
                        $('#chatWindow').append('<p class="text-green-700">ChatGPT: ' + response.chatResponse + '</p>');
                    });
                    $('#userInput').val('');
                }
            });

            $('#clearMessage').click(function() {
                $('#userInput').val('');
                $('#chatWindow').html('');
            });
        });
    </script>
</body>
</html>
