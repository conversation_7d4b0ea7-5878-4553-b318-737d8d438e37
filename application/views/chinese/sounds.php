<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--<link rel="shortcut icon" href="https://cdn.yoyochinese.com/titleicon-beta.png" type="image/png">-->
    <!--<link rel="preload" href="https://yoyochinese.com/public/fonts/v25/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtZ6Hw5aXp-p7K4KLg.woff2" as="font" type="font/woff2" crossorigin="">-->
    <!--<link rel="preload" href="https://yoyochinese.com/public/fonts/v25/JTUSjIg1_i6t8kCHKm459WdhyyTh89ZNpQ.woff2" as="font" type="font/woff2" crossorigin="">-->
    <!--<link rel="preload" href="https://yoyochinese.com/public/fonts/v25/JTUSjIg1_i6t8kCHKm459WlhyyTh89Y.woff2" as="font" type="font/woff2" crossorigin="">-->
    <!--<link rel="preload" href="https://yoyochinese.com/public/images/tools/<EMAIL>" as="image" type="image/webp">-->
    <!--<title>Interactive Pinyin Chart | Yoyo Chinese</title>-->
    <meta name="description" content="Learn how to pronounce Mandarin Chinese with audio demonstrations and video explanations for every possible sound combination in the language.">
    <meta name="keywords" content="">
    <!--<script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=AW-1044880504&amp;l=dataLayer&amp;cx=c"></script>-->
    <!--<script type="text/javascript" id="sendinblue-js" async="" src="https://sibautomation.com/sa.js?key=7852azetg14re00nflhjia9e"></script>-->
    <!--<script defer="" src="https://www.googletagmanager.com/gtag/js?id=G-9QRCQM8W35"></script>-->
    <!--<script>-->
    <!--    window.dataLayer = window.dataLayer || [];-->

    <!--    function gtag() {-->
    <!--        dataLayer.push(arguments);-->
    <!--    }-->
    <!--    gtag('js', new Date());-->
    <!--    gtag('config', 'G-9QRCQM8W35');-->
    <!--    gtag('config', 'AW-1044880504');-->
    <!--</script>-->
    <!--<link rel="stylesheet" href="https://yoyochinese.com/styles/font.e8227f6d.min.css">-->
    <link rel="stylesheet" href="https://yoyochinese.com/styles/public.0d8f3bad.min.css">
    <link rel="stylesheet" href="https://yoyochinese.com/styles/header.36c27c58.min.css">
    <link rel="stylesheet" href="https://yoyochinese.com/styles/footer.7a731390.min.css">
    <link rel="stylesheet" href="https://yoyochinese.com/styles/tools.3f37b8e5.min.css">
    <!--<script type="text/javascript" async="" src="https://googleads.g.doubleclick.net/pagead/viewthroughconversion/1044880504/?random=1701450549118&amp;cv=11&amp;fst=1701450549118&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;gtm=45be3bt0&amp;gcd=11l1l1l1l1&amp;dma=0&amp;u_w=2560&amp;u_h=1440&amp;url=https%3A%2F%2Fyoyochinese.com%2Fchinese-learning-tools%2FMandarin-Chinese-pronunciation-lesson%2Fpinyin-chart-table&amp;ref=https%3A%2F%2Fyoyochinese.com%2Fblog&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=Interactive%20Pinyin%20Chart%20%7C%20Yoyo%20Chinese&amp;auid=2064674491.1700253879&amp;fledge=1&amp;uaa=x86&amp;uab=64&amp;uafvl=Google%2520Chrome%3B119.0.6045.160%7CChromium%3B119.0.6045.160%7CNot%253FA_Brand%3B24.0.0.0&amp;uamb=0&amp;uap=Windows&amp;uapv=10.0.0&amp;uaw=0&amp;data=event%3Dgtag.config&amp;rfmt=3&amp;fmt=4"></script>-->
</head>

<body>
    <div id="root" class="menus-in">
        

        <main class="main-container">
            <div class="bpadding"></div>
            <div class="tabs tabs-top">
                
                <div class="tabs-content">
                    <div class="tabs-panel active">
                        <section class="pinyin-table">
                            <div class="yy-container">
                                <h1>Interactive <span class="import-font">Pinyin Chart</span></h1>
                                <p>Learn how to pronounce Mandarin Chinese like a native by practicing with our audio demonstrations and video explanations for every possible sound combination in the language.</p>
                                <div class="yy-row is-justify-center">
                                    <div class="yy-input yy-input--suffix search">
                                        <input type="text" autocomplete="off" id="searchInput" placeholder="search pinyin,use 'v' for 'ü'" class="yy-input__inner">
                                        <span class="yy-input__suffix">
          <span class="yy-input__suffix-inner"><img class="search-button" src="https://yoyochinese.com/public/images/tools/<EMAIL>" width="24px"></span>
                                        </span>
                                        <ul class="search-results">
                                            <!-- <li class="recommend"><span>zi</span></li>
          <li class="recommend"><span>z</span>h<span>i</span></li>
          <li class="recommend"><span>z</span>u<span>i</span></li>
          <li class="recommend"><span>z</span>hu<span>i</span></li> -->
                                        </ul>
                                    </div>
                                </div>
                                <div class="pinyin-chart-table">
                                    <table id="pinyinChart">
                                        <tr class="tr-header">
                                            <td class="td-cell td-pinyin__ null" data-index="[0,0]"></td>
                                            <td class=" td-pinyin__ null" data-index="[0,1]"></td>
                                            <td class=" td-pinyin__b true" data-index="[0,2]">b</td>
                                            <td class=" td-pinyin__p true" data-index="[0,3]">p</td>
                                            <td class=" td-pinyin__m true" data-index="[0,4]">m</td>
                                            <td class=" td-pinyin__f true" data-index="[0,5]">f</td>
                                            <td class=" td-pinyin__d true" data-index="[0,6]">d</td>
                                            <td class=" td-pinyin__t true" data-index="[0,7]">t</td>
                                            <td class=" td-pinyin__n true" data-index="[0,8]">n</td>
                                            <td class=" td-pinyin__l true" data-index="[0,9]">l</td>
                                            <td class=" td-pinyin__g true" data-index="[0,10]">g</td>
                                            <td class=" td-pinyin__k true" data-index="[0,11]">k</td>
                                            <td class=" td-pinyin__h true" data-index="[0,12]">h</td>
                                            <td class=" td-pinyin__j true" data-index="[0,13]">j</td>
                                            <td class=" td-pinyin__q true" data-index="[0,14]">q</td>
                                            <td class=" td-pinyin__x true" data-index="[0,15]">x</td>
                                            <td class=" td-pinyin__z true" data-index="[0,16]">z</td>
                                            <td class=" td-pinyin__c true" data-index="[0,17]">c</td>
                                            <td class=" td-pinyin__s true" data-index="[0,18]">s</td>
                                            <td class=" td-pinyin__zh true" data-index="[0,19]">zh</td>
                                            <td class=" td-pinyin__ch true" data-index="[0,20]">ch</td>
                                            <td class=" td-pinyin__sh true" data-index="[0,21]">sh</td>
                                            <td class=" td-pinyin__r true" data-index="[0,22]">r</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__i true" data-index="[1,0]">i</td>
                                            <td class=" td-pinyin__ null" data-index="[1,1]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,7]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,8]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,9]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,12]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[1,15]"></td>
                                            <td class=" td-pinyin__zi true" data-index="[1,16]">zi</td>
                                            <td class=" td-pinyin__ci true" data-index="[1,17]">ci</td>
                                            <td class=" td-pinyin__si true" data-index="[1,18]">si</td>
                                            <td class=" td-pinyin__zhi true" data-index="[1,19]">zhi</td>
                                            <td class=" td-pinyin__chi true" data-index="[1,20]">chi</td>
                                            <td class=" td-pinyin__shi true" data-index="[1,21]">shi</td>
                                            <td class=" td-pinyin__ri true" data-index="[1,22]">ri</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__a true" data-index="[2,0]">a</td>
                                            <td class=" td-pinyin__a true" data-index="[2,1]">a</td>
                                            <td class=" td-pinyin__ba true" data-index="[2,2]">ba</td>
                                            <td class=" td-pinyin__pa true" data-index="[2,3]">pa</td>
                                            <td class=" td-pinyin__ma true" data-index="[2,4]">ma</td>
                                            <td class=" td-pinyin__fa true" data-index="[2,5]">fa</td>
                                            <td class=" td-pinyin__da true" data-index="[2,6]">da</td>
                                            <td class=" td-pinyin__ta true" data-index="[2,7]">ta</td>
                                            <td class=" td-pinyin__na true" data-index="[2,8]">na</td>
                                            <td class=" td-pinyin__la true" data-index="[2,9]">la</td>
                                            <td class=" td-pinyin__ga true" data-index="[2,10]">ga</td>
                                            <td class=" td-pinyin__ka true" data-index="[2,11]">ka</td>
                                            <td class=" td-pinyin__ha true" data-index="[2,12]">ha</td>
                                            <td class=" td-pinyin__ null" data-index="[2,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[2,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[2,15]"></td>
                                            <td class=" td-pinyin__za true" data-index="[2,16]">za</td>
                                            <td class=" td-pinyin__ca true" data-index="[2,17]">ca</td>
                                            <td class=" td-pinyin__sa true" data-index="[2,18]">sa</td>
                                            <td class=" td-pinyin__zha true" data-index="[2,19]">zha</td>
                                            <td class=" td-pinyin__cha true" data-index="[2,20]">cha</td>
                                            <td class=" td-pinyin__sha true" data-index="[2,21]">sha</td>
                                            <td class=" td-pinyin__ null" data-index="[2,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ai true" data-index="[3,0]">ai</td>
                                            <td class=" td-pinyin__ai true" data-index="[3,1]">ai</td>
                                            <td class=" td-pinyin__bai true" data-index="[3,2]">bai</td>
                                            <td class=" td-pinyin__pai true" data-index="[3,3]">pai</td>
                                            <td class=" td-pinyin__mai true" data-index="[3,4]">mai</td>
                                            <td class=" td-pinyin__ null" data-index="[3,5]"></td>
                                            <td class=" td-pinyin__dai true" data-index="[3,6]">dai</td>
                                            <td class=" td-pinyin__tai true" data-index="[3,7]">tai</td>
                                            <td class=" td-pinyin__nai true" data-index="[3,8]">nai</td>
                                            <td class=" td-pinyin__lai true" data-index="[3,9]">lai</td>
                                            <td class=" td-pinyin__gai true" data-index="[3,10]">gai</td>
                                            <td class=" td-pinyin__kai true" data-index="[3,11]">kai</td>
                                            <td class=" td-pinyin__hai true" data-index="[3,12]">hai</td>
                                            <td class=" td-pinyin__ null" data-index="[3,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[3,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[3,15]"></td>
                                            <td class=" td-pinyin__zai true" data-index="[3,16]">zai</td>
                                            <td class=" td-pinyin__cai true" data-index="[3,17]">cai</td>
                                            <td class=" td-pinyin__sai true" data-index="[3,18]">sai</td>
                                            <td class=" td-pinyin__zhai true" data-index="[3,19]">zhai</td>
                                            <td class=" td-pinyin__chai true" data-index="[3,20]">chai</td>
                                            <td class=" td-pinyin__shai true" data-index="[3,21]">shai</td>
                                            <td class=" td-pinyin__ null" data-index="[3,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__an true" data-index="[4,0]">an</td>
                                            <td class=" td-pinyin__an true" data-index="[4,1]">an</td>
                                            <td class=" td-pinyin__ban true" data-index="[4,2]">ban</td>
                                            <td class=" td-pinyin__pan true" data-index="[4,3]">pan</td>
                                            <td class=" td-pinyin__man true" data-index="[4,4]">man</td>
                                            <td class=" td-pinyin__fan true" data-index="[4,5]">fan</td>
                                            <td class=" td-pinyin__dan true" data-index="[4,6]">dan</td>
                                            <td class=" td-pinyin__tan true" data-index="[4,7]">tan</td>
                                            <td class=" td-pinyin__nan true" data-index="[4,8]">nan</td>
                                            <td class=" td-pinyin__lan true" data-index="[4,9]">lan</td>
                                            <td class=" td-pinyin__gan true" data-index="[4,10]">gan</td>
                                            <td class=" td-pinyin__kan true" data-index="[4,11]">kan</td>
                                            <td class=" td-pinyin__han true" data-index="[4,12]">han</td>
                                            <td class=" td-pinyin__ null" data-index="[4,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[4,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[4,15]"></td>
                                            <td class=" td-pinyin__zan true" data-index="[4,16]">zan</td>
                                            <td class=" td-pinyin__can true" data-index="[4,17]">can</td>
                                            <td class=" td-pinyin__san true" data-index="[4,18]">san</td>
                                            <td class=" td-pinyin__zhan true" data-index="[4,19]">zhan</td>
                                            <td class=" td-pinyin__chan true" data-index="[4,20]">chan</td>
                                            <td class=" td-pinyin__shan true" data-index="[4,21]">shan</td>
                                            <td class=" td-pinyin__ran true" data-index="[4,22]">ran</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ang true" data-index="[5,0]">ang</td>
                                            <td class=" td-pinyin__ang true" data-index="[5,1]">ang</td>
                                            <td class=" td-pinyin__bang true" data-index="[5,2]">bang</td>
                                            <td class=" td-pinyin__pang true" data-index="[5,3]">pang</td>
                                            <td class=" td-pinyin__mang true" data-index="[5,4]">mang</td>
                                            <td class=" td-pinyin__fang true" data-index="[5,5]">fang</td>
                                            <td class=" td-pinyin__dang true" data-index="[5,6]">dang</td>
                                            <td class=" td-pinyin__tang true" data-index="[5,7]">tang</td>
                                            <td class=" td-pinyin__nang true" data-index="[5,8]">nang</td>
                                            <td class=" td-pinyin__lang true" data-index="[5,9]">lang</td>
                                            <td class=" td-pinyin__gang true" data-index="[5,10]">gang</td>
                                            <td class=" td-pinyin__kang true" data-index="[5,11]">kang</td>
                                            <td class=" td-pinyin__hang true" data-index="[5,12]">hang</td>
                                            <td class=" td-pinyin__ null" data-index="[5,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[5,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[5,15]"></td>
                                            <td class=" td-pinyin__zang true" data-index="[5,16]">zang</td>
                                            <td class=" td-pinyin__cang true" data-index="[5,17]">cang</td>
                                            <td class=" td-pinyin__sang true" data-index="[5,18]">sang</td>
                                            <td class=" td-pinyin__zhang true" data-index="[5,19]">zhang</td>
                                            <td class=" td-pinyin__chang true" data-index="[5,20]">chang</td>
                                            <td class=" td-pinyin__shang true" data-index="[5,21]">shang</td>
                                            <td class=" td-pinyin__rang true" data-index="[5,22]">rang</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ao true" data-index="[6,0]">ao</td>
                                            <td class=" td-pinyin__ao true" data-index="[6,1]">ao</td>
                                            <td class=" td-pinyin__bao true" data-index="[6,2]">bao</td>
                                            <td class=" td-pinyin__pao true" data-index="[6,3]">pao</td>
                                            <td class=" td-pinyin__mao true" data-index="[6,4]">mao</td>
                                            <td class=" td-pinyin__ null" data-index="[6,5]"></td>
                                            <td class=" td-pinyin__dao true" data-index="[6,6]">dao</td>
                                            <td class=" td-pinyin__tao true" data-index="[6,7]">tao</td>
                                            <td class=" td-pinyin__nao true" data-index="[6,8]">nao</td>
                                            <td class=" td-pinyin__lao true" data-index="[6,9]">lao</td>
                                            <td class=" td-pinyin__gao true" data-index="[6,10]">gao</td>
                                            <td class=" td-pinyin__kao true" data-index="[6,11]">kao</td>
                                            <td class=" td-pinyin__hao true" data-index="[6,12]">hao</td>
                                            <td class=" td-pinyin__ null" data-index="[6,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[6,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[6,15]"></td>
                                            <td class=" td-pinyin__zao true" data-index="[6,16]">zao</td>
                                            <td class=" td-pinyin__cao true" data-index="[6,17]">cao</td>
                                            <td class=" td-pinyin__sao true" data-index="[6,18]">sao</td>
                                            <td class=" td-pinyin__zhao true" data-index="[6,19]">zhao</td>
                                            <td class=" td-pinyin__chao true" data-index="[6,20]">chao</td>
                                            <td class=" td-pinyin__shao true" data-index="[6,21]">shao</td>
                                            <td class=" td-pinyin__rao true" data-index="[6,22]">rao</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__e true" data-index="[7,0]">e</td>
                                            <td class=" td-pinyin__e true" data-index="[7,1]">e</td>
                                            <td class=" td-pinyin__ null" data-index="[7,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[7,3]"></td>
                                            <td class=" td-pinyin__me true" data-index="[7,4]">me</td>
                                            <td class=" td-pinyin__ null" data-index="[7,5]"></td>
                                            <td class=" td-pinyin__de true" data-index="[7,6]">de</td>
                                            <td class=" td-pinyin__te true" data-index="[7,7]">te</td>
                                            <td class=" td-pinyin__ne true" data-index="[7,8]">ne</td>
                                            <td class=" td-pinyin__le true" data-index="[7,9]">le</td>
                                            <td class=" td-pinyin__ge true" data-index="[7,10]">ge</td>
                                            <td class=" td-pinyin__ke true" data-index="[7,11]">ke</td>
                                            <td class=" td-pinyin__he true" data-index="[7,12]">he</td>
                                            <td class=" td-pinyin__ null" data-index="[7,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[7,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[7,15]"></td>
                                            <td class=" td-pinyin__ze true" data-index="[7,16]">ze</td>
                                            <td class=" td-pinyin__ce true" data-index="[7,17]">ce</td>
                                            <td class=" td-pinyin__se true" data-index="[7,18]">se</td>
                                            <td class=" td-pinyin__zhe true" data-index="[7,19]">zhe</td>
                                            <td class=" td-pinyin__che true" data-index="[7,20]">che</td>
                                            <td class=" td-pinyin__she true" data-index="[7,21]">she</td>
                                            <td class=" td-pinyin__re true" data-index="[7,22]">re</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ei true" data-index="[8,0]">ei</td>
                                            <td class=" td-pinyin__ei true" data-index="[8,1]">ei</td>
                                            <td class=" td-pinyin__bei true" data-index="[8,2]">bei</td>
                                            <td class=" td-pinyin__pei true" data-index="[8,3]">pei</td>
                                            <td class=" td-pinyin__mei true" data-index="[8,4]">mei</td>
                                            <td class=" td-pinyin__fei true" data-index="[8,5]">fei</td>
                                            <td class=" td-pinyin__dei true" data-index="[8,6]">dei</td>
                                            <td class=" td-pinyin__ null" data-index="[8,7]"></td>
                                            <td class=" td-pinyin__nei true" data-index="[8,8]">nei</td>
                                            <td class=" td-pinyin__lei true" data-index="[8,9]">lei</td>
                                            <td class=" td-pinyin__gei true" data-index="[8,10]">gei</td>
                                            <td class=" td-pinyin__ null" data-index="[8,11]"></td>
                                            <td class=" td-pinyin__hei true" data-index="[8,12]">hei</td>
                                            <td class=" td-pinyin__ null" data-index="[8,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[8,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[8,15]"></td>
                                            <td class=" td-pinyin__zei true" data-index="[8,16]">zei</td>
                                            <td class=" td-pinyin__ null" data-index="[8,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[8,18]"></td>
                                            <td class=" td-pinyin__zhei true" data-index="[8,19]">zhei</td>
                                            <td class=" td-pinyin__ null" data-index="[8,20]"></td>
                                            <td class=" td-pinyin__shei true" data-index="[8,21]">shei</td>
                                            <td class=" td-pinyin__ null" data-index="[8,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__en true" data-index="[9,0]">en</td>
                                            <td class=" td-pinyin__en true" data-index="[9,1]">en</td>
                                            <td class=" td-pinyin__ben true" data-index="[9,2]">ben</td>
                                            <td class=" td-pinyin__pen true" data-index="[9,3]">pen</td>
                                            <td class=" td-pinyin__men true" data-index="[9,4]">men</td>
                                            <td class=" td-pinyin__fen true" data-index="[9,5]">fen</td>
                                            <td class=" td-pinyin__ null" data-index="[9,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[9,7]"></td>
                                            <td class=" td-pinyin__nen true" data-index="[9,8]">nen</td>
                                            <td class=" td-pinyin__ null" data-index="[9,9]"></td>
                                            <td class=" td-pinyin__gen true" data-index="[9,10]">gen</td>
                                            <td class=" td-pinyin__ken true" data-index="[9,11]">ken</td>
                                            <td class=" td-pinyin__hen true" data-index="[9,12]">hen</td>
                                            <td class=" td-pinyin__ null" data-index="[9,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[9,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[9,15]"></td>
                                            <td class=" td-pinyin__zen true" data-index="[9,16]">zen</td>
                                            <td class=" td-pinyin__cen true" data-index="[9,17]">cen</td>
                                            <td class=" td-pinyin__sen true" data-index="[9,18]">sen</td>
                                            <td class=" td-pinyin__zhen true" data-index="[9,19]">zhen</td>
                                            <td class=" td-pinyin__chen true" data-index="[9,20]">chen</td>
                                            <td class=" td-pinyin__shen true" data-index="[9,21]">shen</td>
                                            <td class=" td-pinyin__ren true" data-index="[9,22]">ren</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__eng true" data-index="[10,0]">eng</td>
                                            <td class=" td-pinyin__eng true" data-index="[10,1]">eng</td>
                                            <td class=" td-pinyin__beng true" data-index="[10,2]">beng</td>
                                            <td class=" td-pinyin__peng true" data-index="[10,3]">peng</td>
                                            <td class=" td-pinyin__meng true" data-index="[10,4]">meng</td>
                                            <td class=" td-pinyin__feng true" data-index="[10,5]">feng</td>
                                            <td class=" td-pinyin__deng true" data-index="[10,6]">deng</td>
                                            <td class=" td-pinyin__teng true" data-index="[10,7]">teng</td>
                                            <td class=" td-pinyin__neng true" data-index="[10,8]">neng</td>
                                            <td class=" td-pinyin__leng true" data-index="[10,9]">leng</td>
                                            <td class=" td-pinyin__geng true" data-index="[10,10]">geng</td>
                                            <td class=" td-pinyin__keng true" data-index="[10,11]">keng</td>
                                            <td class=" td-pinyin__heng true" data-index="[10,12]">heng</td>
                                            <td class=" td-pinyin__ null" data-index="[10,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[10,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[10,15]"></td>
                                            <td class=" td-pinyin__zeng true" data-index="[10,16]">zeng</td>
                                            <td class=" td-pinyin__ceng true" data-index="[10,17]">ceng</td>
                                            <td class=" td-pinyin__seng true" data-index="[10,18]">seng</td>
                                            <td class=" td-pinyin__zheng true" data-index="[10,19]">zheng</td>
                                            <td class=" td-pinyin__cheng true" data-index="[10,20]">cheng</td>
                                            <td class=" td-pinyin__sheng true" data-index="[10,21]">sheng</td>
                                            <td class=" td-pinyin__reng true" data-index="[10,22]">reng</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__er true" data-index="[11,0]">er</td>
                                            <td class=" td-pinyin__er true" data-index="[11,1]">er</td>
                                            <td class=" td-pinyin__ null" data-index="[11,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,7]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,8]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,9]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,12]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,15]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[11,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__i true" data-index="[12,0]">i</td>
                                            <td class=" td-pinyin__yi true" data-index="[12,1]">yi</td>
                                            <td class=" td-pinyin__bi true" data-index="[12,2]">bi</td>
                                            <td class=" td-pinyin__pi true" data-index="[12,3]">pi</td>
                                            <td class=" td-pinyin__mi true" data-index="[12,4]">mi</td>
                                            <td class=" td-pinyin__ null" data-index="[12,5]"></td>
                                            <td class=" td-pinyin__di true" data-index="[12,6]">di</td>
                                            <td class=" td-pinyin__ti true" data-index="[12,7]">ti</td>
                                            <td class=" td-pinyin__ni true" data-index="[12,8]">ni</td>
                                            <td class=" td-pinyin__li true" data-index="[12,9]">li</td>
                                            <td class=" td-pinyin__ null" data-index="[12,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[12,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[12,12]"></td>
                                            <td class=" td-pinyin__ji true" data-index="[12,13]">ji</td>
                                            <td class=" td-pinyin__qi true" data-index="[12,14]">qi</td>
                                            <td class=" td-pinyin__xi true" data-index="[12,15]">xi</td>
                                            <td class=" td-pinyin__ null" data-index="[12,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[12,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[12,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[12,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[12,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[12,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[12,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ia true" data-index="[13,0]">ia</td>
                                            <td class=" td-pinyin__ya true" data-index="[13,1]">ya</td>
                                            <td class=" td-pinyin__ null" data-index="[13,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[13,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[13,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[13,5]"></td>
                                            <td class=" td-pinyin__dia true" data-index="[13,6]">dia</td>
                                            <td class=" td-pinyin__ null" data-index="[13,7]"></td>
                                            <td class=" td-pinyin__ null" data-index="[13,8]"></td>
                                            <td class=" td-pinyin__lia true" data-index="[13,9]">lia</td>
                                            <td class=" td-pinyin__ null" data-index="[13,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[13,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[13,12]"></td>
                                            <td class=" td-pinyin__jia true" data-index="[13,13]">jia</td>
                                            <td class=" td-pinyin__qia true" data-index="[13,14]">qia</td>
                                            <td class=" td-pinyin__xia true" data-index="[13,15]">xia</td>
                                            <td class=" td-pinyin__ null" data-index="[13,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[13,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[13,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[13,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[13,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[13,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[13,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ian true" data-index="[14,0]">ian</td>
                                            <td class=" td-pinyin__yan true" data-index="[14,1]">yan</td>
                                            <td class=" td-pinyin__bian true" data-index="[14,2]">bian</td>
                                            <td class=" td-pinyin__pian true" data-index="[14,3]">pian</td>
                                            <td class=" td-pinyin__mian true" data-index="[14,4]">mian</td>
                                            <td class=" td-pinyin__ null" data-index="[14,5]"></td>
                                            <td class=" td-pinyin__dian true" data-index="[14,6]">dian</td>
                                            <td class=" td-pinyin__tian true" data-index="[14,7]">tian</td>
                                            <td class=" td-pinyin__nian true" data-index="[14,8]">nian</td>
                                            <td class=" td-pinyin__lian true" data-index="[14,9]">lian</td>
                                            <td class=" td-pinyin__ null" data-index="[14,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[14,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[14,12]"></td>
                                            <td class=" td-pinyin__jian true" data-index="[14,13]">jian</td>
                                            <td class=" td-pinyin__qian true" data-index="[14,14]">qian</td>
                                            <td class=" td-pinyin__xian true" data-index="[14,15]">xian</td>
                                            <td class=" td-pinyin__ null" data-index="[14,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[14,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[14,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[14,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[14,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[14,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[14,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__iang true" data-index="[15,0]">iang</td>
                                            <td class=" td-pinyin__yang true" data-index="[15,1]">yang</td>
                                            <td class=" td-pinyin__ null" data-index="[15,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,7]"></td>
                                            <td class=" td-pinyin__niang true" data-index="[15,8]">niang</td>
                                            <td class=" td-pinyin__liang true" data-index="[15,9]">liang</td>
                                            <td class=" td-pinyin__ null" data-index="[15,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,12]"></td>
                                            <td class=" td-pinyin__jiang true" data-index="[15,13]">jiang</td>
                                            <td class=" td-pinyin__qiang true" data-index="[15,14]">qiang</td>
                                            <td class=" td-pinyin__xiang true" data-index="[15,15]">xiang</td>
                                            <td class=" td-pinyin__ null" data-index="[15,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[15,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__iao true" data-index="[16,0]">iao</td>
                                            <td class=" td-pinyin__yao true" data-index="[16,1]">yao</td>
                                            <td class=" td-pinyin__biao true" data-index="[16,2]">biao</td>
                                            <td class=" td-pinyin__piao true" data-index="[16,3]">piao</td>
                                            <td class=" td-pinyin__miao true" data-index="[16,4]">miao</td>
                                            <td class=" td-pinyin__ null" data-index="[16,5]"></td>
                                            <td class=" td-pinyin__diao true" data-index="[16,6]">diao</td>
                                            <td class=" td-pinyin__tiao true" data-index="[16,7]">tiao</td>
                                            <td class=" td-pinyin__niao true" data-index="[16,8]">niao</td>
                                            <td class=" td-pinyin__liao true" data-index="[16,9]">liao</td>
                                            <td class=" td-pinyin__ null" data-index="[16,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[16,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[16,12]"></td>
                                            <td class=" td-pinyin__jiao true" data-index="[16,13]">jiao</td>
                                            <td class=" td-pinyin__qiao true" data-index="[16,14]">qiao</td>
                                            <td class=" td-pinyin__xiao true" data-index="[16,15]">xiao</td>
                                            <td class=" td-pinyin__ null" data-index="[16,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[16,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[16,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[16,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[16,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[16,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[16,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ie true" data-index="[17,0]">ie</td>
                                            <td class=" td-pinyin__ye true" data-index="[17,1]">ye</td>
                                            <td class=" td-pinyin__bie true" data-index="[17,2]">bie</td>
                                            <td class=" td-pinyin__pie true" data-index="[17,3]">pie</td>
                                            <td class=" td-pinyin__mie true" data-index="[17,4]">mie</td>
                                            <td class=" td-pinyin__ null" data-index="[17,5]"></td>
                                            <td class=" td-pinyin__die true" data-index="[17,6]">die</td>
                                            <td class=" td-pinyin__tie true" data-index="[17,7]">tie</td>
                                            <td class=" td-pinyin__nie true" data-index="[17,8]">nie</td>
                                            <td class=" td-pinyin__lie true" data-index="[17,9]">lie</td>
                                            <td class=" td-pinyin__ null" data-index="[17,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[17,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[17,12]"></td>
                                            <td class=" td-pinyin__jie true" data-index="[17,13]">jie</td>
                                            <td class=" td-pinyin__qie true" data-index="[17,14]">qie</td>
                                            <td class=" td-pinyin__xie true" data-index="[17,15]">xie</td>
                                            <td class=" td-pinyin__ null" data-index="[17,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[17,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[17,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[17,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[17,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[17,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[17,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__in true" data-index="[18,0]">in</td>
                                            <td class=" td-pinyin__yin true" data-index="[18,1]">yin</td>
                                            <td class=" td-pinyin__bin true" data-index="[18,2]">bin</td>
                                            <td class=" td-pinyin__pin true" data-index="[18,3]">pin</td>
                                            <td class=" td-pinyin__min true" data-index="[18,4]">min</td>
                                            <td class=" td-pinyin__ null" data-index="[18,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[18,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[18,7]"></td>
                                            <td class=" td-pinyin__nin true" data-index="[18,8]">nin</td>
                                            <td class=" td-pinyin__lin true" data-index="[18,9]">lin</td>
                                            <td class=" td-pinyin__ null" data-index="[18,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[18,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[18,12]"></td>
                                            <td class=" td-pinyin__jin true" data-index="[18,13]">jin</td>
                                            <td class=" td-pinyin__qin true" data-index="[18,14]">qin</td>
                                            <td class=" td-pinyin__xin true" data-index="[18,15]">xin</td>
                                            <td class=" td-pinyin__ null" data-index="[18,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[18,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[18,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[18,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[18,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[18,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[18,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ing true" data-index="[19,0]">ing</td>
                                            <td class=" td-pinyin__ying true" data-index="[19,1]">ying</td>
                                            <td class=" td-pinyin__bing true" data-index="[19,2]">bing</td>
                                            <td class=" td-pinyin__ping true" data-index="[19,3]">ping</td>
                                            <td class=" td-pinyin__ming true" data-index="[19,4]">ming</td>
                                            <td class=" td-pinyin__ null" data-index="[19,5]"></td>
                                            <td class=" td-pinyin__ding true" data-index="[19,6]">ding</td>
                                            <td class=" td-pinyin__ting true" data-index="[19,7]">ting</td>
                                            <td class=" td-pinyin__ning true" data-index="[19,8]">ning</td>
                                            <td class=" td-pinyin__ling true" data-index="[19,9]">ling</td>
                                            <td class=" td-pinyin__ null" data-index="[19,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[19,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[19,12]"></td>
                                            <td class=" td-pinyin__jing true" data-index="[19,13]">jing</td>
                                            <td class=" td-pinyin__qing true" data-index="[19,14]">qing</td>
                                            <td class=" td-pinyin__xing true" data-index="[19,15]">xing</td>
                                            <td class=" td-pinyin__ null" data-index="[19,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[19,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[19,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[19,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[19,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[19,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[19,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__iong true" data-index="[20,0]">iong</td>
                                            <td class=" td-pinyin__yong true" data-index="[20,1]">yong</td>
                                            <td class=" td-pinyin__ null" data-index="[20,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,7]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,8]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,9]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,12]"></td>
                                            <td class=" td-pinyin__jiong true" data-index="[20,13]">jiong</td>
                                            <td class=" td-pinyin__qiong true" data-index="[20,14]">qiong</td>
                                            <td class=" td-pinyin__xiong true" data-index="[20,15]">xiong</td>
                                            <td class=" td-pinyin__ null" data-index="[20,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[20,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__iou true" data-index="[21,0]">iou</td>
                                            <td class=" td-pinyin__you true" data-index="[21,1]">you</td>
                                            <td class=" td-pinyin__ null" data-index="[21,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[21,3]"></td>
                                            <td class=" td-pinyin__miu true" data-index="[21,4]">miu</td>
                                            <td class=" td-pinyin__ null" data-index="[21,5]"></td>
                                            <td class=" td-pinyin__diu true" data-index="[21,6]">diu</td>
                                            <td class=" td-pinyin__ null" data-index="[21,7]"></td>
                                            <td class=" td-pinyin__niu true" data-index="[21,8]">niu</td>
                                            <td class=" td-pinyin__liu true" data-index="[21,9]">liu</td>
                                            <td class=" td-pinyin__ null" data-index="[21,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[21,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[21,12]"></td>
                                            <td class=" td-pinyin__jiu true" data-index="[21,13]">jiu</td>
                                            <td class=" td-pinyin__qiu true" data-index="[21,14]">qiu</td>
                                            <td class=" td-pinyin__xiu true" data-index="[21,15]">xiu</td>
                                            <td class=" td-pinyin__ null" data-index="[21,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[21,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[21,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[21,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[21,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[21,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[21,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__o true" data-index="[22,0]">o</td>
                                            <td class=" td-pinyin__o true" data-index="[22,1]">o</td>
                                            <td class=" td-pinyin__bo true" data-index="[22,2]">bo</td>
                                            <td class=" td-pinyin__po true" data-index="[22,3]">po</td>
                                            <td class=" td-pinyin__mo true" data-index="[22,4]">mo</td>
                                            <td class=" td-pinyin__fo true" data-index="[22,5]">fo</td>
                                            <td class=" td-pinyin__ null" data-index="[22,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,7]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,8]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,9]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,12]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,15]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[22,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ong true" data-index="[23,0]">ong</td>
                                            <td class=" td-pinyin__ null" data-index="[23,1]"></td>
                                            <td class=" td-pinyin__ null" data-index="[23,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[23,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[23,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[23,5]"></td>
                                            <td class=" td-pinyin__dong true" data-index="[23,6]">dong</td>
                                            <td class=" td-pinyin__tong true" data-index="[23,7]">tong</td>
                                            <td class=" td-pinyin__nong true" data-index="[23,8]">nong</td>
                                            <td class=" td-pinyin__long true" data-index="[23,9]">long</td>
                                            <td class=" td-pinyin__gong true" data-index="[23,10]">gong</td>
                                            <td class=" td-pinyin__kong true" data-index="[23,11]">kong</td>
                                            <td class=" td-pinyin__hong true" data-index="[23,12]">hong</td>
                                            <td class=" td-pinyin__ null" data-index="[23,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[23,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[23,15]"></td>
                                            <td class=" td-pinyin__zong true" data-index="[23,16]">zong</td>
                                            <td class=" td-pinyin__cong true" data-index="[23,17]">cong</td>
                                            <td class=" td-pinyin__song true" data-index="[23,18]">song</td>
                                            <td class=" td-pinyin__zhong true" data-index="[23,19]">zhong</td>
                                            <td class=" td-pinyin__chong true" data-index="[23,20]">chong</td>
                                            <td class=" td-pinyin__ null" data-index="[23,21]"></td>
                                            <td class=" td-pinyin__rong true" data-index="[23,22]">rong</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ou true" data-index="[24,0]">ou</td>
                                            <td class=" td-pinyin__ou true" data-index="[24,1]">ou</td>
                                            <td class=" td-pinyin__ null" data-index="[24,2]"></td>
                                            <td class=" td-pinyin__pou true" data-index="[24,3]">pou</td>
                                            <td class=" td-pinyin__mou true" data-index="[24,4]">mou</td>
                                            <td class=" td-pinyin__fou true" data-index="[24,5]">fou</td>
                                            <td class=" td-pinyin__dou true" data-index="[24,6]">dou</td>
                                            <td class=" td-pinyin__tou true" data-index="[24,7]">tou</td>
                                            <td class=" td-pinyin__ null" data-index="[24,8]"></td>
                                            <td class=" td-pinyin__lou true" data-index="[24,9]">lou</td>
                                            <td class=" td-pinyin__gou true" data-index="[24,10]">gou</td>
                                            <td class=" td-pinyin__kou true" data-index="[24,11]">kou</td>
                                            <td class=" td-pinyin__hou true" data-index="[24,12]">hou</td>
                                            <td class=" td-pinyin__ null" data-index="[24,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[24,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[24,15]"></td>
                                            <td class=" td-pinyin__zou true" data-index="[24,16]">zou</td>
                                            <td class=" td-pinyin__cou true" data-index="[24,17]">cou</td>
                                            <td class=" td-pinyin__sou true" data-index="[24,18]">sou</td>
                                            <td class=" td-pinyin__zhou true" data-index="[24,19]">zhou</td>
                                            <td class=" td-pinyin__chou true" data-index="[24,20]">chou</td>
                                            <td class=" td-pinyin__shou true" data-index="[24,21]">shou</td>
                                            <td class=" td-pinyin__rou true" data-index="[24,22]">rou</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__u true" data-index="[25,0]">u</td>
                                            <td class=" td-pinyin__wu true" data-index="[25,1]">wu</td>
                                            <td class=" td-pinyin__bu true" data-index="[25,2]">bu</td>
                                            <td class=" td-pinyin__pu true" data-index="[25,3]">pu</td>
                                            <td class=" td-pinyin__mu true" data-index="[25,4]">mu</td>
                                            <td class=" td-pinyin__fu true" data-index="[25,5]">fu</td>
                                            <td class=" td-pinyin__du true" data-index="[25,6]">du</td>
                                            <td class=" td-pinyin__tu true" data-index="[25,7]">tu</td>
                                            <td class=" td-pinyin__nu true" data-index="[25,8]">nu</td>
                                            <td class=" td-pinyin__lu true" data-index="[25,9]">lu</td>
                                            <td class=" td-pinyin__gu true" data-index="[25,10]">gu</td>
                                            <td class=" td-pinyin__ku true" data-index="[25,11]">ku</td>
                                            <td class=" td-pinyin__hu true" data-index="[25,12]">hu</td>
                                            <td class=" td-pinyin__ null" data-index="[25,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[25,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[25,15]"></td>
                                            <td class=" td-pinyin__zu true" data-index="[25,16]">zu</td>
                                            <td class=" td-pinyin__cu true" data-index="[25,17]">cu</td>
                                            <td class=" td-pinyin__su true" data-index="[25,18]">su</td>
                                            <td class=" td-pinyin__zhu true" data-index="[25,19]">zhu</td>
                                            <td class=" td-pinyin__chu true" data-index="[25,20]">chu</td>
                                            <td class=" td-pinyin__shu true" data-index="[25,21]">shu</td>
                                            <td class=" td-pinyin__ru true" data-index="[25,22]">ru</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ua true" data-index="[26,0]">ua</td>
                                            <td class=" td-pinyin__wa true" data-index="[26,1]">wa</td>
                                            <td class=" td-pinyin__ null" data-index="[26,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[26,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[26,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[26,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[26,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[26,7]"></td>
                                            <td class=" td-pinyin__ null" data-index="[26,8]"></td>
                                            <td class=" td-pinyin__ null" data-index="[26,9]"></td>
                                            <td class=" td-pinyin__gua true" data-index="[26,10]">gua</td>
                                            <td class=" td-pinyin__kua true" data-index="[26,11]">kua</td>
                                            <td class=" td-pinyin__hua true" data-index="[26,12]">hua</td>
                                            <td class=" td-pinyin__ null" data-index="[26,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[26,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[26,15]"></td>
                                            <td class=" td-pinyin__ null" data-index="[26,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[26,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[26,18]"></td>
                                            <td class=" td-pinyin__zhua true" data-index="[26,19]">zhua</td>
                                            <td class=" td-pinyin__ null" data-index="[26,20]"></td>
                                            <td class=" td-pinyin__shua true" data-index="[26,21]">shua</td>
                                            <td class=" td-pinyin__ null" data-index="[26,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__uai true" data-index="[27,0]">uai</td>
                                            <td class=" td-pinyin__wai true" data-index="[27,1]">wai</td>
                                            <td class=" td-pinyin__ null" data-index="[27,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[27,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[27,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[27,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[27,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[27,7]"></td>
                                            <td class=" td-pinyin__ null" data-index="[27,8]"></td>
                                            <td class=" td-pinyin__ null" data-index="[27,9]"></td>
                                            <td class=" td-pinyin__guai true" data-index="[27,10]">guai</td>
                                            <td class=" td-pinyin__kuai true" data-index="[27,11]">kuai</td>
                                            <td class=" td-pinyin__huai true" data-index="[27,12]">huai</td>
                                            <td class=" td-pinyin__ null" data-index="[27,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[27,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[27,15]"></td>
                                            <td class=" td-pinyin__ null" data-index="[27,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[27,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[27,18]"></td>
                                            <td class=" td-pinyin__zhuai true" data-index="[27,19]">zhuai</td>
                                            <td class=" td-pinyin__chuai true" data-index="[27,20]">chuai</td>
                                            <td class=" td-pinyin__shuai true" data-index="[27,21]">shuai</td>
                                            <td class=" td-pinyin__ null" data-index="[27,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__uan true" data-index="[28,0]">uan</td>
                                            <td class=" td-pinyin__wan true" data-index="[28,1]">wan</td>
                                            <td class=" td-pinyin__ null" data-index="[28,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[28,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[28,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[28,5]"></td>
                                            <td class=" td-pinyin__duan true" data-index="[28,6]">duan</td>
                                            <td class=" td-pinyin__tuan true" data-index="[28,7]">tuan</td>
                                            <td class=" td-pinyin__nuan true" data-index="[28,8]">nuan</td>
                                            <td class=" td-pinyin__luan true" data-index="[28,9]">luan</td>
                                            <td class=" td-pinyin__guan true" data-index="[28,10]">guan</td>
                                            <td class=" td-pinyin__kuan true" data-index="[28,11]">kuan</td>
                                            <td class=" td-pinyin__huan true" data-index="[28,12]">huan</td>
                                            <td class=" td-pinyin__ null" data-index="[28,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[28,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[28,15]"></td>
                                            <td class=" td-pinyin__zuan true" data-index="[28,16]">zuan</td>
                                            <td class=" td-pinyin__cuan true" data-index="[28,17]">cuan</td>
                                            <td class=" td-pinyin__suan true" data-index="[28,18]">suan</td>
                                            <td class=" td-pinyin__zhuan true" data-index="[28,19]">zhuan</td>
                                            <td class=" td-pinyin__chuan true" data-index="[28,20]">chuan</td>
                                            <td class=" td-pinyin__shuan true" data-index="[28,21]">shuan</td>
                                            <td class=" td-pinyin__ruan true" data-index="[28,22]">ruan</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__uang true" data-index="[29,0]">uang</td>
                                            <td class=" td-pinyin__wang true" data-index="[29,1]">wang</td>
                                            <td class=" td-pinyin__ null" data-index="[29,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[29,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[29,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[29,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[29,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[29,7]"></td>
                                            <td class=" td-pinyin__ null" data-index="[29,8]"></td>
                                            <td class=" td-pinyin__ null" data-index="[29,9]"></td>
                                            <td class=" td-pinyin__guang true" data-index="[29,10]">guang</td>
                                            <td class=" td-pinyin__kuang true" data-index="[29,11]">kuang</td>
                                            <td class=" td-pinyin__huang true" data-index="[29,12]">huang</td>
                                            <td class=" td-pinyin__ null" data-index="[29,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[29,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[29,15]"></td>
                                            <td class=" td-pinyin__ null" data-index="[29,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[29,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[29,18]"></td>
                                            <td class=" td-pinyin__zhuang true" data-index="[29,19]">zhuang</td>
                                            <td class=" td-pinyin__chuang true" data-index="[29,20]">chuang</td>
                                            <td class=" td-pinyin__shuang true" data-index="[29,21]">shuang</td>
                                            <td class=" td-pinyin__ null" data-index="[29,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__uei true" data-index="[30,0]">uei</td>
                                            <td class=" td-pinyin__wei true" data-index="[30,1]">wei</td>
                                            <td class=" td-pinyin__ null" data-index="[30,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[30,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[30,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[30,5]"></td>
                                            <td class=" td-pinyin__dui true" data-index="[30,6]">dui</td>
                                            <td class=" td-pinyin__tui true" data-index="[30,7]">tui</td>
                                            <td class=" td-pinyin__ null" data-index="[30,8]"></td>
                                            <td class=" td-pinyin__ null" data-index="[30,9]"></td>
                                            <td class=" td-pinyin__gui true" data-index="[30,10]">gui</td>
                                            <td class=" td-pinyin__kui true" data-index="[30,11]">kui</td>
                                            <td class=" td-pinyin__hui true" data-index="[30,12]">hui</td>
                                            <td class=" td-pinyin__ null" data-index="[30,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[30,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[30,15]"></td>
                                            <td class=" td-pinyin__zui true" data-index="[30,16]">zui</td>
                                            <td class=" td-pinyin__cui true" data-index="[30,17]">cui</td>
                                            <td class=" td-pinyin__sui true" data-index="[30,18]">sui</td>
                                            <td class=" td-pinyin__zhui true" data-index="[30,19]">zhui</td>
                                            <td class=" td-pinyin__chui true" data-index="[30,20]">chui</td>
                                            <td class=" td-pinyin__shui true" data-index="[30,21]">shui</td>
                                            <td class=" td-pinyin__rui true" data-index="[30,22]">rui</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__uen true" data-index="[31,0]">uen</td>
                                            <td class=" td-pinyin__wen true" data-index="[31,1]">wen</td>
                                            <td class=" td-pinyin__ null" data-index="[31,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[31,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[31,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[31,5]"></td>
                                            <td class=" td-pinyin__dun true" data-index="[31,6]">dun</td>
                                            <td class=" td-pinyin__tun true" data-index="[31,7]">tun</td>
                                            <td class=" td-pinyin__ null" data-index="[31,8]"></td>
                                            <td class=" td-pinyin__lun true" data-index="[31,9]">lun</td>
                                            <td class=" td-pinyin__gun true" data-index="[31,10]">gun</td>
                                            <td class=" td-pinyin__kun true" data-index="[31,11]">kun</td>
                                            <td class=" td-pinyin__hun true" data-index="[31,12]">hun</td>
                                            <td class=" td-pinyin__ null" data-index="[31,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[31,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[31,15]"></td>
                                            <td class=" td-pinyin__zun true" data-index="[31,16]">zun</td>
                                            <td class=" td-pinyin__cun true" data-index="[31,17]">cun</td>
                                            <td class=" td-pinyin__sun true" data-index="[31,18]">sun</td>
                                            <td class=" td-pinyin__zhun true" data-index="[31,19]">zhun</td>
                                            <td class=" td-pinyin__chun true" data-index="[31,20]">chun</td>
                                            <td class=" td-pinyin__shun true" data-index="[31,21]">shun</td>
                                            <td class=" td-pinyin__run true" data-index="[31,22]">run</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ueng true" data-index="[32,0]">ueng</td>
                                            <td class=" td-pinyin__weng true" data-index="[32,1]">weng</td>
                                            <td class=" td-pinyin__ null" data-index="[32,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,7]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,8]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,9]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,12]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,15]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[32,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__uo true" data-index="[33,0]">uo</td>
                                            <td class=" td-pinyin__wo true" data-index="[33,1]">wo</td>
                                            <td class=" td-pinyin__ null" data-index="[33,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[33,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[33,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[33,5]"></td>
                                            <td class=" td-pinyin__duo true" data-index="[33,6]">duo</td>
                                            <td class=" td-pinyin__tuo true" data-index="[33,7]">tuo</td>
                                            <td class=" td-pinyin__nuo true" data-index="[33,8]">nuo</td>
                                            <td class=" td-pinyin__luo true" data-index="[33,9]">luo</td>
                                            <td class=" td-pinyin__guo true" data-index="[33,10]">guo</td>
                                            <td class=" td-pinyin__kuo true" data-index="[33,11]">kuo</td>
                                            <td class=" td-pinyin__huo true" data-index="[33,12]">huo</td>
                                            <td class=" td-pinyin__ null" data-index="[33,13]"></td>
                                            <td class=" td-pinyin__ null" data-index="[33,14]"></td>
                                            <td class=" td-pinyin__ null" data-index="[33,15]"></td>
                                            <td class=" td-pinyin__zuo true" data-index="[33,16]">zuo</td>
                                            <td class=" td-pinyin__cuo true" data-index="[33,17]">cuo</td>
                                            <td class=" td-pinyin__suo true" data-index="[33,18]">suo</td>
                                            <td class=" td-pinyin__zhuo true" data-index="[33,19]">zhuo</td>
                                            <td class=" td-pinyin__chuo true" data-index="[33,20]">chuo</td>
                                            <td class=" td-pinyin__shuo true" data-index="[33,21]">shuo</td>
                                            <td class=" td-pinyin__ruo true" data-index="[33,22]">ruo</td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ü true" data-index="[34,0]">ü</td>
                                            <td class=" td-pinyin__yu true" data-index="[34,1]">yu</td>
                                            <td class=" td-pinyin__ null" data-index="[34,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,7]"></td>
                                            <td class=" td-pinyin__nü true" data-index="[34,8]">nü</td>
                                            <td class=" td-pinyin__lü true" data-index="[34,9]">lü</td>
                                            <td class=" td-pinyin__ null" data-index="[34,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,12]"></td>
                                            <td class=" td-pinyin__ju true" data-index="[34,13]">ju</td>
                                            <td class=" td-pinyin__qu true" data-index="[34,14]">qu</td>
                                            <td class=" td-pinyin__xu true" data-index="[34,15]">xu</td>
                                            <td class=" td-pinyin__ null" data-index="[34,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[34,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__üan true" data-index="[35,0]">üan</td>
                                            <td class=" td-pinyin__yuan true" data-index="[35,1]">yuan</td>
                                            <td class=" td-pinyin__ null" data-index="[35,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,7]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,8]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,9]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,12]"></td>
                                            <td class=" td-pinyin__juan true" data-index="[35,13]">juan</td>
                                            <td class=" td-pinyin__quan true" data-index="[35,14]">quan</td>
                                            <td class=" td-pinyin__xuan true" data-index="[35,15]">xuan</td>
                                            <td class=" td-pinyin__ null" data-index="[35,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[35,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__üe true" data-index="[36,0]">üe</td>
                                            <td class=" td-pinyin__yue true" data-index="[36,1]">yue</td>
                                            <td class=" td-pinyin__ null" data-index="[36,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,7]"></td>
                                            <td class=" td-pinyin__nüe true" data-index="[36,8]">nüe</td>
                                            <td class=" td-pinyin__lüe true" data-index="[36,9]">lüe</td>
                                            <td class=" td-pinyin__ null" data-index="[36,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,12]"></td>
                                            <td class=" td-pinyin__jue true" data-index="[36,13]">jue</td>
                                            <td class=" td-pinyin__que true" data-index="[36,14]">que</td>
                                            <td class=" td-pinyin__xue true" data-index="[36,15]">xue</td>
                                            <td class=" td-pinyin__ null" data-index="[36,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[36,22]"></td>
                                        </tr>
                                        <tr class="">
                                            <td class="td-cell td-pinyin__ün true" data-index="[37,0]">ün</td>
                                            <td class=" td-pinyin__yun true" data-index="[37,1]">yun</td>
                                            <td class=" td-pinyin__ null" data-index="[37,2]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,3]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,4]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,5]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,6]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,7]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,8]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,9]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,10]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,11]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,12]"></td>
                                            <td class=" td-pinyin__jun true" data-index="[37,13]">jun</td>
                                            <td class=" td-pinyin__qun true" data-index="[37,14]">qun</td>
                                            <td class=" td-pinyin__xun true" data-index="[37,15]">xun</td>
                                            <td class=" td-pinyin__ null" data-index="[37,16]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,17]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,18]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,19]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,20]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,21]"></td>
                                            <td class=" td-pinyin__ null" data-index="[37,22]"></td>
                                        </tr>
                                    </table>
                                    <div class="popover popover-container">
                                        <div class="pop_header">
                                            <div><span class="pinyin_var"></span>
                                                <span class="pinyin_join"></span>
                                            </div>
                                            <button class="yy-button yy-button--text
                          close-pinyin-pop">
            <img class="close-button" src="https://yoyochinese.com/public/images/tools/<EMAIL>" width="24px"></button>
                                        </div>
                                        <div class="pop-contanet">
                                            <div class="tones"> </div>
                                            <div class="pinyin-video-container">
                                                <!--<div class="mack">-->
                                                <!--    <div>Sign up/log in for</div>-->
                                                <!--    <div>more demo videos</div>-->
                                                <!--</div>-->
                                                <iframe></iframe>
                                            </div>
                                            <!--<p class="login-tips"><a href="#">Sign up</a> to check all videos</p>-->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <section class="faq-section pinyin-faq">
                            <h2>What is <span class="em-orange">Pinyin?</span></h2>
                            <p>The word <b>pīnyīn</b> (拼音) in Chinese literally means "spell-sounds". <b>It's the most commonly used system for transcribing or or spelling out the Chinese sounds</b>, and it uses the letters in the Latin alphabet that you
                                are already familiar with.</p>
                            <p>Pinyin is an extremely useful tool and should be the foundation of your Chinese learning. Plus, it's not just for Chinese learners like yourself. Actually, all native Chinese speakers know pinyin - it's the first thing Chinese
                                children learn at school before learning characters. So even if you don't know Chinese characters, you can type in pinyin and Chinese people will understand.</p>
                            <p><b>Every single sound that exists in Chinese can be displayed easily in a pinyin chart like the one above.</b> Once you master these 409 sounds along with the tones, you'll have practically mastered the pronunciation of every
                                single word in the Chinese language. And even better, <b>if you know English you should already know how to correctly pronounce over half of those sounds without any coaching!</b> As for the rest, that's what we're here
                                for! We've built this interactive pinyin chart (above) with demonstrations for how to correctly pronounce every possible sound in the Chinese language. We've also created a series of video lessons (below) with practice
                                tools that enable you to record yourself and listen back to it to ensure your pronunciation matches the examples. Combined with our Tone Pairs practice tool, you now have <b>everything you need to master pinyin, tones, and Chinese pronunciation.</b></p>
                        </section>
                       

                    </div>
                </div>
            </div>
        </main>
  


    </div>


    <script src="https://yoyochinese.com/public/libs/jquery-3.6.0.min.js" async=""></script>
    <script src="https://yoyochinese.com/public/libs/accordion.js"></script>
    <script>
        window.yoyoData = {
            config: {
                "YOYO_ENV": "production",
                "SITE_URL": "https://yoyochinese.com",
                "API_PATH": "https://yoyochinese.com/api/v1",
                "API_VERSION": "3.0.0",
                "ASSETS_PATH": "https://yoyochinese.com/public",
                "STRIPE_PUBLISHABLE_KEY": "pk_live_51ITrVcDL7AHDQHPzOZubYMs9bFWsbu7aT7CtBxj4WkGi2NKrLXCDx62WpaXjdRh6fN0VX3YIvqgmtALTLs5tgLxB00qyKX17hV",
                "SALE": "false"
            }
        }
    </script>
    
    <script>
        (() => {
            (function() {
                let a = window.location.search;
                new URLSearchParams(a).has("ref") && fetch(`https://yoyochinese.com/createref${window.location.search}`)
            })();
        })();
    </script>
    <script>
        (() => {
            (function() {
                function a(t) {
                    var e;
                    ((e = t == null ? void 0 : t.tagName) == null ? void 0 : e.toLowerCase()) === "audio" && t.play()
                }

                function o(t) {
                    t.target.tagName.toLowerCase() === "img" ? a(t.target.nextSibling) : t.target.classList.contains("audio-label") && a(t.target.previousSibling)
                }
                document.addEventListener("mousedown", o)
            })();
        })();
    </script>

  <script>
      /*! lazysizes - v5.3.2 */

! function(e) {
    var t = function(u, D, f) {
        "use strict";
        var k, H;
        if (function() {
                var e;
                var t = {
                    lazyClass: "lazyload",
                    loadedClass: "lazyloaded",
                    loadingClass: "lazyloading",
                    preloadClass: "lazypreload",
                    errorClass: "lazyerror",
                    autosizesClass: "lazyautosizes",
                    fastLoadedClass: "ls-is-cached",
                    iframeLoadMode: 0,
                    srcAttr: "data-src",
                    srcsetAttr: "data-srcset",
                    sizesAttr: "data-sizes",
                    minSize: 40,
                    customMedia: {},
                    init: true,
                    expFactor: 1.5,
                    hFac: .8,
                    loadMode: 2,
                    loadHidden: true,
                    ricTimeout: 0,
                    throttleDelay: 125
                };
                H = u.lazySizesConfig || u.lazysizesConfig || {};
                for (e in t) {
                    if (!(e in H)) {
                        H[e] = t[e]
                    }
                }
            }(), !D || !D.getElementsByClassName) {
            return {
                init: function() {},
                cfg: H,
                noSupport: true
            }
        }
        var O = D.documentElement,
            i = u.HTMLPictureElement,
            P = "addEventListener",
            $ = "getAttribute",
            q = u[P].bind(u),
            I = u.setTimeout,
            U = u.requestAnimationFrame || I,
            o = u.requestIdleCallback,
            j = /^picture$/i,
            r = ["load", "error", "lazyincluded", "_lazyloaded"],
            a = {},
            G = Array.prototype.forEach,
            J = function(e, t) {
                if (!a[t]) {
                    a[t] = new RegExp("(\\s|^)" + t + "(\\s|$)")
                }
                return a[t].test(e[$]("class") || "") && a[t]
            },
            K = function(e, t) {
                if (!J(e, t)) {
                    e.setAttribute("class", (e[$]("class") || "").trim() + " " + t)
                }
            },
            Q = function(e, t) {
                var a;
                if (a = J(e, t)) {
                    e.setAttribute("class", (e[$]("class") || "").replace(a, " "))
                }
            },
            V = function(t, a, e) {
                var i = e ? P : "removeEventListener";
                if (e) {
                    V(t, a)
                }
                r.forEach(function(e) {
                    t[i](e, a)
                })
            },
            X = function(e, t, a, i, r) {
                var n = D.createEvent("Event");
                if (!a) {
                    a = {}
                }
                a.instance = k;
                n.initEvent(t, !i, !r);
                n.detail = a;
                e.dispatchEvent(n);
                return n
            },
            Y = function(e, t) {
                var a;
                if (!i && (a = u.picturefill || H.pf)) {
                    if (t && t.src && !e[$]("srcset")) {
                        e.setAttribute("srcset", t.src)
                    }
                    a({
                        reevaluate: true,
                        elements: [e]
                    })
                } else if (t && t.src) {
                    e.src = t.src
                }
            },
            Z = function(e, t) {
                return (getComputedStyle(e, null) || {})[t]
            },
            s = function(e, t, a) {
                a = a || e.offsetWidth;
                while (a < H.minSize && t && !e._lazysizesWidth) {
                    a = t.offsetWidth;
                    t = t.parentNode
                }
                return a
            },
            ee = function() {
                var a, i;
                var t = [];
                var r = [];
                var n = t;
                var s = function() {
                    var e = n;
                    n = t.length ? r : t;
                    a = true;
                    i = false;
                    while (e.length) {
                        e.shift()()
                    }
                    a = false
                };
                var e = function(e, t) {
                    if (a && !t) {
                        e.apply(this, arguments)
                    } else {
                        n.push(e);
                        if (!i) {
                            i = true;
                            (D.hidden ? I : U)(s)
                        }
                    }
                };
                e._lsFlush = s;
                return e
            }(),
            te = function(a, e) {
                return e ? function() {
                    ee(a)
                } : function() {
                    var e = this;
                    var t = arguments;
                    ee(function() {
                        a.apply(e, t)
                    })
                }
            },
            ae = function(e) {
                var a;
                var i = 0;
                var r = H.throttleDelay;
                var n = H.ricTimeout;
                var t = function() {
                    a = false;
                    i = f.now();
                    e()
                };
                var s = o && n > 49 ? function() {
                    o(t, {
                        timeout: n
                    });
                    if (n !== H.ricTimeout) {
                        n = H.ricTimeout
                    }
                } : te(function() {
                    I(t)
                }, true);
                return function(e) {
                    var t;
                    if (e = e === true) {
                        n = 33
                    }
                    if (a) {
                        return
                    }
                    a = true;
                    t = r - (f.now() - i);
                    if (t < 0) {
                        t = 0
                    }
                    if (e || t < 9) {
                        s()
                    } else {
                        I(s, t)
                    }
                }
            },
            ie = function(e) {
                var t, a;
                var i = 99;
                var r = function() {
                    t = null;
                    e()
                };
                var n = function() {
                    var e = f.now() - a;
                    if (e < i) {
                        I(n, i - e)
                    } else {
                        (o || r)(r)
                    }
                };
                return function() {
                    a = f.now();
                    if (!t) {
                        t = I(n, i)
                    }
                }
            },
            e = function() {
                var v, m, c, h, e;
                var y, z, g, p, C, b, A;
                var n = /^img$/i;
                var d = /^iframe$/i;
                var E = "onscroll" in u && !/(gle|ing)bot/.test(navigator.userAgent);
                var _ = 0;
                var w = 0;
                var M = 0;
                var N = -1;
                var L = function(e) {
                    M--;
                    if (!e || M < 0 || !e.target) {
                        M = 0
                    }
                };
                var x = function(e) {
                    if (A == null) {
                        A = Z(D.body, "visibility") == "hidden"
                    }
                    return A || !(Z(e.parentNode, "visibility") == "hidden" && Z(e, "visibility") == "hidden")
                };
                var W = function(e, t) {
                    var a;
                    var i = e;
                    var r = x(e);
                    g -= t;
                    b += t;
                    p -= t;
                    C += t;
                    while (r && (i = i.offsetParent) && i != D.body && i != O) {
                        r = (Z(i, "opacity") || 1) > 0;
                        if (r && Z(i, "overflow") != "visible") {
                            a = i.getBoundingClientRect();
                            r = C > a.left && p < a.right && b > a.top - 1 && g < a.bottom + 1
                        }
                    }
                    return r
                };
                var t = function() {
                    var e, t, a, i, r, n, s, o, l, u, f, c;
                    var d = k.elements;
                    if ((h = H.loadMode) && M < 8 && (e = d.length)) {
                        t = 0;
                        N++;
                        for (; t < e; t++) {
                            if (!d[t] || d[t]._lazyRace) {
                                continue
                            }
                            if (!E || k.prematureUnveil && k.prematureUnveil(d[t])) {
                                R(d[t]);
                                continue
                            }
                            if (!(o = d[t][$]("data-expand")) || !(n = o * 1)) {
                                n = w
                            }
                            if (!u) {
                                u = !H.expand || H.expand < 1 ? O.clientHeight > 500 && O.clientWidth > 500 ? 500 : 370 : H.expand;
                                k._defEx = u;
                                f = u * H.expFactor;
                                c = H.hFac;
                                A = null;
                                if (w < f && M < 1 && N > 2 && h > 2 && !D.hidden) {
                                    w = f;
                                    N = 0
                                } else if (h > 1 && N > 1 && M < 6) {
                                    w = u
                                } else {
                                    w = _
                                }
                            }
                            if (l !== n) {
                                y = innerWidth + n * c;
                                z = innerHeight + n;
                                s = n * -1;
                                l = n
                            }
                            a = d[t].getBoundingClientRect();
                            if ((b = a.bottom) >= s && (g = a.top) <= z && (C = a.right) >= s * c && (p = a.left) <= y && (b || C || p || g) && (H.loadHidden || x(d[t])) && (m && M < 3 && !o && (h < 3 || N < 4) || W(d[t], n))) {
                                R(d[t]);
                                r = true;
                                if (M > 9) {
                                    break
                                }
                            } else if (!r && m && !i && M < 4 && N < 4 && h > 2 && (v[0] || H.preloadAfterLoad) && (v[0] || !o && (b || C || p || g || d[t][$](H.sizesAttr) != "auto"))) {
                                i = v[0] || d[t]
                            }
                        }
                        if (i && !r) {
                            R(i)
                        }
                    }
                };
                var a = ae(t);
                var S = function(e) {
                    var t = e.target;
                    if (t._lazyCache) {
                        delete t._lazyCache;
                        return
                    }
                    L(e);
                    K(t, H.loadedClass);
                    Q(t, H.loadingClass);
                    V(t, B);
                    X(t, "lazyloaded")
                };
                var i = te(S);
                var B = function(e) {
                    i({
                        target: e.target
                    })
                };
                var T = function(e, t) {
                    var a = e.getAttribute("data-load-mode") || H.iframeLoadMode;
                    if (a == 0) {
                        e.contentWindow.location.replace(t)
                    } else if (a == 1) {
                        e.src = t
                    }
                };
                var F = function(e) {
                    var t;
                    var a = e[$](H.srcsetAttr);
                    if (t = H.customMedia[e[$]("data-media") || e[$]("media")]) {
                        e.setAttribute("media", t)
                    }
                    if (a) {
                        e.setAttribute("srcset", a)
                    }
                };
                var s = te(function(t, e, a, i, r) {
                    var n, s, o, l, u, f;
                    if (!(u = X(t, "lazybeforeunveil", e)).defaultPrevented) {
                        if (i) {
                            if (a) {
                                K(t, H.autosizesClass)
                            } else {
                                t.setAttribute("sizes", i)
                            }
                        }
                        s = t[$](H.srcsetAttr);
                        n = t[$](H.srcAttr);
                        if (r) {
                            o = t.parentNode;
                            l = o && j.test(o.nodeName || "")
                        }
                        f = e.firesLoad || "src" in t && (s || n || l);
                        u = {
                            target: t
                        };
                        K(t, H.loadingClass);
                        if (f) {
                            clearTimeout(c);
                            c = I(L, 2500);
                            V(t, B, true)
                        }
                        if (l) {
                            G.call(o.getElementsByTagName("source"), F)
                        }
                        if (s) {
                            t.setAttribute("srcset", s)
                        } else if (n && !l) {
                            if (d.test(t.nodeName)) {
                                T(t, n)
                            } else {
                                t.src = n
                            }
                        }
                        if (r && (s || l)) {
                            Y(t, {
                                src: n
                            })
                        }
                    }
                    if (t._lazyRace) {
                        delete t._lazyRace
                    }
                    Q(t, H.lazyClass);
                    ee(function() {
                        var e = t.complete && t.naturalWidth > 1;
                        if (!f || e) {
                            if (e) {
                                K(t, H.fastLoadedClass)
                            }
                            S(u);
                            t._lazyCache = true;
                            I(function() {
                                if ("_lazyCache" in t) {
                                    delete t._lazyCache
                                }
                            }, 9)
                        }
                        if (t.loading == "lazy") {
                            M--
                        }
                    }, true)
                });
                var R = function(e) {
                    if (e._lazyRace) {
                        return
                    }
                    var t;
                    var a = n.test(e.nodeName);
                    var i = a && (e[$](H.sizesAttr) || e[$]("sizes"));
                    var r = i == "auto";
                    if ((r || !m) && a && (e[$]("src") || e.srcset) && !e.complete && !J(e, H.errorClass) && J(e, H.lazyClass)) {
                        return
                    }
                    t = X(e, "lazyunveilread").detail;
                    if (r) {
                        re.updateElem(e, true, e.offsetWidth)
                    }
                    e._lazyRace = true;
                    M++;
                    s(e, t, r, i, a)
                };
                var r = ie(function() {
                    H.loadMode = 3;
                    a()
                });
                var o = function() {
                    if (H.loadMode == 3) {
                        H.loadMode = 2
                    }
                    r()
                };
                var l = function() {
                    if (m) {
                        return
                    }
                    if (f.now() - e < 999) {
                        I(l, 999);
                        return
                    }
                    m = true;
                    H.loadMode = 3;
                    a();
                    q("scroll", o, true)
                };
                return {
                    _: function() {
                        e = f.now();
                        k.elements = D.getElementsByClassName(H.lazyClass);
                        v = D.getElementsByClassName(H.lazyClass + " " + H.preloadClass);
                        q("scroll", a, true);
                        q("resize", a, true);
                        q("pageshow", function(e) {
                            if (e.persisted) {
                                var t = D.querySelectorAll("." + H.loadingClass);
                                if (t.length && t.forEach) {
                                    U(function() {
                                        t.forEach(function(e) {
                                            if (e.complete) {
                                                R(e)
                                            }
                                        })
                                    })
                                }
                            }
                        });
                        if (u.MutationObserver) {
                            new MutationObserver(a).observe(O, {
                                childList: true,
                                subtree: true,
                                attributes: true
                            })
                        } else {
                            O[P]("DOMNodeInserted", a, true);
                            O[P]("DOMAttrModified", a, true);
                            setInterval(a, 999)
                        }
                        q("hashchange", a, true);
                        ["focus", "mouseover", "click", "load", "transitionend", "animationend"].forEach(function(e) {
                            D[P](e, a, true)
                        });
                        if (/d$|^c/.test(D.readyState)) {
                            l()
                        } else {
                            q("load", l);
                            D[P]("DOMContentLoaded", a);
                            I(l, 2e4)
                        }
                        if (k.elements.length) {
                            t();
                            ee._lsFlush()
                        } else {
                            a()
                        }
                    },
                    checkElems: a,
                    unveil: R,
                    _aLSL: o
                }
            }(),
            re = function() {
                var a;
                var n = te(function(e, t, a, i) {
                    var r, n, s;
                    e._lazysizesWidth = i;
                    i += "px";
                    e.setAttribute("sizes", i);
                    if (j.test(t.nodeName || "")) {
                        r = t.getElementsByTagName("source");
                        for (n = 0, s = r.length; n < s; n++) {
                            r[n].setAttribute("sizes", i)
                        }
                    }
                    if (!a.detail.dataAttr) {
                        Y(e, a.detail)
                    }
                });
                var i = function(e, t, a) {
                    var i;
                    var r = e.parentNode;
                    if (r) {
                        a = s(e, r, a);
                        i = X(e, "lazybeforesizes", {
                            width: a,
                            dataAttr: !!t
                        });
                        if (!i.defaultPrevented) {
                            a = i.detail.width;
                            if (a && a !== e._lazysizesWidth) {
                                n(e, r, i, a)
                            }
                        }
                    }
                };
                var e = function() {
                    var e;
                    var t = a.length;
                    if (t) {
                        e = 0;
                        for (; e < t; e++) {
                            i(a[e])
                        }
                    }
                };
                var t = ie(e);
                return {
                    _: function() {
                        a = D.getElementsByClassName(H.autosizesClass);
                        q("resize", t)
                    },
                    checkElems: t,
                    updateElem: i
                }
            }(),
            t = function() {
                if (!t.i && D.getElementsByClassName) {
                    t.i = true;
                    re._();
                    e._()
                }
            };
        return I(function() {
            H.init && t()
        }), k = {
            cfg: H,
            autoSizer: re,
            loader: e,
            init: t,
            uP: Y,
            aC: K,
            rC: Q,
            hC: J,
            fire: X,
            gW: s,
            rAF: ee
        }
    }(e, e.document, Date);
    e.lazySizes = t, "object" == typeof module && module.exports && (module.exports = t)
}("undefined" != typeof window ? window : {});
  </script>
  
  <script>
      (() => {
    var f = (r, a, l) => new Promise((d, u) => {
        var t = e => {
                try {
                    c(l.next(e))
                } catch (n) {
                    u(n)
                }
            },
            o = e => {
                try {
                    c(l.throw(e))
                } catch (n) {
                    u(n)
                }
            },
            c = e => e.done ? d(e.value) : Promise.resolve(e.value).then(t, o);
        c((l = l.apply(r, a)).next())
    });
    (function() {
        function r(t, o, c, e) {
            let n = document.querySelector(c);
            n && (n.value = ""), document.querySelectorAll(".newsletter_notification").forEach(i => {
                let s = document.createElement("p");
                s.classList.add("newsletter_result"), s.textContent = o, s.style.color = t ? "#63b768" : "#fc4b49", s.style.fontWeight = "700", i.innerHTML = "", i.append(s)
            });
            let m = document.querySelector(e);
            m && m.removeAttribute("disabled")
        }

        function a(t, o) {
            let c = document.querySelector(t),
                e = document.querySelector(o);
            if (!c) return;
            let n = c.value,
                m = "https://yoyochinese.com/api/v1/newsletter-signup";
            n && (e && e.setAttribute("disabled", ""), document.querySelectorAll(".newsletter_notification").forEach(i => i.innerHTML = ""), fetch(m, {
                method: "POST",
                credentials: "include",
                headers: {
                    "content-type": "application/json"
                },
                body: JSON.stringify({
                    email: n
                })
            }).then(i => f(this, null, function*() {
                if (i.ok) {
                    let s = yield i.json();
                    r(s.success, s.description, t, o)
                } else r(!1, "Something went wrong.", t, o)
            })).catch(i => {
                r(!1, "Something went wrong.", t, o), console.error(i)
            }))
        }
        let l = document.getElementById("btnSubscribe");
        l && l.addEventListener("click", () => a("#input_email", "#btnSubscribe"));
        let d = document.getElementById("btn_subNewletter");
        d && d.addEventListener("click", () => a("#input_email1", "#btn_subNewletter"));
        let u = document.getElementById("newsletter_subscription_desc");
        u && u.addEventListener("click", t => t.stopPropagation())
    })();
})();
  </script>
  
  <script>
      (() => {
    var l = () => {
            var n;
            return (n = window.Capacitor) != null && n.isNativePlatform() ? "native" : "web"
        },
        d = n => {
            let t = [`platform=${l()}`];
            n && t.push(`bannerId=${n}`);
            let r = `${window.yoyoData.config.SITE_URL}${window.yoyoData.config.API_PATH}/banner?${t.join("&")}`;
            fetch(r).then(e => e.json()).then(e => {
                if (!e.h) return;
                let o = window.sessionStorage.getItem("dismissed");
                if (o = o ? o.split(",") : [], o.includes(e.id)) return;
                let s = document.getElementById("page-banner"),
                    i = document.querySelector(".bpadding");
                s.style.backgroundColor = e.bgc, s.style.color = e.c, s.innerHTML = e.h;
                let c = s.getElementsByTagName("a");
                for (let a of c) a.style.color = e.ac;
                s.getElementsByClassName("bclose")[0].addEventListener("click", () => {
                    s.classList.remove("open"), i && i.classList.remove("open"), e.id && !n && o.push(e.id), window.sessionStorage.setItem("dismissed", o.join(","))
                }), s.classList.add("open"), i && i.classList.add("open")
            })
        };
    setTimeout(() => document.addEventListener("scroll", () => {
        let t = new URLSearchParams(document.location.search).get("bannerId");
        d(t)
    }, {
        once: !0
    }), 2e3);
})();
  </script>
  
  <script>
      (() => {
    (function() {
        var o, s;
        window.sib = {
            equeue: [],
            client_key: "7852azetg14re00nflhjia9e"
        }, (s = (o = window.yoyoData) == null ? void 0 : o.initialUserData) != null && s.email && (window.sib.email_id = window.yoyoData.initialUserData.email), window.sendinblue = {};
        for (var a = ["track", "identify", "trackLink", "page"], e = 0; e < a.length; e++)(function(t) {
            window.sendinblue[t] = function() {
                var n = Array.prototype.slice.call(arguments);
                (window.sib[t] || function() {
                    var r = {};
                    r[t] = n, window.sib.equeue.push(r)
                })(n[0], n[1], n[2], n[3])
            }
        })(a[e]);
        var i = document.createElement("script"),
            e = document.getElementsByTagName("script")[0];
        i.type = "text/javascript", i.id = "sendinblue-js", i.async = !0, i.src = "https://sibautomation.com/sa.js?key=" + window.sib.client_key, e.parentNode.insertBefore(i, e), window.sendinblue.page()
    })();
})();
  </script>
  
  <script>
      (() => {
    var a = [
        [{
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["b", "", "", "", ""],
            type: 1,
            videoUrl: "",
            audio: [null, null, null, null, null],
            join: "b"
        }, {
            text: ["p", "", "", "", ""],
            type: 1,
            videoUrl: "",
            audio: [null, null, null, null, null],
            join: "p"
        }, {
            text: ["m", "", "", "", ""],
            type: 1,
            videoUrl: "",
            audio: [null, null, null, null, null],
            join: "m"
        }, {
            text: ["f", "", "", "", ""],
            type: 1,
            videoUrl: "",
            audio: [null, null, null, null, null],
            join: "f"
        }, {
            text: ["d", "", "", "", ""],
            type: 1,
            videoUrl: "",
            audio: [null, null, null, null, null],
            join: "d"
        }, {
            text: ["t", "", "", "", ""],
            type: 1,
            videoUrl: "",
            audio: [null, null, null, null, null],
            join: "t"
        }, {
            text: ["n", "", "", "", ""],
            type: 1,
            videoUrl: "",
            audio: [null, null, null, null, null],
            join: "n"
        }, {
            text: ["l", "", "", "", ""],
            type: 1,
            videoUrl: "",
            audio: [null, null, null, null, null],
            join: "l"
        }, {
            text: ["g", "", "", "", ""],
            type: 1,
            videoUrl: "",
            audio: [null, null, null, null, null],
            join: "g"
        }, {
            text: ["k", "", "", "", ""],
            type: 1,
            videoUrl: "",
            audio: [null, null, null, null, null],
            join: "k"
        }, {
            text: ["h", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQyMQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "h"
        }, {
            text: ["j", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQyMg==?fallback=true",
            audio: [null, null, null, null, null],
            join: "j"
        }, {
            text: ["q", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQyMw==?fallback=true",
            audio: [null, null, null, null, null],
            join: "q"
        }, {
            text: ["x", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQyNg==?fallback=true",
            audio: [null, null, null, null, null],
            join: "x"
        }, {
            text: ["z", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQyNw==?fallback=true",
            audio: [null, null, null, null, null],
            join: "z"
        }, {
            text: ["c", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQxOQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "c"
        }, {
            text: ["s", "", "", "", ""],
            type: 1,
            videoUrl: "",
            audio: [null, null, null, null, null],
            join: "s"
        }, {
            text: ["zh", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQyOA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "zh"
        }, {
            text: ["ch", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQyMA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ch"
        }, {
            text: ["sh", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQyNQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "sh"
        }, {
            text: ["r", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQyNA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "r"
        }],
        [{
            text: ["i", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM5Mg==?fallback=true",
            audio: [null, null, null, null, null],
            join: "i"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zi", "z\u012B", "z\xED", "z\u01D0", "z\xEC"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ3NA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zi1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zi2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zi3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zi4.mp3"],
            join: "z + i"
        }, {
            text: ["ci", "c\u012B", "c\xED", "c\u01D0", "c\xEC"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQzMQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ci1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ci2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ci3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ci4.mp3"],
            join: "c + i"
        }, {
            text: ["si", "s\u012B", "s\xED", "s\u01D0", "s\xEC"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ0Ng==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/si1.mp3", "https://cdn.yoyochinese.com/audio/pychart/si2.mp3", "https://cdn.yoyochinese.com/audio/pychart/si3.mp3", "https://cdn.yoyochinese.com/audio/pychart/si4.mp3"],
            join: "s + i"
        }, {
            text: ["zhi", "zh\u012B", "zh\xED", "zh\u01D0", "zh\xEC"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ3Mw==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhi1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhi2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhi3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhi4.mp3"],
            join: "zh + i"
        }, {
            text: ["chi", "ch\u012B", "ch\xED", "ch\u01D0", "ch\xEC"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQzMA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chi1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chi2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chi3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chi4.mp3"],
            join: "ch + i"
        }, {
            text: ["shi", "sh\u012B", "sh\xED", "sh\u01D0", "sh\xEC"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ0NQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shi1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shi2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shi3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shi4.mp3"],
            join: "sh + i"
        }, {
            text: ["ri", "r\u012B", "r\xED", "r\u01D0", "r\xEC"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ0NA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ri1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ri2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ri3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ri4.mp3"],
            join: "r + i"
        }],
        [{
            text: ["a", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM4Mg==?fallback=true",
            audio: [null, null, null, null, null],
            join: "a"
        }, {
            text: ["a", "\u0101", "\xE1", "\u01CE", "\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/a1.mp3", "https://cdn.yoyochinese.com/audio/pychart/a2.mp3", "https://cdn.yoyochinese.com/audio/pychart/a3.mp3", "https://cdn.yoyochinese.com/audio/pychart/a4.mp3"],
            join: "a"
        }, {
            text: ["ba", "b\u0101", "b\xE1", "b\u01CE", "b\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ba1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ba2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ba3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ba4.mp3"],
            join: "b + a"
        }, {
            text: ["pa", "p\u0101", "p\xE1", "p\u01CE", "p\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pa1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pa2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pa3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pa4.mp3"],
            join: "p + a"
        }, {
            text: ["ma", "m\u0101", "m\xE1", "m\u01CE", "m\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ma1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ma2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ma3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ma4.mp3"],
            join: "m + a"
        }, {
            text: ["fa", "f\u0101", "f\xE1", "f\u01CE", "f\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/fa1.mp3", "https://cdn.yoyochinese.com/audio/pychart/fa2.mp3", "https://cdn.yoyochinese.com/audio/pychart/fa3.mp3", "https://cdn.yoyochinese.com/audio/pychart/fa4.mp3"],
            join: "f + a"
        }, {
            text: ["da", "d\u0101", "d\xE1", "d\u01CE", "d\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/da1.mp3", "https://cdn.yoyochinese.com/audio/pychart/da2.mp3", "https://cdn.yoyochinese.com/audio/pychart/da3.mp3", "https://cdn.yoyochinese.com/audio/pychart/da4.mp3"],
            join: "d + a"
        }, {
            text: ["ta", "t\u0101", "t\xE1", "t\u01CE", "t\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ta1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ta2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ta3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ta4.mp3"],
            join: "t + a"
        }, {
            text: ["na", "n\u0101", "n\xE1", "n\u01CE", "n\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/na1.mp3", "https://cdn.yoyochinese.com/audio/pychart/na2.mp3", "https://cdn.yoyochinese.com/audio/pychart/na3.mp3", "https://cdn.yoyochinese.com/audio/pychart/na4.mp3"],
            join: "n + a"
        }, {
            text: ["la", "l\u0101", "l\xE1", "l\u01CE", "l\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/la1.mp3", "https://cdn.yoyochinese.com/audio/pychart/la2.mp3", "https://cdn.yoyochinese.com/audio/pychart/la3.mp3", "https://cdn.yoyochinese.com/audio/pychart/la4.mp3"],
            join: "l + a"
        }, {
            text: ["ga", "g\u0101", "g\xE1", "g\u01CE", "g\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ga1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ga2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ga3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ga4.mp3"],
            join: "g + a"
        }, {
            text: ["ka", "k\u0101", "k\xE1", "k\u01CE", "k\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ka1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ka2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ka3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ka4.mp3"],
            join: "k + a"
        }, {
            text: ["ha", "h\u0101", "h\xE1", "h\u01CE", "h\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ha1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ha2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ha3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ha4.mp3"],
            join: "h + a"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["za", "z\u0101", "z\xE1", "z\u01CE", "z\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/za1.mp3", "https://cdn.yoyochinese.com/audio/pychart/za2.mp3", "https://cdn.yoyochinese.com/audio/pychart/za3.mp3", "https://cdn.yoyochinese.com/audio/pychart/za4.mp3"],
            join: "z + a"
        }, {
            text: ["ca", "c\u0101", "c\xE1", "c\u01CE", "c\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ca1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ca2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ca3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ca4.mp3"],
            join: "c + a"
        }, {
            text: ["sa", "s\u0101", "s\xE1", "s\u01CE", "s\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/sa1.mp3", "https://cdn.yoyochinese.com/audio/pychart/sa2.mp3", "https://cdn.yoyochinese.com/audio/pychart/sa3.mp3", "https://cdn.yoyochinese.com/audio/pychart/sa4.mp3"],
            join: "s + a"
        }, {
            text: ["zha", "zh\u0101", "zh\xE1", "zh\u01CE", "zh\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zha1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zha2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zha3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zha4.mp3"],
            join: "zh + a"
        }, {
            text: ["cha", "ch\u0101", "ch\xE1", "ch\u01CE", "ch\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cha1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cha2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cha3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cha4.mp3"],
            join: "ch + a"
        }, {
            text: ["sha", "sh\u0101", "sh\xE1", "sh\u01CE", "sh\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/sha1.mp3", "https://cdn.yoyochinese.com/audio/pychart/sha2.mp3", "https://cdn.yoyochinese.com/audio/pychart/sha3.mp3", "https://cdn.yoyochinese.com/audio/pychart/sha4.mp3"],
            join: "sh + a"
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["ai", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM4Mw==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ai"
        }, {
            text: ["ai", "\u0101i", "\xE1i", "\u01CEi", "\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ai4.mp3"],
            join: "ai"
        }, {
            text: ["bai", "b\u0101i", "b\xE1i", "b\u01CEi", "b\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/bai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/bai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/bai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/bai4.mp3"],
            join: "b + ai"
        }, {
            text: ["pai", "p\u0101i", "p\xE1i", "p\u01CEi", "p\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pai4.mp3"],
            join: "p + ai"
        }, {
            text: ["mai", "m\u0101i", "m\xE1i", "m\u01CEi", "m\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/mai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/mai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/mai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/mai4.mp3"],
            join: "m + ai"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["dai", "d\u0101i", "d\xE1i", "d\u01CEi", "d\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/dai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/dai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/dai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/dai4.mp3"],
            join: "d + ai"
        }, {
            text: ["tai", "t\u0101i", "t\xE1i", "t\u01CEi", "t\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tai4.mp3"],
            join: "t + ai"
        }, {
            text: ["nai", "n\u0101i", "n\xE1i", "n\u01CEi", "n\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nai4.mp3"],
            join: "n + ai"
        }, {
            text: ["lai", "l\u0101i", "l\xE1i", "l\u01CEi", "l\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lai4.mp3"],
            join: "l + ai"
        }, {
            text: ["gai", "g\u0101i", "g\xE1i", "g\u01CEi", "g\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/gai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/gai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/gai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/gai4.mp3"],
            join: "g + ai"
        }, {
            text: ["kai", "k\u0101i", "k\xE1i", "k\u01CEi", "k\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kai4.mp3"],
            join: "k + ai"
        }, {
            text: ["hai", "h\u0101i", "h\xE1i", "h\u01CEi", "h\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/hai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/hai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/hai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/hai4.mp3"],
            join: "h + ai"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zai", "z\u0101i", "z\xE1i", "z\u01CEi", "z\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zai4.mp3"],
            join: "z + ai"
        }, {
            text: ["cai", "c\u0101i", "c\xE1i", "c\u01CEi", "c\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cai4.mp3"],
            join: "c + ai"
        }, {
            text: ["sai", "s\u0101i", "s\xE1i", "s\u01CEi", "s\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/sai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/sai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/sai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/sai4.mp3"],
            join: "s + ai"
        }, {
            text: ["zhai", "zh\u0101i", "zh\xE1i", "zh\u01CEi", "zh\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhai4.mp3"],
            join: "zh + ai"
        }, {
            text: ["chai", "ch\u0101i", "ch\xE1i", "ch\u01CEi", "ch\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chai4.mp3"],
            join: "ch + ai"
        }, {
            text: ["shai", "sh\u0101i", "sh\xE1i", "sh\u01CEi", "sh\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shai4.mp3"],
            join: "sh + ai"
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["an", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM4NA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "an"
        }, {
            text: ["an", "\u0101n", "\xE1n", "\u01CEn", "\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/an1.mp3", "https://cdn.yoyochinese.com/audio/pychart/an2.mp3", "https://cdn.yoyochinese.com/audio/pychart/an3.mp3", "https://cdn.yoyochinese.com/audio/pychart/an4.mp3"],
            join: "an"
        }, {
            text: ["ban", "b\u0101n", "b\xE1n", "b\u01CEn", "b\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ban1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ban2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ban3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ban4.mp3"],
            join: "b + an"
        }, {
            text: ["pan", "p\u0101n", "p\xE1n", "p\u01CEn", "p\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pan4.mp3"],
            join: "p + an"
        }, {
            text: ["man", "m\u0101n", "m\xE1n", "m\u01CEn", "m\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/man1.mp3", "https://cdn.yoyochinese.com/audio/pychart/man2.mp3", "https://cdn.yoyochinese.com/audio/pychart/man3.mp3", "https://cdn.yoyochinese.com/audio/pychart/man4.mp3"],
            join: "m + an"
        }, {
            text: ["fan", "f\u0101n", "f\xE1n", "f\u01CEn", "f\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/fan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/fan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/fan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/fan4.mp3"],
            join: "f + an"
        }, {
            text: ["dan", "d\u0101n", "d\xE1n", "d\u01CEn", "d\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/dan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/dan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/dan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/dan4.mp3"],
            join: "d + an"
        }, {
            text: ["tan", "t\u0101n", "t\xE1n", "t\u01CEn", "t\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tan4.mp3"],
            join: "t + an"
        }, {
            text: ["nan", "n\u0101n", "n\xE1n", "n\u01CEn", "n\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nan4.mp3"],
            join: "n + an"
        }, {
            text: ["lan", "l\u0101n", "l\xE1n", "l\u01CEn", "l\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lan4.mp3"],
            join: "l + an"
        }, {
            text: ["gan", "g\u0101n", "g\xE1n", "g\u01CEn", "g\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/gan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/gan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/gan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/gan4.mp3"],
            join: "g + an"
        }, {
            text: ["kan", "k\u0101n", "k\xE1n", "k\u01CEn", "k\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kan4.mp3"],
            join: "k + an"
        }, {
            text: ["han", "h\u0101n", "h\xE1n", "h\u01CEn", "h\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/han1.mp3", "https://cdn.yoyochinese.com/audio/pychart/han2.mp3", "https://cdn.yoyochinese.com/audio/pychart/han3.mp3", "https://cdn.yoyochinese.com/audio/pychart/han4.mp3"],
            join: "h + an"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zan", "z\u0101n", "z\xE1n", "z\u01CEn", "z\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zan4.mp3"],
            join: "z + an"
        }, {
            text: ["can", "c\u0101n", "c\xE1n", "c\u01CEn", "c\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/can1.mp3", "https://cdn.yoyochinese.com/audio/pychart/can2.mp3", "https://cdn.yoyochinese.com/audio/pychart/can3.mp3", "https://cdn.yoyochinese.com/audio/pychart/can4.mp3"],
            join: "c + an"
        }, {
            text: ["san", "s\u0101n", "s\xE1n", "s\u01CEn", "s\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/san1.mp3", "https://cdn.yoyochinese.com/audio/pychart/san2.mp3", "https://cdn.yoyochinese.com/audio/pychart/san3.mp3", "https://cdn.yoyochinese.com/audio/pychart/san4.mp3"],
            join: "s + an"
        }, {
            text: ["zhan", "zh\u0101n", "zh\xE1n", "zh\u01CEn", "zh\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhan4.mp3"],
            join: "zh + an"
        }, {
            text: ["chan", "ch\u0101n", "ch\xE1n", "ch\u01CEn", "ch\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chan4.mp3"],
            join: "ch + an"
        }, {
            text: ["shan", "sh\u0101n", "sh\xE1n", "sh\u01CEn", "sh\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shan4.mp3"],
            join: "sh + an"
        }, {
            text: ["ran", "r\u0101n", "r\xE1n", "r\u01CEn", "r\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ran1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ran2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ran3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ran4.mp3"],
            join: "r + an"
        }],
        [{
            text: ["ang", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM4NQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ang"
        }, {
            text: ["ang", "\u0101ng", "\xE1ng", "\u01CEng", "\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ang4.mp3"],
            join: "ang"
        }, {
            text: ["bang", "b\u0101ng", "b\xE1ng", "b\u01CEng", "b\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/bang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/bang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/bang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/bang4.mp3"],
            join: "b + ang"
        }, {
            text: ["pang", "p\u0101ng", "p\xE1ng", "p\u01CEng", "p\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pang4.mp3"],
            join: "p + ang"
        }, {
            text: ["mang", "m\u0101ng", "m\xE1ng", "m\u01CEng", "m\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/mang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/mang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/mang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/mang4.mp3"],
            join: "m + ang"
        }, {
            text: ["fang", "f\u0101ng", "f\xE1ng", "f\u01CEng", "f\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/fang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/fang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/fang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/fang4.mp3"],
            join: "f + ang"
        }, {
            text: ["dang", "d\u0101ng", "d\xE1ng", "d\u01CEng", "d\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/dang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/dang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/dang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/dang4.mp3"],
            join: "d + ang"
        }, {
            text: ["tang", "t\u0101ng", "t\xE1ng", "t\u01CEng", "t\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tang4.mp3"],
            join: "t + ang"
        }, {
            text: ["nang", "n\u0101ng", "n\xE1ng", "n\u01CEng", "n\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nang4.mp3"],
            join: "n + ang"
        }, {
            text: ["lang", "l\u0101ng", "l\xE1ng", "l\u01CEng", "l\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lang4.mp3"],
            join: "l + ang"
        }, {
            text: ["gang", "g\u0101ng", "g\xE1ng", "g\u01CEng", "g\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/gang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/gang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/gang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/gang4.mp3"],
            join: "g + ang"
        }, {
            text: ["kang", "k\u0101ng", "k\xE1ng", "k\u01CEng", "k\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kang4.mp3"],
            join: "k + ang"
        }, {
            text: ["hang", "h\u0101ng", "h\xE1ng", "h\u01CEng", "h\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/hang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/hang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/hang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/hang4.mp3"],
            join: "h + ang"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zang", "z\u0101ng", "z\xE1ng", "z\u01CEng", "z\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zang4.mp3"],
            join: "z + ang"
        }, {
            text: ["cang", "c\u0101ng", "c\xE1ng", "c\u01CEng", "c\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cang4.mp3"],
            join: "c + ang"
        }, {
            text: ["sang", "s\u0101ng", "s\xE1ng", "s\u01CEng", "s\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/sang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/sang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/sang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/sang4.mp3"],
            join: "s + ang"
        }, {
            text: ["zhang", "zh\u0101ng", "zh\xE1ng", "zh\u01CEng", "zh\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhang4.mp3"],
            join: "zh + ang"
        }, {
            text: ["chang", "ch\u0101ng", "ch\xE1ng", "ch\u01CEng", "ch\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chang4.mp3"],
            join: "ch + ang"
        }, {
            text: ["shang", "sh\u0101ng", "sh\xE1ng", "sh\u01CEng", "sh\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shang4.mp3"],
            join: "sh + ang"
        }, {
            text: ["rang", "r\u0101ng", "r\xE1ng", "r\u01CEng", "r\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/rang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/rang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/rang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/rang4.mp3"],
            join: "r + ang"
        }],
        [{
            text: ["ao", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM4Ng==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ao"
        }, {
            text: ["ao", "\u0101o", "\xE1o", "\u01CEo", "\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ao4.mp3"],
            join: "ao"
        }, {
            text: ["bao", "b\u0101o", "b\xE1o", "b\u01CEo", "b\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/bao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/bao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/bao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/bao4.mp3"],
            join: "b + ao"
        }, {
            text: ["pao", "p\u0101o", "p\xE1o", "p\u01CEo", "p\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pao4.mp3"],
            join: "p + ao"
        }, {
            text: ["mao", "m\u0101o", "m\xE1o", "m\u01CEo", "m\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/mao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/mao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/mao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/mao4.mp3"],
            join: "m + ao"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["dao", "d\u0101o", "d\xE1o", "d\u01CEo", "d\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/dao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/dao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/dao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/dao4.mp3"],
            join: "d + ao"
        }, {
            text: ["tao", "t\u0101o", "t\xE1o", "t\u01CEo", "t\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tao4.mp3"],
            join: "t + ao"
        }, {
            text: ["nao", "n\u0101o", "n\xE1o", "n\u01CEo", "n\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nao4.mp3"],
            join: "n + ao"
        }, {
            text: ["lao", "l\u0101o", "l\xE1o", "l\u01CEo", "l\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lao4.mp3"],
            join: "l + ao"
        }, {
            text: ["gao", "g\u0101o", "g\xE1o", "g\u01CEo", "g\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/gao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/gao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/gao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/gao4.mp3"],
            join: "g + ao"
        }, {
            text: ["kao", "k\u0101o", "k\xE1o", "k\u01CEo", "k\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kao4.mp3"],
            join: "k + ao"
        }, {
            text: ["hao", "h\u0101o", "h\xE1o", "h\u01CEo", "h\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/hao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/hao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/hao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/hao4.mp3"],
            join: "h + ao"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zao", "z\u0101o", "z\xE1o", "z\u01CEo", "z\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zao4.mp3"],
            join: "z + ao"
        }, {
            text: ["cao", "c\u0101o", "c\xE1o", "c\u01CEo", "c\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cao4.mp3"],
            join: "c + ao"
        }, {
            text: ["sao", "s\u0101o", "s\xE1o", "s\u01CEo", "s\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/sao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/sao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/sao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/sao4.mp3"],
            join: "s + ao"
        }, {
            text: ["zhao", "zh\u0101o", "zh\xE1o", "zh\u01CEo", "zh\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhao4.mp3"],
            join: "zh + ao"
        }, {
            text: ["chao", "ch\u0101o", "ch\xE1o", "ch\u01CEo", "ch\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chao4.mp3"],
            join: "ch + ao"
        }, {
            text: ["shao", "sh\u0101o", "sh\xE1o", "sh\u01CEo", "sh\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shao4.mp3"],
            join: "sh + ao"
        }, {
            text: ["rao", "r\u0101o", "r\xE1o", "r\u01CEo", "r\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/rao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/rao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/rao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/rao4.mp3"],
            join: "r + ao"
        }],
        [{
            text: ["e", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM4Nw==?fallback=true",
            audio: [null, null, null, null, null],
            join: "e"
        }, {
            text: ["e", "\u0113", "\xE9", "\u011B", "\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/e1.mp3", "https://cdn.yoyochinese.com/audio/pychart/e2.mp3", "https://cdn.yoyochinese.com/audio/pychart/e3.mp3", "https://cdn.yoyochinese.com/audio/pychart/e4.mp3"],
            join: "e"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["me", "m\u0113", "m\xE9", "m\u011B", "m\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/me1.mp3", "https://cdn.yoyochinese.com/audio/pychart/me2.mp3", "https://cdn.yoyochinese.com/audio/pychart/me3.mp3", "https://cdn.yoyochinese.com/audio/pychart/me4.mp3"],
            join: "m + e"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["de", "d\u0113", "d\xE9", "d\u011B", "d\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/de1.mp3", "https://cdn.yoyochinese.com/audio/pychart/de2.mp3", "https://cdn.yoyochinese.com/audio/pychart/de3.mp3", "https://cdn.yoyochinese.com/audio/pychart/de4.mp3"],
            join: "d + e"
        }, {
            text: ["te", "t\u0113", "t\xE9", "t\u011B", "t\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/te1.mp3", "https://cdn.yoyochinese.com/audio/pychart/te2.mp3", "https://cdn.yoyochinese.com/audio/pychart/te3.mp3", "https://cdn.yoyochinese.com/audio/pychart/te4.mp3"],
            join: "t + e"
        }, {
            text: ["ne", "n\u0113", "n\xE9", "n\u011B", "n\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ne1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ne2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ne3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ne4.mp3"],
            join: "n + e"
        }, {
            text: ["le", "l\u0113", "l\xE9", "l\u011B", "l\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/le1.mp3", "https://cdn.yoyochinese.com/audio/pychart/le2.mp3", "https://cdn.yoyochinese.com/audio/pychart/le3.mp3", "https://cdn.yoyochinese.com/audio/pychart/le4.mp3"],
            join: "l + e"
        }, {
            text: ["ge", "g\u0113", "g\xE9", "g\u011B", "g\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ge1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ge2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ge3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ge4.mp3"],
            join: "g + e"
        }, {
            text: ["ke", "k\u0113", "k\xE9", "k\u011B", "k\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ke1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ke2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ke3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ke4.mp3"],
            join: "k + e"
        }, {
            text: ["he", "h\u0113", "h\xE9", "h\u011B", "h\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/he1.mp3", "https://cdn.yoyochinese.com/audio/pychart/he2.mp3", "https://cdn.yoyochinese.com/audio/pychart/he3.mp3", "https://cdn.yoyochinese.com/audio/pychart/he4.mp3"],
            join: "h + e"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["ze", "z\u0113", "z\xE9", "z\u011B", "z\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ze1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ze2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ze3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ze4.mp3"],
            join: "z + e"
        }, {
            text: ["ce", "c\u0113", "c\xE9", "c\u011B", "c\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ce1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ce2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ce3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ce4.mp3"],
            join: "c + e"
        }, {
            text: ["se", "s\u0113", "s\xE9", "s\u011B", "s\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/se1.mp3", "https://cdn.yoyochinese.com/audio/pychart/se2.mp3", "https://cdn.yoyochinese.com/audio/pychart/se3.mp3", "https://cdn.yoyochinese.com/audio/pychart/se4.mp3"],
            join: "s + e"
        }, {
            text: ["zhe", "zh\u0113", "zh\xE9", "zh\u011B", "zh\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhe1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhe2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhe3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhe4.mp3"],
            join: "zh + e"
        }, {
            text: ["che", "ch\u0113", "ch\xE9", "ch\u011B", "ch\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/che1.mp3", "https://cdn.yoyochinese.com/audio/pychart/che2.mp3", "https://cdn.yoyochinese.com/audio/pychart/che3.mp3", "https://cdn.yoyochinese.com/audio/pychart/che4.mp3"],
            join: "ch + e"
        }, {
            text: ["she", "sh\u0113", "sh\xE9", "sh\u011B", "sh\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/she1.mp3", "https://cdn.yoyochinese.com/audio/pychart/she2.mp3", "https://cdn.yoyochinese.com/audio/pychart/she3.mp3", "https://cdn.yoyochinese.com/audio/pychart/she4.mp3"],
            join: "sh + e"
        }, {
            text: ["re", "r\u0113", "r\xE9", "r\u011B", "r\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/re1.mp3", "https://cdn.yoyochinese.com/audio/pychart/re2.mp3", "https://cdn.yoyochinese.com/audio/pychart/re3.mp3", "https://cdn.yoyochinese.com/audio/pychart/re4.mp3"],
            join: "r + e"
        }],
        [{
            text: ["ei", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM4OA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ei"
        }, {
            text: ["ei", "\u0113i", "\xE9i", "\u011Bi", "\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ei4.mp3"],
            join: "ei"
        }, {
            text: ["bei", "b\u0113i", "b\xE9i", "b\u011Bi", "b\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/bei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/bei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/bei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/bei4.mp3"],
            join: "b + ei"
        }, {
            text: ["pei", "p\u0113i", "p\xE9i", "p\u011Bi", "p\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pei4.mp3"],
            join: "p + ei"
        }, {
            text: ["mei", "m\u0113i", "m\xE9i", "m\u011Bi", "m\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/mei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/mei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/mei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/mei4.mp3"],
            join: "m + ei"
        }, {
            text: ["fei", "f\u0113i", "f\xE9i", "f\u011Bi", "f\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/fei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/fei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/fei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/fei4.mp3"],
            join: "f + ei"
        }, {
            text: ["dei", "d\u0113i", "d\xE9i", "d\u011Bi", "d\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/dei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/dei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/dei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/dei4.mp3"],
            join: "d + ei"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["nei", "n\u0113i", "n\xE9i", "n\u011Bi", "n\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nei4.mp3"],
            join: "n + ei"
        }, {
            text: ["lei", "l\u0113i", "l\xE9i", "l\u011Bi", "l\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lei4.mp3"],
            join: "l + ei"
        }, {
            text: ["gei", "g\u0113i", "g\xE9i", "g\u011Bi", "g\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/gei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/gei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/gei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/gei4.mp3"],
            join: "g + ei"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["hei", "h\u0113i", "h\xE9i", "h\u011Bi", "h\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/hei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/hei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/hei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/hei4.mp3"],
            join: "h + ei"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zei", "z\u0113i", "z\xE9i", "z\u011Bi", "z\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zei4.mp3"],
            join: "z + ei"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zhei", "zh\u0113i", "zh\xE9i", "zh\u011Bi", "zh\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhei4.mp3"],
            join: "zh + ei"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["shei", "sh\u0113i", "sh\xE9i", "sh\u011Bi", "sh\xE8i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shei4.mp3"],
            join: "sh + ei"
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["en", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM4OQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "en"
        }, {
            text: ["en", "\u0113n", "\xE9n", "\u011Bn", "\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/en1.mp3", "https://cdn.yoyochinese.com/audio/pychart/en2.mp3", "https://cdn.yoyochinese.com/audio/pychart/en3.mp3", "https://cdn.yoyochinese.com/audio/pychart/en4.mp3"],
            join: "en"
        }, {
            text: ["ben", "b\u0113n", "b\xE9n", "b\u011Bn", "b\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ben1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ben2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ben3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ben4.mp3"],
            join: "b + en"
        }, {
            text: ["pen", "p\u0113n", "p\xE9n", "p\u011Bn", "p\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pen1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pen2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pen3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pen4.mp3"],
            join: "p + en"
        }, {
            text: ["men", "m\u0113n", "m\xE9n", "m\u011Bn", "m\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/men1.mp3", "https://cdn.yoyochinese.com/audio/pychart/men2.mp3", "https://cdn.yoyochinese.com/audio/pychart/men3.mp3", "https://cdn.yoyochinese.com/audio/pychart/men4.mp3"],
            join: "m + en"
        }, {
            text: ["fen", "f\u0113n", "f\xE9n", "f\u011Bn", "f\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/fen1.mp3", "https://cdn.yoyochinese.com/audio/pychart/fen2.mp3", "https://cdn.yoyochinese.com/audio/pychart/fen3.mp3", "https://cdn.yoyochinese.com/audio/pychart/fen4.mp3"],
            join: "f + en"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["nen", "n\u0113n", "n\xE9n", "n\u011Bn", "n\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nen1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nen2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nen3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nen4.mp3"],
            join: "n + en"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["gen", "g\u0113n", "g\xE9n", "g\u011Bn", "g\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/gen1.mp3", "https://cdn.yoyochinese.com/audio/pychart/gen2.mp3", "https://cdn.yoyochinese.com/audio/pychart/gen3.mp3", "https://cdn.yoyochinese.com/audio/pychart/gen4.mp3"],
            join: "g + en"
        }, {
            text: ["ken", "k\u0113n", "k\xE9n", "k\u011Bn", "k\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ken1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ken2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ken3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ken4.mp3"],
            join: "k + en"
        }, {
            text: ["hen", "h\u0113n", "h\xE9n", "h\u011Bn", "h\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/hen1.mp3", "https://cdn.yoyochinese.com/audio/pychart/hen2.mp3", "https://cdn.yoyochinese.com/audio/pychart/hen3.mp3", "https://cdn.yoyochinese.com/audio/pychart/hen4.mp3"],
            join: "h + en"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zen", "z\u0113n", "z\xE9n", "z\u011Bn", "z\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zen1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zen2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zen3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zen4.mp3"],
            join: "z + en"
        }, {
            text: ["cen", "c\u0113n", "c\xE9n", "c\u011Bn", "c\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cen1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cen2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cen3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cen4.mp3"],
            join: "c + en"
        }, {
            text: ["sen", "s\u0113n", "s\xE9n", "s\u011Bn", "s\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/sen1.mp3", "https://cdn.yoyochinese.com/audio/pychart/sen2.mp3", "https://cdn.yoyochinese.com/audio/pychart/sen3.mp3", "https://cdn.yoyochinese.com/audio/pychart/sen4.mp3"],
            join: "s + en"
        }, {
            text: ["zhen", "zh\u0113n", "zh\xE9n", "zh\u011Bn", "zh\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhen1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhen2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhen3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhen4.mp3"],
            join: "zh + en"
        }, {
            text: ["chen", "ch\u0113n", "ch\xE9n", "ch\u011Bn", "ch\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chen1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chen2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chen3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chen4.mp3"],
            join: "ch + en"
        }, {
            text: ["shen", "sh\u0113n", "sh\xE9n", "sh\u011Bn", "sh\xE8n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shen1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shen2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shen3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shen4.mp3"],
            join: "sh + en"
        }, {
            text: ["ren", "r\u0113n", "r\xE9n", "r\u011Bn", "r\xE8n"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ0Mw==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ren1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ren2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ren3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ren4.mp3"],
            join: "r + en"
        }],
        [{
            text: ["eng", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM5MA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "eng"
        }, {
            text: ["eng", "\u0113ng", "\xE9ng", "\u011Bng", "\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/eng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/eng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/eng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/eng4.mp3"],
            join: "eng"
        }, {
            text: ["beng", "b\u0113ng", "b\xE9ng", "b\u011Bng", "b\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/beng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/beng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/beng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/beng4.mp3"],
            join: "b + eng"
        }, {
            text: ["peng", "p\u0113ng", "p\xE9ng", "p\u011Bng", "p\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/peng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/peng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/peng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/peng4.mp3"],
            join: "p + eng"
        }, {
            text: ["meng", "m\u0113ng", "m\xE9ng", "m\u011Bng", "m\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/meng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/meng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/meng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/meng4.mp3"],
            join: "m + eng"
        }, {
            text: ["feng", "f\u0113ng", "f\xE9ng", "f\u011Bng", "f\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/feng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/feng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/feng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/feng4.mp3"],
            join: "f + eng"
        }, {
            text: ["deng", "d\u0113ng", "d\xE9ng", "d\u011Bng", "d\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/deng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/deng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/deng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/deng4.mp3"],
            join: "d + eng"
        }, {
            text: ["teng", "t\u0113ng", "t\xE9ng", "t\u011Bng", "t\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/teng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/teng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/teng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/teng4.mp3"],
            join: "t + eng"
        }, {
            text: ["neng", "n\u0113ng", "n\xE9ng", "n\u011Bng", "n\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/neng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/neng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/neng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/neng4.mp3"],
            join: "n + eng"
        }, {
            text: ["leng", "l\u0113ng", "l\xE9ng", "l\u011Bng", "l\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/leng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/leng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/leng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/leng4.mp3"],
            join: "l + eng"
        }, {
            text: ["geng", "g\u0113ng", "g\xE9ng", "g\u011Bng", "g\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/geng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/geng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/geng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/geng4.mp3"],
            join: "g + eng"
        }, {
            text: ["keng", "k\u0113ng", "k\xE9ng", "k\u011Bng", "k\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/keng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/keng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/keng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/keng4.mp3"],
            join: "k + eng"
        }, {
            text: ["heng", "h\u0113ng", "h\xE9ng", "h\u011Bng", "h\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/heng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/heng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/heng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/heng4.mp3"],
            join: "h + eng"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zeng", "z\u0113ng", "z\xE9ng", "z\u011Bng", "z\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zeng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zeng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zeng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zeng4.mp3"],
            join: "z + eng"
        }, {
            text: ["ceng", "c\u0113ng", "c\xE9ng", "c\u011Bng", "c\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ceng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ceng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ceng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ceng4.mp3"],
            join: "c + eng"
        }, {
            text: ["seng", "s\u0113ng", "s\xE9ng", "s\u011Bng", "s\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/seng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/seng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/seng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/seng4.mp3"],
            join: "s + eng"
        }, {
            text: ["zheng", "zh\u0113ng", "zh\xE9ng", "zh\u011Bng", "zh\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zheng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zheng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zheng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zheng4.mp3"],
            join: "zh + eng"
        }, {
            text: ["cheng", "ch\u0113ng", "ch\xE9ng", "ch\u011Bng", "ch\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cheng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cheng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cheng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cheng4.mp3"],
            join: "ch + eng"
        }, {
            text: ["sheng", "sh\u0113ng", "sh\xE9ng", "sh\u011Bng", "sh\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/sheng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/sheng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/sheng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/sheng4.mp3"],
            join: "sh + eng"
        }, {
            text: ["reng", "r\u0113ng", "r\xE9ng", "r\u011Bng", "r\xE8ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/reng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/reng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/reng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/reng4.mp3"],
            join: "r + eng"
        }],
        [{
            text: ["er", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM5MQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "er"
        }, {
            text: ["er", "\u0113r", "\xE9r", "\u011Br", "\xE8r"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/er1.mp3", "https://cdn.yoyochinese.com/audio/pychart/er2.mp3", "https://cdn.yoyochinese.com/audio/pychart/er3.mp3", "https://cdn.yoyochinese.com/audio/pychart/er4.mp3"],
            join: "er"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["i", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM5Mg==?fallback=true",
            audio: [null, null, null, null, null],
            join: "i"
        }, {
            text: ["yi", "y\u012B", "y\xED", "y\u01D0", "y\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/yi1.mp3", "https://cdn.yoyochinese.com/audio/pychart/yi2.mp3", "https://cdn.yoyochinese.com/audio/pychart/yi3.mp3", "https://cdn.yoyochinese.com/audio/pychart/yi4.mp3"],
            join: "y + i"
        }, {
            text: ["bi", "b\u012B", "b\xED", "b\u01D0", "b\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/bi1.mp3", "https://cdn.yoyochinese.com/audio/pychart/bi2.mp3", "https://cdn.yoyochinese.com/audio/pychart/bi3.mp3", "https://cdn.yoyochinese.com/audio/pychart/bi4.mp3"],
            join: "b + i"
        }, {
            text: ["pi", "p\u012B", "p\xED", "p\u01D0", "p\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pi1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pi2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pi3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pi4.mp3"],
            join: "p + i"
        }, {
            text: ["mi", "m\u012B", "m\xED", "m\u01D0", "m\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/mi1.mp3", "https://cdn.yoyochinese.com/audio/pychart/mi2.mp3", "https://cdn.yoyochinese.com/audio/pychart/mi3.mp3", "https://cdn.yoyochinese.com/audio/pychart/mi4.mp3"],
            join: "m + i"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["di", "d\u012B", "d\xED", "d\u01D0", "d\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/di1.mp3", "https://cdn.yoyochinese.com/audio/pychart/di2.mp3", "https://cdn.yoyochinese.com/audio/pychart/di3.mp3", "https://cdn.yoyochinese.com/audio/pychart/di4.mp3"],
            join: "d + i"
        }, {
            text: ["ti", "t\u012B", "t\xED", "t\u01D0", "t\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ti1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ti2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ti3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ti4.mp3"],
            join: "t + i"
        }, {
            text: ["ni", "n\u012B", "n\xED", "n\u01D0", "n\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ni1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ni2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ni3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ni4.mp3"],
            join: "n + i"
        }, {
            text: ["li", "l\u012B", "l\xED", "l\u01D0", "l\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/li1.mp3", "https://cdn.yoyochinese.com/audio/pychart/li2.mp3", "https://cdn.yoyochinese.com/audio/pychart/li3.mp3", "https://cdn.yoyochinese.com/audio/pychart/li4.mp3"],
            join: "l + i"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["ji", "j\u012B", "j\xED", "j\u01D0", "j\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ji1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ji2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ji3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ji4.mp3"],
            join: "j + i"
        }, {
            text: ["qi", "q\u012B", "q\xED", "q\u01D0", "q\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/qi1.mp3", "https://cdn.yoyochinese.com/audio/pychart/qi2.mp3", "https://cdn.yoyochinese.com/audio/pychart/qi3.mp3", "https://cdn.yoyochinese.com/audio/pychart/qi4.mp3"],
            join: "q + i"
        }, {
            text: ["xi", "x\u012B", "x\xED", "x\u01D0", "x\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xi1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xi2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xi3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xi4.mp3"],
            join: "x + i"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["ia", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM5NA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ia"
        }, {
            text: ["ya", "y\u0101", "y\xE1", "y\u01CE", "y\xE0"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ2MA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ya1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ya2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ya3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ya4.mp3"],
            join: "y + ia"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["dia", "di\u0101", "di\xE1", "di\u01CE", "di\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/dia1.mp3", "https://cdn.yoyochinese.com/audio/pychart/dia2.mp3", "https://cdn.yoyochinese.com/audio/pychart/dia3.mp3", "https://cdn.yoyochinese.com/audio/pychart/dia4.mp3"],
            join: "d + ia"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["lia", "li\u0101", "li\xE1", "li\u01CE", "li\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lia1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lia2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lia3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lia4.mp3"],
            join: "l + ia"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["jia", "ji\u0101", "ji\xE1", "ji\u01CE", "ji\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/jia1.mp3", "https://cdn.yoyochinese.com/audio/pychart/jia2.mp3", "https://cdn.yoyochinese.com/audio/pychart/jia3.mp3", "https://cdn.yoyochinese.com/audio/pychart/jia4.mp3"],
            join: "j + ia"
        }, {
            text: ["qia", "qi\u0101", "qi\xE1", "qi\u01CE", "qi\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/qia1.mp3", "https://cdn.yoyochinese.com/audio/pychart/qia2.mp3", "https://cdn.yoyochinese.com/audio/pychart/qia3.mp3", "https://cdn.yoyochinese.com/audio/pychart/qia4.mp3"],
            join: "q + ia"
        }, {
            text: ["xia", "xi\u0101", "xi\xE1", "xi\u01CE", "xi\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xia1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xia2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xia3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xia4.mp3"],
            join: "x + ia"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["ian", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM5NQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ian"
        }, {
            text: ["yan", "y\u0101n", "y\xE1n", "y\u01CEn", "y\xE0n"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ2MQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/yan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/yan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/yan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/yan4.mp3"],
            join: "y + ian"
        }, {
            text: ["bian", "bi\u0101n", "bi\xE1n", "bi\u01CEn", "bi\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/bian1.mp3", "https://cdn.yoyochinese.com/audio/pychart/bian2.mp3", "https://cdn.yoyochinese.com/audio/pychart/bian3.mp3", "https://cdn.yoyochinese.com/audio/pychart/bian4.mp3"],
            join: "b + ian"
        }, {
            text: ["pian", "pi\u0101n", "pi\xE1n", "pi\u01CEn", "pi\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pian1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pian2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pian3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pian4.mp3"],
            join: "p + ian"
        }, {
            text: ["mian", "mi\u0101n", "mi\xE1n", "mi\u01CEn", "mi\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/mian1.mp3", "https://cdn.yoyochinese.com/audio/pychart/mian2.mp3", "https://cdn.yoyochinese.com/audio/pychart/mian3.mp3", "https://cdn.yoyochinese.com/audio/pychart/mian4.mp3"],
            join: "m + ian"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["dian", "di\u0101n", "di\xE1n", "di\u01CEn", "di\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/dian1.mp3", "https://cdn.yoyochinese.com/audio/pychart/dian2.mp3", "https://cdn.yoyochinese.com/audio/pychart/dian3.mp3", "https://cdn.yoyochinese.com/audio/pychart/dian4.mp3"],
            join: "d + ian"
        }, {
            text: ["tian", "ti\u0101n", "ti\xE1n", "ti\u01CEn", "ti\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tian1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tian2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tian3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tian4.mp3"],
            join: "t + ian"
        }, {
            text: ["nian", "ni\u0101n", "ni\xE1n", "ni\u01CEn", "ni\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nian1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nian2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nian3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nian4.mp3"],
            join: "n + ian"
        }, {
            text: ["lian", "li\u0101n", "li\xE1n", "li\u01CEn", "li\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lian1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lian2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lian3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lian4.mp3"],
            join: "l + ian"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["jian", "ji\u0101n", "ji\xE1n", "ji\u01CEn", "ji\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/jian1.mp3", "https://cdn.yoyochinese.com/audio/pychart/jian2.mp3", "https://cdn.yoyochinese.com/audio/pychart/jian3.mp3", "https://cdn.yoyochinese.com/audio/pychart/jian4.mp3"],
            join: "j + ian"
        }, {
            text: ["qian", "qi\u0101n", "qi\xE1n", "qi\u01CEn", "qi\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/qian1.mp3", "https://cdn.yoyochinese.com/audio/pychart/qian2.mp3", "https://cdn.yoyochinese.com/audio/pychart/qian3.mp3", "https://cdn.yoyochinese.com/audio/pychart/qian4.mp3"],
            join: "q + ian"
        }, {
            text: ["xian", "xi\u0101n", "xi\xE1n", "xi\u01CEn", "xi\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xian1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xian2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xian3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xian4.mp3"],
            join: "x + ian"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["iang", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM5Ng==?fallback=true",
            audio: [null, null, null, null, null],
            join: "iang"
        }, {
            text: ["yang", "y\u0101ng", "y\xE1ng", "y\u01CEng", "y\xE0ng"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ2Mg==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/yang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/yang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/yang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/yang4.mp3"],
            join: "y + iang"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["niang", "ni\u0101ng", "ni\xE1ng", "ni\u01CEng", "ni\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/niang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/niang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/niang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/niang4.mp3"],
            join: "n + iang"
        }, {
            text: ["liang", "li\u0101ng", "li\xE1ng", "li\u01CEng", "li\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/liang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/liang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/liang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/liang4.mp3"],
            join: "l + iang"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["jiang", "ji\u0101ng", "ji\xE1ng", "ji\u01CEng", "ji\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/jiang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/jiang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/jiang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/jiang4.mp3"],
            join: "j + iang"
        }, {
            text: ["qiang", "qi\u0101ng", "qi\xE1ng", "qi\u01CEng", "qi\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/qiang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/qiang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/qiang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/qiang4.mp3"],
            join: "q + iang"
        }, {
            text: ["xiang", "xi\u0101ng", "xi\xE1ng", "xi\u01CEng", "xi\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xiang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xiang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xiang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xiang4.mp3"],
            join: "x + iang"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["iao", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM5Nw==?fallback=true",
            audio: [null, null, null, null, null],
            join: "iao"
        }, {
            text: ["yao", "y\u0101o", "y\xE1o", "y\u01CEo", "y\xE0o"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ2Mw==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/yao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/yao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/yao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/yao4.mp3"],
            join: "y + iao"
        }, {
            text: ["biao", "bi\u0101o", "bi\xE1o", "bi\u01CEo", "bi\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/biao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/biao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/biao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/biao4.mp3"],
            join: "b + iao"
        }, {
            text: ["piao", "pi\u0101o", "pi\xE1o", "pi\u01CEo", "pi\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/piao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/piao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/piao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/piao4.mp3"],
            join: "p + iao"
        }, {
            text: ["miao", "mi\u0101o", "mi\xE1o", "mi\u01CEo", "mi\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/miao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/miao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/miao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/miao4.mp3"],
            join: "m + iao"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["diao", "di\u0101o", "di\xE1o", "di\u01CEo", "di\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/diao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/diao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/diao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/diao4.mp3"],
            join: "d + iao"
        }, {
            text: ["tiao", "ti\u0101o", "ti\xE1o", "ti\u01CEo", "ti\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tiao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tiao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tiao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tiao4.mp3"],
            join: "t + iao"
        }, {
            text: ["niao", "ni\u0101o", "ni\xE1o", "ni\u01CEo", "ni\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/niao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/niao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/niao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/niao4.mp3"],
            join: "n + iao"
        }, {
            text: ["liao", "li\u0101o", "li\xE1o", "li\u01CEo", "li\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/liao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/liao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/liao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/liao4.mp3"],
            join: "l + iao"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["jiao", "ji\u0101o", "ji\xE1o", "ji\u01CEo", "ji\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/jiao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/jiao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/jiao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/jiao4.mp3"],
            join: "j + iao"
        }, {
            text: ["qiao", "qi\u0101o", "qi\xE1o", "qi\u01CEo", "qi\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/qiao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/qiao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/qiao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/qiao4.mp3"],
            join: "q + iao"
        }, {
            text: ["xiao", "xi\u0101o", "xi\xE1o", "xi\u01CEo", "xi\xE0o"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xiao1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xiao2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xiao3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xiao4.mp3"],
            join: "x + iao"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["ie", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM5OA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ie"
        }, {
            text: ["ye", "y\u0113", "y\xE9", "y\u011B", "y\xE8"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ2NA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ye1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ye2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ye3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ye4.mp3"],
            join: "y + ie"
        }, {
            text: ["bie", "bi\u0113", "bi\xE9", "bi\u011B", "bi\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/bie1.mp3", "https://cdn.yoyochinese.com/audio/pychart/bie2.mp3", "https://cdn.yoyochinese.com/audio/pychart/bie3.mp3", "https://cdn.yoyochinese.com/audio/pychart/bie4.mp3"],
            join: "b + ie"
        }, {
            text: ["pie", "pi\u0113", "pi\xE9", "pi\u011B", "pi\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pie1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pie2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pie3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pie4.mp3"],
            join: "p + ie"
        }, {
            text: ["mie", "mi\u0113", "mi\xE9", "mi\u011B", "mi\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/mie1.mp3", "https://cdn.yoyochinese.com/audio/pychart/mie2.mp3", "https://cdn.yoyochinese.com/audio/pychart/mie3.mp3", "https://cdn.yoyochinese.com/audio/pychart/mie4.mp3"],
            join: "m + ie"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["die", "di\u0113", "di\xE9", "di\u011B", "di\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/die1.mp3", "https://cdn.yoyochinese.com/audio/pychart/die2.mp3", "https://cdn.yoyochinese.com/audio/pychart/die3.mp3", "https://cdn.yoyochinese.com/audio/pychart/die4.mp3"],
            join: "d + ie"
        }, {
            text: ["tie", "ti\u0113", "ti\xE9", "ti\u011B", "ti\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tie1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tie2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tie3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tie4.mp3"],
            join: "t + ie"
        }, {
            text: ["nie", "ni\u0113", "ni\xE9", "ni\u011B", "ni\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nie1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nie2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nie3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nie4.mp3"],
            join: "n + ie"
        }, {
            text: ["lie", "li\u0113", "li\xE9", "li\u011B", "li\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lie1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lie2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lie3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lie4.mp3"],
            join: "l + ie"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["jie", "ji\u0113", "ji\xE9", "ji\u011B", "ji\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/jie1.mp3", "https://cdn.yoyochinese.com/audio/pychart/jie2.mp3", "https://cdn.yoyochinese.com/audio/pychart/jie3.mp3", "https://cdn.yoyochinese.com/audio/pychart/jie4.mp3"],
            join: "j + ie"
        }, {
            text: ["qie", "qi\u0113", "qi\xE9", "qi\u011B", "qi\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/qie1.mp3", "https://cdn.yoyochinese.com/audio/pychart/qie2.mp3", "https://cdn.yoyochinese.com/audio/pychart/qie3.mp3", "https://cdn.yoyochinese.com/audio/pychart/qie4.mp3"],
            join: "q + ie"
        }, {
            text: ["xie", "xi\u0113", "xi\xE9", "xi\u011B", "xi\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xie1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xie2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xie3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xie4.mp3"],
            join: "x + ie"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["in", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDM5OQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "in"
        }, {
            text: ["yin", "y\u012Bn", "y\xEDn", "y\u01D0n", "y\xECn"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ2NQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/yin1.mp3", "https://cdn.yoyochinese.com/audio/pychart/yin2.mp3", "https://cdn.yoyochinese.com/audio/pychart/yin3.mp3", "https://cdn.yoyochinese.com/audio/pychart/yin4.mp3"],
            join: "y + in"
        }, {
            text: ["bin", "b\u012Bn", "b\xEDn", "b\u01D0n", "b\xECn"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/bin1.mp3", "https://cdn.yoyochinese.com/audio/pychart/bin2.mp3", "https://cdn.yoyochinese.com/audio/pychart/bin3.mp3", "https://cdn.yoyochinese.com/audio/pychart/bin4.mp3"],
            join: "b + in"
        }, {
            text: ["pin", "p\u012Bn", "p\xEDn", "p\u01D0n", "p\xECn"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pin1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pin2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pin3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pin4.mp3"],
            join: "p + in"
        }, {
            text: ["min", "m\u012Bn", "m\xEDn", "m\u01D0n", "m\xECn"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/min1.mp3", "https://cdn.yoyochinese.com/audio/pychart/min2.mp3", "https://cdn.yoyochinese.com/audio/pychart/min3.mp3", "https://cdn.yoyochinese.com/audio/pychart/min4.mp3"],
            join: "m + in"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["nin", "n\u012Bn", "n\xEDn", "n\u01D0n", "n\xECn"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nin1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nin2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nin3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nin4.mp3"],
            join: "n + in"
        }, {
            text: ["lin", "l\u012Bn", "l\xEDn", "l\u01D0n", "l\xECn"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lin1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lin2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lin3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lin4.mp3"],
            join: "l + in"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["jin", "j\u012Bn", "j\xEDn", "j\u01D0n", "j\xECn"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/jin1.mp3", "https://cdn.yoyochinese.com/audio/pychart/jin2.mp3", "https://cdn.yoyochinese.com/audio/pychart/jin3.mp3", "https://cdn.yoyochinese.com/audio/pychart/jin4.mp3"],
            join: "j + in"
        }, {
            text: ["qin", "q\u012Bn", "q\xEDn", "q\u01D0n", "q\xECn"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/qin1.mp3", "https://cdn.yoyochinese.com/audio/pychart/qin2.mp3", "https://cdn.yoyochinese.com/audio/pychart/qin3.mp3", "https://cdn.yoyochinese.com/audio/pychart/qin4.mp3"],
            join: "q + in"
        }, {
            text: ["xin", "x\u012Bn", "x\xEDn", "x\u01D0n", "x\xECn"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xin1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xin2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xin3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xin4.mp3"],
            join: "x + in"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["ing", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQwMA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ing"
        }, {
            text: ["ying", "y\u012Bng", "y\xEDng", "y\u01D0ng", "y\xECng"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ2Ng==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ying1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ying2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ying3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ying4.mp3"],
            join: "y + ing"
        }, {
            text: ["bing", "b\u012Bng", "b\xEDng", "b\u01D0ng", "b\xECng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/bing1.mp3", "https://cdn.yoyochinese.com/audio/pychart/bing2.mp3", "https://cdn.yoyochinese.com/audio/pychart/bing3.mp3", "https://cdn.yoyochinese.com/audio/pychart/bing4.mp3"],
            join: "b + ing"
        }, {
            text: ["ping", "p\u012Bng", "p\xEDng", "p\u01D0ng", "p\xECng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ping1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ping2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ping3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ping4.mp3"],
            join: "p + ing"
        }, {
            text: ["ming", "m\u012Bng", "m\xEDng", "m\u01D0ng", "m\xECng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ming1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ming2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ming3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ming4.mp3"],
            join: "m + ing"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["ding", "d\u012Bng", "d\xEDng", "d\u01D0ng", "d\xECng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ding1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ding2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ding3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ding4.mp3"],
            join: "d + ing"
        }, {
            text: ["ting", "t\u012Bng", "t\xEDng", "t\u01D0ng", "t\xECng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ting1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ting2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ting3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ting4.mp3"],
            join: "t + ing"
        }, {
            text: ["ning", "n\u012Bng", "n\xEDng", "n\u01D0ng", "n\xECng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ning1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ning2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ning3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ning4.mp3"],
            join: "n + ing"
        }, {
            text: ["ling", "l\u012Bng", "l\xEDng", "l\u01D0ng", "l\xECng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ling1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ling2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ling3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ling4.mp3"],
            join: "l + ing"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["jing", "j\u012Bng", "j\xEDng", "j\u01D0ng", "j\xECng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/jing1.mp3", "https://cdn.yoyochinese.com/audio/pychart/jing2.mp3", "https://cdn.yoyochinese.com/audio/pychart/jing3.mp3", "https://cdn.yoyochinese.com/audio/pychart/jing4.mp3"],
            join: "j + ing"
        }, {
            text: ["qing", "q\u012Bng", "q\xEDng", "q\u01D0ng", "q\xECng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/qing1.mp3", "https://cdn.yoyochinese.com/audio/pychart/qing2.mp3", "https://cdn.yoyochinese.com/audio/pychart/qing3.mp3", "https://cdn.yoyochinese.com/audio/pychart/qing4.mp3"],
            join: "q + ing"
        }, {
            text: ["xing", "x\u012Bng", "x\xEDng", "x\u01D0ng", "x\xECng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xing1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xing2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xing3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xing4.mp3"],
            join: "x + ing"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["iong", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQwMQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "iong"
        }, {
            text: ["yong", "y\u014Dng", "y\xF3ng", "y\u01D2ng", "y\xF2ng"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ2Nw==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/yong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/yong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/yong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/yong4.mp3"],
            join: "y + iong"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["jiong", "ji\u014Dng", "ji\xF3ng", "ji\u01D2ng", "ji\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/jiong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/jiong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/jiong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/jiong4.mp3"],
            join: "j + iong"
        }, {
            text: ["qiong", "qi\u014Dng", "qi\xF3ng", "qi\u01D2ng", "qi\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/qiong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/qiong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/qiong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/qiong4.mp3"],
            join: "q + iong"
        }, {
            text: ["xiong", "xi\u014Dng", "xi\xF3ng", "xi\u01D2ng", "xi\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xiong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xiong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xiong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xiong4.mp3"],
            join: "x + iong"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["iou", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQwMg==?fallback=true",
            audio: [null, null, null, null, null],
            join: "iou"
        }, {
            text: ["you", "y\u014Du", "y\xF3u", "y\u01D2u", "y\xF2u"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ2OA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/you1.mp3", "https://cdn.yoyochinese.com/audio/pychart/you2.mp3", "https://cdn.yoyochinese.com/audio/pychart/you3.mp3", "https://cdn.yoyochinese.com/audio/pychart/you4.mp3"],
            join: "y + iou"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["miu", "mi\u016B", "mi\xFA", "mi\u01D4", "mi\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/miu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/miu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/miu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/miu4.mp3"],
            join: "m + iou"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["diu", "di\u016B", "di\xFA", "di\u01D4", "di\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/diu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/diu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/diu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/diu4.mp3"],
            join: "d + iou"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["niu", "ni\u016B", "ni\xFA", "ni\u01D4", "ni\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/niu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/niu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/niu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/niu4.mp3"],
            join: "n + iou"
        }, {
            text: ["liu", "li\u016B", "li\xFA", "li\u01D4", "li\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/liu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/liu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/liu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/liu4.mp3"],
            join: "l + iou"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["jiu", "ji\u016B", "ji\xFA", "ji\u01D4", "ji\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/jiu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/jiu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/jiu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/jiu4.mp3"],
            join: "j + iou"
        }, {
            text: ["qiu", "qi\u016B", "qi\xFA", "qi\u01D4", "qi\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/qiu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/qiu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/qiu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/qiu4.mp3"],
            join: "q + iou"
        }, {
            text: ["xiu", "xi\u016B", "xi\xFA", "xi\u01D4", "xi\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xiu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xiu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xiu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xiu4.mp3"],
            join: "x + iou"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["o", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQwMw==?fallback=true",
            audio: [null, null, null, null, null],
            join: "o"
        }, {
            text: ["o", "\u014D", "\xF3", "\u01D2", "\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/o1.mp3", "https://cdn.yoyochinese.com/audio/pychart/o2.mp3", "https://cdn.yoyochinese.com/audio/pychart/o3.mp3", "https://cdn.yoyochinese.com/audio/pychart/o4.mp3"],
            join: "o"
        }, {
            text: ["bo", "b\u014D", "b\xF3", "b\u01D2", "b\xF2"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQyOQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/bo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/bo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/bo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/bo4.mp3"],
            join: "b + o"
        }, {
            text: ["po", "p\u014D", "p\xF3", "p\u01D2", "p\xF2"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQzOA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/po1.mp3", "https://cdn.yoyochinese.com/audio/pychart/po2.mp3", "https://cdn.yoyochinese.com/audio/pychart/po3.mp3", "https://cdn.yoyochinese.com/audio/pychart/po4.mp3"],
            join: "p + o"
        }, {
            text: ["mo", "m\u014D", "m\xF3", "m\u01D2", "m\xF2"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQzNw==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/mo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/mo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/mo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/mo4.mp3"],
            join: "m + o"
        }, {
            text: ["fo", "f\u014D", "f\xF3", "f\u01D2", "f\xF2"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQzMg==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/fo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/fo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/fo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/fo4.mp3"],
            join: "f + o"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["ong", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQwNA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ong"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["dong", "d\u014Dng", "d\xF3ng", "d\u01D2ng", "d\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/dong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/dong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/dong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/dong4.mp3"],
            join: "d + ong"
        }, {
            text: ["tong", "t\u014Dng", "t\xF3ng", "t\u01D2ng", "t\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tong4.mp3"],
            join: "t + ong"
        }, {
            text: ["nong", "n\u014Dng", "n\xF3ng", "n\u01D2ng", "n\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nong4.mp3"],
            join: "n + ong"
        }, {
            text: ["long", "l\u014Dng", "l\xF3ng", "l\u01D2ng", "l\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/long1.mp3", "https://cdn.yoyochinese.com/audio/pychart/long2.mp3", "https://cdn.yoyochinese.com/audio/pychart/long3.mp3", "https://cdn.yoyochinese.com/audio/pychart/long4.mp3"],
            join: "l + ong"
        }, {
            text: ["gong", "g\u014Dng", "g\xF3ng", "g\u01D2ng", "g\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/gong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/gong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/gong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/gong4.mp3"],
            join: "g + ong"
        }, {
            text: ["kong", "k\u014Dng", "k\xF3ng", "k\u01D2ng", "k\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kong4.mp3"],
            join: "k + ong"
        }, {
            text: ["hong", "h\u014Dng", "h\xF3ng", "h\u01D2ng", "h\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/hong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/hong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/hong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/hong4.mp3"],
            join: "h + ong"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zong", "z\u014Dng", "z\xF3ng", "z\u01D2ng", "z\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zong4.mp3"],
            join: "z + ong"
        }, {
            text: ["cong", "c\u014Dng", "c\xF3ng", "c\u01D2ng", "c\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cong4.mp3"],
            join: "c + ong"
        }, {
            text: ["song", "s\u014Dng", "s\xF3ng", "s\u01D2ng", "s\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/song1.mp3", "https://cdn.yoyochinese.com/audio/pychart/song2.mp3", "https://cdn.yoyochinese.com/audio/pychart/song3.mp3", "https://cdn.yoyochinese.com/audio/pychart/song4.mp3"],
            join: "s + ong"
        }, {
            text: ["zhong", "zh\u014Dng", "zh\xF3ng", "zh\u01D2ng", "zh\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhong4.mp3"],
            join: "zh + ong"
        }, {
            text: ["chong", "ch\u014Dng", "ch\xF3ng", "ch\u01D2ng", "ch\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chong4.mp3"],
            join: "ch + ong"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["rong", "r\u014Dng", "r\xF3ng", "r\u01D2ng", "r\xF2ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/rong1.mp3", "https://cdn.yoyochinese.com/audio/pychart/rong2.mp3", "https://cdn.yoyochinese.com/audio/pychart/rong3.mp3", "https://cdn.yoyochinese.com/audio/pychart/rong4.mp3"],
            join: "r + ong"
        }],
        [{
            text: ["ou", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQwNQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ou"
        }, {
            text: ["ou", "\u014Du", "\xF3u", "\u01D2u", "\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ou4.mp3"],
            join: "ou"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["pou", "p\u014Du", "p\xF3u", "p\u01D2u", "p\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pou4.mp3"],
            join: "p + ou"
        }, {
            text: ["mou", "m\u014Du", "m\xF3u", "m\u01D2u", "m\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/mou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/mou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/mou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/mou4.mp3"],
            join: "m + ou"
        }, {
            text: ["fou", "f\u014Du", "f\xF3u", "f\u01D2u", "f\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/fou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/fou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/fou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/fou4.mp3"],
            join: "f + ou"
        }, {
            text: ["dou", "d\u014Du", "d\xF3u", "d\u01D2u", "d\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/dou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/dou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/dou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/dou4.mp3"],
            join: "d + ou"
        }, {
            text: ["tou", "t\u014Du", "t\xF3u", "t\u01D2u", "t\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tou4.mp3"],
            join: "t + ou"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["lou", "l\u014Du", "l\xF3u", "l\u01D2u", "l\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lou4.mp3"],
            join: "l + ou"
        }, {
            text: ["gou", "g\u014Du", "g\xF3u", "g\u01D2u", "g\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/gou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/gou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/gou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/gou4.mp3"],
            join: "g + ou"
        }, {
            text: ["kou", "k\u014Du", "k\xF3u", "k\u01D2u", "k\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kou4.mp3"],
            join: "k + ou"
        }, {
            text: ["hou", "h\u014Du", "h\xF3u", "h\u01D2u", "h\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/hou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/hou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/hou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/hou4.mp3"],
            join: "h + ou"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zou", "z\u014Du", "z\xF3u", "z\u01D2u", "z\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zou4.mp3"],
            join: "z + ou"
        }, {
            text: ["cou", "c\u014Du", "c\xF3u", "c\u01D2u", "c\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cou4.mp3"],
            join: "c + ou"
        }, {
            text: ["sou", "s\u014Du", "s\xF3u", "s\u01D2u", "s\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/sou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/sou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/sou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/sou4.mp3"],
            join: "s + ou"
        }, {
            text: ["zhou", "zh\u014Du", "zh\xF3u", "zh\u01D2u", "zh\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhou4.mp3"],
            join: "zh + ou"
        }, {
            text: ["chou", "ch\u014Du", "ch\xF3u", "ch\u01D2u", "ch\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chou4.mp3"],
            join: "ch + ou"
        }, {
            text: ["shou", "sh\u014Du", "sh\xF3u", "sh\u01D2u", "sh\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shou4.mp3"],
            join: "sh + ou"
        }, {
            text: ["rou", "r\u014Du", "r\xF3u", "r\u01D2u", "r\xF2u"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/rou1.mp3", "https://cdn.yoyochinese.com/audio/pychart/rou2.mp3", "https://cdn.yoyochinese.com/audio/pychart/rou3.mp3", "https://cdn.yoyochinese.com/audio/pychart/rou4.mp3"],
            join: "r + ou"
        }],
        [{
            text: ["u", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQwNg==?fallback=true",
            audio: [null, null, null, null, null],
            join: "u"
        }, {
            text: ["wu", "w\u016B", "w\xFA", "w\u01D4", "w\xF9"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ1NQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/wu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/wu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/wu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/wu4.mp3"],
            join: "w + u"
        }, {
            text: ["bu", "b\u016B", "b\xFA", "b\u01D4", "b\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/bu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/bu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/bu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/bu4.mp3"],
            join: "b + u"
        }, {
            text: ["pu", "p\u016B", "p\xFA", "p\u01D4", "p\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/pu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/pu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/pu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/pu4.mp3"],
            join: "p + u"
        }, {
            text: ["mu", "m\u016B", "m\xFA", "m\u01D4", "m\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/mu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/mu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/mu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/mu4.mp3"],
            join: "m + u"
        }, {
            text: ["fu", "f\u016B", "f\xFA", "f\u01D4", "f\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/fu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/fu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/fu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/fu4.mp3"],
            join: "f + u"
        }, {
            text: ["du", "d\u016B", "d\xFA", "d\u01D4", "d\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/du1.mp3", "https://cdn.yoyochinese.com/audio/pychart/du2.mp3", "https://cdn.yoyochinese.com/audio/pychart/du3.mp3", "https://cdn.yoyochinese.com/audio/pychart/du4.mp3"],
            join: "d + u"
        }, {
            text: ["tu", "t\u016B", "t\xFA", "t\u01D4", "t\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tu4.mp3"],
            join: "t + u"
        }, {
            text: ["nu", "n\u016B", "n\xFA", "n\u01D4", "n\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nu4.mp3"],
            join: "n + u"
        }, {
            text: ["lu", "l\u016B", "l\xFA", "l\u01D4", "l\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lu4.mp3"],
            join: "l + u"
        }, {
            text: ["gu", "g\u016B", "g\xFA", "g\u01D4", "g\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/gu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/gu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/gu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/gu4.mp3"],
            join: "g + u"
        }, {
            text: ["ku", "k\u016B", "k\xFA", "k\u01D4", "k\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ku1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ku2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ku3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ku4.mp3"],
            join: "k + u"
        }, {
            text: ["hu", "h\u016B", "h\xFA", "h\u01D4", "h\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/hu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/hu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/hu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/hu4.mp3"],
            join: "h + u"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zu", "z\u016B", "z\xFA", "z\u01D4", "z\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zu4.mp3"],
            join: "z + u"
        }, {
            text: ["cu", "c\u016B", "c\xFA", "c\u01D4", "c\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cu4.mp3"],
            join: "c + u"
        }, {
            text: ["su", "s\u016B", "s\xFA", "s\u01D4", "s\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/su1.mp3", "https://cdn.yoyochinese.com/audio/pychart/su2.mp3", "https://cdn.yoyochinese.com/audio/pychart/su3.mp3", "https://cdn.yoyochinese.com/audio/pychart/su4.mp3"],
            join: "s + u"
        }, {
            text: ["zhu", "zh\u016B", "zh\xFA", "zh\u01D4", "zh\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhu4.mp3"],
            join: "zh + u"
        }, {
            text: ["chu", "ch\u016B", "ch\xFA", "ch\u01D4", "ch\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chu4.mp3"],
            join: "ch + u"
        }, {
            text: ["shu", "sh\u016B", "sh\xFA", "sh\u01D4", "sh\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shu4.mp3"],
            join: "sh + u"
        }, {
            text: ["ru", "r\u016B", "r\xFA", "r\u01D4", "r\xF9"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ru1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ru2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ru3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ru4.mp3"],
            join: "r + u"
        }],
        [{
            text: ["ua", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQwNw==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ua"
        }, {
            text: ["wa", "w\u0101", "w\xE1", "w\u01CE", "w\xE0"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ0Nw==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/wa1.mp3", "https://cdn.yoyochinese.com/audio/pychart/wa2.mp3", "https://cdn.yoyochinese.com/audio/pychart/wa3.mp3", "https://cdn.yoyochinese.com/audio/pychart/wa4.mp3"],
            join: "w + ua"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["gua", "gu\u0101", "gu\xE1", "gu\u01CE", "gu\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/gua1.mp3", "https://cdn.yoyochinese.com/audio/pychart/gua2.mp3", "https://cdn.yoyochinese.com/audio/pychart/gua3.mp3", "https://cdn.yoyochinese.com/audio/pychart/gua4.mp3"],
            join: "g + ua"
        }, {
            text: ["kua", "ku\u0101", "ku\xE1", "ku\u01CE", "ku\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kua1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kua2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kua3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kua4.mp3"],
            join: "k + ua"
        }, {
            text: ["hua", "hu\u0101", "hu\xE1", "hu\u01CE", "hu\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/hua1.mp3", "https://cdn.yoyochinese.com/audio/pychart/hua2.mp3", "https://cdn.yoyochinese.com/audio/pychart/hua3.mp3", "https://cdn.yoyochinese.com/audio/pychart/hua4.mp3"],
            join: "h + ua"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zhua", "zhu\u0101", "zhu\xE1", "zhu\u01CE", "zhu\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhua1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhua2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhua3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhua4.mp3"],
            join: "zh + ua"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["shua", "shu\u0101", "shu\xE1", "shu\u01CE", "shu\xE0"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shua1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shua2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shua3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shua4.mp3"],
            join: "sh + ua"
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["uai", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQwOA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "uai"
        }, {
            text: ["wai", "w\u0101i", "w\xE1i", "w\u01CEi", "w\xE0i"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ0OA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/wai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/wai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/wai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/wai4.mp3"],
            join: "w + uai"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["guai", "gu\u0101i", "gu\xE1i", "gu\u01CEi", "gu\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/guai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/guai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/guai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/guai4.mp3"],
            join: "g + uai"
        }, {
            text: ["kuai", "ku\u0101i", "ku\xE1i", "ku\u01CEi", "ku\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kuai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kuai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kuai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kuai4.mp3"],
            join: "k + uai"
        }, {
            text: ["huai", "hu\u0101i", "hu\xE1i", "hu\u01CEi", "hu\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/huai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/huai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/huai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/huai4.mp3"],
            join: "h + uai"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zhuai", "zhu\u0101i", "zhu\xE1i", "zhu\u01CEi", "zhu\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhuai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhuai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhuai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhuai4.mp3"],
            join: "zh + uai"
        }, {
            text: ["chuai", "chu\u0101i", "chu\xE1i", "chu\u01CEi", "chu\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chuai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chuai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chuai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chuai4.mp3"],
            join: "ch + uai"
        }, {
            text: ["shuai", "shu\u0101i", "shu\xE1i", "shu\u01CEi", "shu\xE0i"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shuai1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shuai2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shuai3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shuai4.mp3"],
            join: "sh + uai"
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["uan", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQwOQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "uan"
        }, {
            text: ["wan", "w\u0101n", "w\xE1n", "w\u01CEn", "w\xE0n"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ0OQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/wan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/wan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/wan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/wan4.mp3"],
            join: "w + uan"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["duan", "du\u0101n", "du\xE1n", "du\u01CEn", "du\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/duan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/duan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/duan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/duan4.mp3"],
            join: "d + uan"
        }, {
            text: ["tuan", "tu\u0101n", "tu\xE1n", "tu\u01CEn", "tu\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tuan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tuan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tuan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tuan4.mp3"],
            join: "t + uan"
        }, {
            text: ["nuan", "nu\u0101n", "nu\xE1n", "nu\u01CEn", "nu\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nuan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nuan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nuan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nuan4.mp3"],
            join: "n + uan"
        }, {
            text: ["luan", "lu\u0101n", "lu\xE1n", "lu\u01CEn", "lu\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/luan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/luan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/luan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/luan4.mp3"],
            join: "l + uan"
        }, {
            text: ["guan", "gu\u0101n", "gu\xE1n", "gu\u01CEn", "gu\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/guan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/guan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/guan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/guan4.mp3"],
            join: "g + uan"
        }, {
            text: ["kuan", "ku\u0101n", "ku\xE1n", "ku\u01CEn", "ku\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kuan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kuan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kuan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kuan4.mp3"],
            join: "k + uan"
        }, {
            text: ["huan", "hu\u0101n", "hu\xE1n", "hu\u01CEn", "hu\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/huan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/huan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/huan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/huan4.mp3"],
            join: "h + uan"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zuan", "zu\u0101n", "zu\xE1n", "zu\u01CEn", "zu\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zuan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zuan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zuan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zuan4.mp3"],
            join: "z + uan"
        }, {
            text: ["cuan", "cu\u0101n", "cu\xE1n", "cu\u01CEn", "cu\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cuan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cuan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cuan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cuan4.mp3"],
            join: "c + uan"
        }, {
            text: ["suan", "su\u0101n", "su\xE1n", "su\u01CEn", "su\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/suan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/suan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/suan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/suan4.mp3"],
            join: "s + uan"
        }, {
            text: ["zhuan", "zhu\u0101n", "zhu\xE1n", "zhu\u01CEn", "zhu\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhuan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhuan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhuan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhuan4.mp3"],
            join: "zh + uan"
        }, {
            text: ["chuan", "chu\u0101n", "chu\xE1n", "chu\u01CEn", "chu\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chuan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chuan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chuan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chuan4.mp3"],
            join: "ch + uan"
        }, {
            text: ["shuan", "shu\u0101n", "shu\xE1n", "shu\u01CEn", "shu\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shuan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shuan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shuan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shuan4.mp3"],
            join: "sh + uan"
        }, {
            text: ["ruan", "ru\u0101n", "ru\xE1n", "ru\u01CEn", "ru\xE0n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ruan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ruan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ruan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ruan4.mp3"],
            join: "r + uan"
        }],
        [{
            text: ["uang", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQxMA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "uang"
        }, {
            text: ["wang", "w\u0101ng", "w\xE1ng", "w\u01CEng", "w\xE0ng"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ1MA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/wang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/wang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/wang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/wang4.mp3"],
            join: "w + uang"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["guang", "gu\u0101ng", "gu\xE1ng", "gu\u01CEng", "gu\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/guang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/guang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/guang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/guang4.mp3"],
            join: "g + uang"
        }, {
            text: ["kuang", "ku\u0101ng", "ku\xE1ng", "ku\u01CEng", "ku\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kuang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kuang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kuang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kuang4.mp3"],
            join: "k + uang"
        }, {
            text: ["huang", "hu\u0101ng", "hu\xE1ng", "hu\u01CEng", "hu\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/huang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/huang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/huang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/huang4.mp3"],
            join: "h + uang"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zhuang", "zhu\u0101ng", "zhu\xE1ng", "zhu\u01CEng", "zhu\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhuang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhuang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhuang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhuang4.mp3"],
            join: "zh + uang"
        }, {
            text: ["chuang", "chu\u0101ng", "chu\xE1ng", "chu\u01CEng", "chu\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chuang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chuang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chuang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chuang4.mp3"],
            join: "ch + uang"
        }, {
            text: ["shuang", "shu\u0101ng", "shu\xE1ng", "shu\u01CEng", "shu\xE0ng"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shuang1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shuang2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shuang3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shuang4.mp3"],
            join: "sh + uang"
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["uei", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQxMg==?fallback=true",
            audio: [null, null, null, null, null],
            join: "uei"
        }, {
            text: ["wei", "w\u0113i", "w\xE9i", "w\u011Bi", "w\xE8i"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ1MQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/wei1.mp3", "https://cdn.yoyochinese.com/audio/pychart/wei2.mp3", "https://cdn.yoyochinese.com/audio/pychart/wei3.mp3", "https://cdn.yoyochinese.com/audio/pychart/wei4.mp3"],
            join: "w + uei"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["dui", "du\u012B", "du\xED", "du\u01D0", "du\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/dui1.mp3", "https://cdn.yoyochinese.com/audio/pychart/dui2.mp3", "https://cdn.yoyochinese.com/audio/pychart/dui3.mp3", "https://cdn.yoyochinese.com/audio/pychart/dui4.mp3"],
            join: "d + uei"
        }, {
            text: ["tui", "tu\u012B", "tu\xED", "tu\u01D0", "tu\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tui1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tui2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tui3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tui4.mp3"],
            join: "t + uei"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["gui", "gu\u012B", "gu\xED", "gu\u01D0", "gu\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/gui1.mp3", "https://cdn.yoyochinese.com/audio/pychart/gui2.mp3", "https://cdn.yoyochinese.com/audio/pychart/gui3.mp3", "https://cdn.yoyochinese.com/audio/pychart/gui4.mp3"],
            join: "g + uei"
        }, {
            text: ["kui", "ku\u012B", "ku\xED", "ku\u01D0", "ku\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kui1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kui2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kui3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kui4.mp3"],
            join: "k + uei"
        }, {
            text: ["hui", "hu\u012B", "hu\xED", "hu\u01D0", "hu\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/hui1.mp3", "https://cdn.yoyochinese.com/audio/pychart/hui2.mp3", "https://cdn.yoyochinese.com/audio/pychart/hui3.mp3", "https://cdn.yoyochinese.com/audio/pychart/hui4.mp3"],
            join: "h + uei"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zui", "zu\u012B", "zu\xED", "zu\u01D0", "zu\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zui1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zui2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zui3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zui4.mp3"],
            join: "z + uei"
        }, {
            text: ["cui", "cu\u012B", "cu\xED", "cu\u01D0", "cu\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cui1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cui2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cui3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cui4.mp3"],
            join: "c + uei"
        }, {
            text: ["sui", "su\u012B", "su\xED", "su\u01D0", "su\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/sui1.mp3", "https://cdn.yoyochinese.com/audio/pychart/sui2.mp3", "https://cdn.yoyochinese.com/audio/pychart/sui3.mp3", "https://cdn.yoyochinese.com/audio/pychart/sui4.mp3"],
            join: "s + uei"
        }, {
            text: ["zhui", "zhu\u012B", "zhu\xED", "zhu\u01D0", "zhu\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhui1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhui2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhui3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhui4.mp3"],
            join: "zh + uei"
        }, {
            text: ["chui", "chu\u012B", "chu\xED", "chu\u01D0", "chu\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chui1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chui2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chui3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chui4.mp3"],
            join: "ch + uei"
        }, {
            text: ["shui", "shu\u012B", "shu\xED", "shu\u01D0", "shu\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shui1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shui2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shui3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shui4.mp3"],
            join: "sh + uei"
        }, {
            text: ["rui", "ru\u012B", "ru\xED", "ru\u01D0", "ru\xEC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/rui1.mp3", "https://cdn.yoyochinese.com/audio/pychart/rui2.mp3", "https://cdn.yoyochinese.com/audio/pychart/rui3.mp3", "https://cdn.yoyochinese.com/audio/pychart/rui4.mp3"],
            join: "r + uei"
        }],
        [{
            text: ["uen", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQxMw==?fallback=true",
            audio: [null, null, null, null, null],
            join: "uen"
        }, {
            text: ["wen", "w\u0113n", "w\xE9n", "w\u011Bn", "w\xE8n"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ1Mg==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/wen1.mp3", "https://cdn.yoyochinese.com/audio/pychart/wen2.mp3", "https://cdn.yoyochinese.com/audio/pychart/wen3.mp3", "https://cdn.yoyochinese.com/audio/pychart/wen4.mp3"],
            join: "w + uen"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["dun", "d\u016Bn", "d\xFAn", "d\u01D4n", "d\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/dun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/dun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/dun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/dun4.mp3"],
            join: "d + uen"
        }, {
            text: ["tun", "t\u016Bn", "t\xFAn", "t\u01D4n", "t\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tun4.mp3"],
            join: "t + uen"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["lun", "l\u016Bn", "l\xFAn", "l\u01D4n", "l\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lun4.mp3"],
            join: "l + uen"
        }, {
            text: ["gun", "g\u016Bn", "g\xFAn", "g\u01D4n", "g\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/gun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/gun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/gun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/gun4.mp3"],
            join: "g + uen"
        }, {
            text: ["kun", "k\u016Bn", "k\xFAn", "k\u01D4n", "k\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kun4.mp3"],
            join: "k + uen"
        }, {
            text: ["hun", "h\u016Bn", "h\xFAn", "h\u01D4n", "h\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/hun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/hun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/hun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/hun4.mp3"],
            join: "h + uen"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zun", "z\u016Bn", "z\xFAn", "z\u01D4n", "z\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zun4.mp3"],
            join: "z + uen"
        }, {
            text: ["cun", "c\u016Bn", "c\xFAn", "c\u01D4n", "c\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cun4.mp3"],
            join: "c + uen"
        }, {
            text: ["sun", "s\u016Bn", "s\xFAn", "s\u01D4n", "s\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/sun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/sun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/sun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/sun4.mp3"],
            join: "s + uen"
        }, {
            text: ["zhun", "zh\u016Bn", "zh\xFAn", "zh\u01D4n", "zh\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhun4.mp3"],
            join: "zh + uen"
        }, {
            text: ["chun", "ch\u016Bn", "ch\xFAn", "ch\u01D4n", "ch\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chun4.mp3"],
            join: "ch + uen"
        }, {
            text: ["shun", "sh\u016Bn", "sh\xFAn", "sh\u01D4n", "sh\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shun4.mp3"],
            join: "sh + uen"
        }, {
            text: ["run", "r\u016Bn", "r\xFAn", "r\u01D4n", "r\xF9n"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/run1.mp3", "https://cdn.yoyochinese.com/audio/pychart/run2.mp3", "https://cdn.yoyochinese.com/audio/pychart/run3.mp3", "https://cdn.yoyochinese.com/audio/pychart/run4.mp3"],
            join: "r + uen"
        }],
        [{
            text: ["ueng", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQxMQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "ueng"
        }, {
            text: ["weng", "w\u0113ng", "w\xE9ng", "w\u011Bng", "w\xE8ng"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ1Mw==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/weng1.mp3", "https://cdn.yoyochinese.com/audio/pychart/weng2.mp3", "https://cdn.yoyochinese.com/audio/pychart/weng3.mp3", "https://cdn.yoyochinese.com/audio/pychart/weng4.mp3"],
            join: "w + ueng"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["uo", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQxNA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "uo"
        }, {
            text: ["wo", "w\u014D", "w\xF3", "w\u01D2", "w\xF2"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ1NA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/wo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/wo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/wo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/wo4.mp3"],
            join: "w + uo"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["duo", "du\u014D", "du\xF3", "du\u01D2", "du\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/duo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/duo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/duo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/duo4.mp3"],
            join: "d + uo"
        }, {
            text: ["tuo", "tu\u014D", "tu\xF3", "tu\u01D2", "tu\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/tuo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/tuo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/tuo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/tuo4.mp3"],
            join: "t + uo"
        }, {
            text: ["nuo", "nu\u014D", "nu\xF3", "nu\u01D2", "nu\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nuo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nuo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nuo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nuo4.mp3"],
            join: "n + uo"
        }, {
            text: ["luo", "lu\u014D", "lu\xF3", "lu\u01D2", "lu\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/luo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/luo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/luo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/luo4.mp3"],
            join: "l + uo"
        }, {
            text: ["guo", "gu\u014D", "gu\xF3", "gu\u01D2", "gu\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/guo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/guo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/guo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/guo4.mp3"],
            join: "g + uo"
        }, {
            text: ["kuo", "ku\u014D", "ku\xF3", "ku\u01D2", "ku\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/kuo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/kuo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/kuo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/kuo4.mp3"],
            join: "k + uo"
        }, {
            text: ["huo", "hu\u014D", "hu\xF3", "hu\u01D2", "hu\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/huo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/huo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/huo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/huo4.mp3"],
            join: "h + uo"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["zuo", "zu\u014D", "zu\xF3", "zu\u01D2", "zu\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zuo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zuo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zuo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zuo4.mp3"],
            join: "z + uo"
        }, {
            text: ["cuo", "cu\u014D", "cu\xF3", "cu\u01D2", "cu\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/cuo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/cuo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/cuo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/cuo4.mp3"],
            join: "c + uo"
        }, {
            text: ["suo", "su\u014D", "su\xF3", "su\u01D2", "su\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/suo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/suo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/suo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/suo4.mp3"],
            join: "s + uo"
        }, {
            text: ["zhuo", "zhu\u014D", "zhu\xF3", "zhu\u01D2", "zhu\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/zhuo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhuo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhuo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/zhuo4.mp3"],
            join: "zh + uo"
        }, {
            text: ["chuo", "chu\u014D", "chu\xF3", "chu\u01D2", "chu\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/chuo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/chuo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/chuo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/chuo4.mp3"],
            join: "ch + uo"
        }, {
            text: ["shuo", "shu\u014D", "shu\xF3", "shu\u01D2", "shu\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/shuo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/shuo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/shuo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/shuo4.mp3"],
            join: "sh + uo"
        }, {
            text: ["ruo", "ru\u014D", "ru\xF3", "ru\u01D2", "ru\xF2"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ruo1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ruo2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ruo3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ruo4.mp3"],
            join: "r + uo"
        }],
        [{
            text: ["\xFC", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQxNQ==?fallback=true",
            audio: [null, null, null, null, null],
            join: "\xFC"
        }, {
            text: ["yu", "y\u016B", "y\xFA", "y\u01D4", "y\xF9"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ2OQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/yu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/yu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/yu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/yu4.mp3"],
            join: "y + \xFC"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["n\xFC", "n\u01D6", "n\u01D8", "n\u01DA", "n\u01DC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nv1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nv2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nv3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nv4.mp3"],
            join: "n + \xFC"
        }, {
            text: ["l\xFC", "l\u01D6", "l\u01D8", "l\u01DA", "l\u01DC"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lv1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lv2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lv3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lv4.mp3"],
            join: "l + \xFC"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["ju", "j\u016B", "j\xFA", "j\u01D4", "j\xF9"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQzMw==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/ju1.mp3", "https://cdn.yoyochinese.com/audio/pychart/ju2.mp3", "https://cdn.yoyochinese.com/audio/pychart/ju3.mp3", "https://cdn.yoyochinese.com/audio/pychart/ju4.mp3"],
            join: "j + \xFC"
        }, {
            text: ["qu", "q\u016B", "q\xFA", "q\u01D4", "q\xF9"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQzOQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/qu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/qu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/qu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/qu4.mp3"],
            join: "q + \xFC"
        }, {
            text: ["xu", "x\u016B", "x\xFA", "x\u01D4", "x\xF9"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ1Ng==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xu1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xu2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xu3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xu4.mp3"],
            join: "x + \xFC"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["\xFCan", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQxNg==?fallback=true",
            audio: [null, null, null, null, null],
            join: "\xFCan"
        }, {
            text: ["yuan", "yu\u0101n", "yu\xE1n", "yu\u01CEn", "yu\xE0n"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ3MA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/yuan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/yuan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/yuan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/yuan4.mp3"],
            join: "y + \xFCan"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["juan", "ju\u0101n", "j\xFC\xE1n", "j\xFC\u01CEn", "j\xFC\xE0n"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQzNA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/juan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/juan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/juan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/juan4.mp3"],
            join: "j + \xFCan"
        }, {
            text: ["quan", "qu\u0101n", "qu\xE1n", "qu\u01CEn", "qu\xE0n"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ0MA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/quan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/quan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/quan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/quan4.mp3"],
            join: "q + \xFCan"
        }, {
            text: ["xuan", "xu\u0101n", "xu\xE1n", "xu\u01CEn", "xu\xE0n"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ1Nw==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xuan1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xuan2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xuan3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xuan4.mp3"],
            join: "x + \xFCan"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["\xFCe", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQxNw==?fallback=true",
            audio: [null, null, null, null, null],
            join: "\xFCe"
        }, {
            text: ["yue", "yu\u0113", "yu\xE9", "yu\u011B", "yu\xE8"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ3MQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/yue1.mp3", "https://cdn.yoyochinese.com/audio/pychart/yue2.mp3", "https://cdn.yoyochinese.com/audio/pychart/yue3.mp3", "https://cdn.yoyochinese.com/audio/pychart/yue4.mp3"],
            join: "y + \xFCe"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["n\xFCe", "n\xFC\u0113", "n\xFC\xE9", "n\xFC\u011B", "n\xFC\xE8"],
            type: 2,
            videoUrl: "",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/nue1.mp3", "https://cdn.yoyochinese.com/audio/pychart/nue2.mp3", "https://cdn.yoyochinese.com/audio/pychart/nue3.mp3", "https://cdn.yoyochinese.com/audio/pychart/nue4.mp3"],
            join: "n + \xFCe"
        }, {
            text: ["l\xFCe", "l\xFC\u0113", "l\xFC\xE9", "l\xFC\u011B", "l\xFC\xE8"],
            type: 2,
            videoUrl: null,
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/lve1.mp3", "https://cdn.yoyochinese.com/audio/pychart/lve2.mp3", "https://cdn.yoyochinese.com/audio/pychart/lve3.mp3", "https://cdn.yoyochinese.com/audio/pychart/lve4.mp3"],
            join: "l + \xFCe"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["jue", "ju\u0113", "ju\xE9", "ju\u011B", "ju\xE8"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQzNQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/jue1.mp3", "https://cdn.yoyochinese.com/audio/pychart/jue2.mp3", "https://cdn.yoyochinese.com/audio/pychart/jue3.mp3", "https://cdn.yoyochinese.com/audio/pychart/jue4.mp3"],
            join: "j + \xFCe"
        }, {
            text: ["que", "qu\u0113", "qu\xE9", "qu\u011B", "qu\xE8"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ0MQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/que1.mp3", "https://cdn.yoyochinese.com/audio/pychart/que2.mp3", "https://cdn.yoyochinese.com/audio/pychart/que3.mp3", "https://cdn.yoyochinese.com/audio/pychart/que4.mp3"],
            join: "q + \xFCe"
        }, {
            text: ["xue", "xu\u0113", "xu\xE9", "xu\u011B", "xu\xE8"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ1OA==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xue1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xue2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xue3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xue4.mp3"],
            join: "x + \xFCe"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }],
        [{
            text: ["\xFCn", "", "", "", ""],
            type: 1,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQxOA==?fallback=true",
            audio: [null, null, null, null, null],
            join: "\xFCn"
        }, {
            text: ["yun", "y\u016Bn", "y\xFAn", "y\u01D4n", "y\xF9n"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ3Mg==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/yun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/yun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/yun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/yun4.mp3"],
            join: "y + \xFCn"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: ["jun", "j\u016Bn", "j\u01D4n", "j\u01D4n", "j\xF9n"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQzNg==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/jun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/jun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/jun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/jun4.mp3"],
            join: "j + \xFCn"
        }, {
            text: ["qun", "q\u016Bn", "q\xFAn", "q\u01D4n", "q\xF9n"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ0Mg==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/qun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/qun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/qun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/qun4.mp3"],
            join: "q + \xFCn"
        }, {
            text: ["xun", "x\u016Bn", "x\xFAn", "x\u01D4n", "x\xF9n"],
            type: 2,
            videoUrl: "https://videos.cdn.spotlightr.com/watch/MTQxMDQ1OQ==?fallback=true",
            audio: [null, "https://cdn.yoyochinese.com/audio/pychart/xun1.mp3", "https://cdn.yoyochinese.com/audio/pychart/xun2.mp3", "https://cdn.yoyochinese.com/audio/pychart/xun3.mp3", "https://cdn.yoyochinese.com/audio/pychart/xun4.mp3"],
            join: "x + \xFCn"
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }, {
            text: [""],
            isNull: !0
        }]
    ];
    window.addEventListener("load", function() {
        function x() {
            $(".search-results .results").remove();
            var o = $("#searchInput").val();
            if ($.trim(o)) {
                o = o.toLowerCase();
                var h = [],
                    p = [],
                    n = [],
                    i = [];
                for (let t = 0; t < a.length; t++) p = p.concat(a[t].filter(c => c.text[0] == o.replace("v", "\xFC") || c.text[0] == o.replace("v", "u"))), n = n.concat(a[t].filter(c => c.text[0].length != o.length && (c.text[0].indexOf(o.replace("v", "\xFC")) > -1 || c.text[0].indexOf(o.replace("v", "u")) > -1)));
                p.concat(n).concat(i).forEach(t => {
                    h.push(`<li class="results">${t.text[0]}</li>`)
                }), h.length == 0 ? $(".search-results").html('<li class="results-alert">Sorry, we couldn\u2019t find it! Please check your spelling and try again!</li>') : $(".search-results").html(h.join("")), $(".search-results").show()
            }
        }
        var g = 0;

        function v() {
            var o = $("#pinyinChart"),
                h = [];
            for (let n = 0; n < a.length; n++) {
                let i = a[n];
                var p = "";
                for (let t = 0; t < i.length; t++) {
                    let {
                        text: c,
                        isNull: s
                    } = i[t];
                    p += `<td class="${t==0?"td-cell":""} td-pinyin__${c[0]} ${!s||"null"}" 
              data-index="[${n},${t}]"
            >${c[0]}</td>`
                }
                h.push(`<tr class="${n==0?"tr-header":""}">${p}</tr>`)
            }
            o.html(h.join("")), $("#pinyinChart").on("click", "td:not(.null)", function(n) {
                n.stopPropagation(), l();
                var i = $(n.target);
                let t = i[0].getBoundingClientRect();
                t.top + 300 > document.scrollingElement.clientHeight && $(document.scrollingElement).stop().animate({
                    scrollTop: document.scrollingElement.scrollTop + i[0].getBoundingClientRect().top - document.scrollingElement.clientHeight / 2
                });
                let c = $(".pinyin-chart-table");
                (t.left < 0 || t.left > c[0].clientWidth + c[0].scrollLeft) && c.stop().animate({
                    scrollLeft: c[0].scrollLeft + (t.left - c[0].clientWidth / 2)
                }, 300), i.addClass("active");
                var {
                    index: s
                } = i.data(), e = a[s[0]][s[1]];
                $(".pinyin_var").text(e.text[0]), $(".pinyin_join").text(e.join), $(".tones").html(e.text.map((U, r) => e.audio[r] ? `
            <button class="yy-button yy-button--text btn-play-audio" data-url="${e.audio[r]}">${U}
              <img class="play-button" src="https://yoyochinese.com/public/images/tools/icon_audio_play.webp" width='16px'> <audio src='${e.audio[r]}' preload="auto"https://yoyochinese.com/>
            </button>
            ` : "")), e.videoUrl ? (g++, $(".pinyin-video-container").show(), $(".pinyin-video-container iframe").attr("src", e.videoUrl), g > 10 && $(".pinyin-video-container .mack").css({
                    display: "flex"
                })) : $(".pinyin-video-container").hide();
                let u = $(".popover");
                u.show();
                var m = i.offset(),
                    y = y = m.left + n.target.offsetWidth;
                document.body.clientWidth < y + u[0].clientWidth && (y = m.left - u[0].clientWidth), y < 0 || y > document.body.clientWidth ? (u.css({
                    left: 12
                }), d(m.top + 24)) : (u.css({
                    left: y
                }), d(m.top)), d()
            }), $(".popover .close-pinyin-pop").click(function() {
                l()
            })
        }

        function d(o) {
            $(".popover").attr("top", o), $(".popover").css({
                top: o - document.scrollingElement.scrollTop
            })
        }

        function l() {
            $(".pinyin-video-container iframe").attr("src", ""), $("td.active").removeClass("active"), $(".popover").hide()
        }
        v();

        function j() {
            window.addEventListener("scroll", () => {
                d($(".popover").attr("top"))
            }), $(".search-results").on("click", ".results", function(o) {
                o.stopPropagation(), $(`.td-pinyin__${$(o.target).text()}`).trigger("click"), $(".search-results").hide()
            }), $("#searchInput").on("input click", function(o) {
                o.stopPropagation(), x()
            }), $("#searchInput").click(o => o.stopPropagation()), $("#btn_Proit").click(function(o) {
                o.stopPropagation(), $(".search-results li:first-child").trigger("click")
            }), $(document).on("click", ".btn-play-audio", function(o) {
                o.stopPropagation(), $(o.currentTarget).find("audio")[0].play()
            }), document.addEventListener("click", function(o) {
                $(o.target).closest(".popover").length || ($(".search-results").hide(), l())
            })
        }
        j()
    });
})();
  </script>
  <script>
      (() => {
    var u = [{
        lessonId: "5f9c5385c32d410f1447c146",
        title: "What Is Pinyin",
        thumbnail: "what-is-pinyin.jpg",
        intro: "Have you ever wondered how people type Chinese characters? The answer lies in the wonderfully useful phonetic system used to spell out Chinese words called 'pinyin'. In this lesson, get an introduction to pinyin and how you'll use it to start learning Chinese.",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAxNA==?fallback=true",
        duration: "4:04",
        counts: {
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c147",
        duration: "3:46",
        title: "An Introduction to the Tones",
        thumbnail: "introduction-to-the-tones.jpg",
        intro: "Chinese is a 'tonal language' - but what does that mean? In this lesson, listen as Yangyang explains how tones work in Chinese and why they're so important to mastering the language.",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAyNQ==?fallback=true",
        counts: {
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c148",
        duration: "6:25",
        title: "The 1st and 2nd Tones",
        thumbnail: "the-1st-and-2nd-tones.jpg",
        intro: "All of the tones are important, but it's a good idea to really focus on each one when you're getting started. In this lesson, learn all about the first and second tones, how to pronounce them, and a few tricks you can use to make sure your understanding and pronunciation of the first two tones is spot on.",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAzNA==?fallback=true",
        counts: {
            Pinyin: 8,
            Audio: "2:08",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c149",
        duration: "3:54",
        title: "The 3rd and 4th Tones",
        thumbnail: "the-3rd-and-4th-tones.jpg",
        intro: "You're halfway there with the tones now and it's time to tackle the third and fourth tones. In this lesson, let Yangyang break down these essential sounds for you and how to produce them yourself using a few essential tips.",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAzNQ==?fallback=true",
        counts: {
            Pinyin: 8,
            Audio: "3:46",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c14d",
        duration: "3:28",
        title: "Tone Changes and the Neutral Tone",
        thumbnail: "tone-changes-and-the-neutral-tone.jpg",
        intro: "Each word has a tone, but did you know that tones also change sometimes depending on context? It sounds tricky, but don't despair - watch this video as Yangyang breaks down the tone change rules for you as well as introducing the 5th 'neutral' tone.",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAzNg==?fallback=true",
        counts: {
            Pinyin: 4,
            Audio: "3:01",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c155",
        duration: "5:34",
        title: "Pinyin Finals - Part 1",
        thumbnail: "pinyin-finals-part-1.jpg",
        intro: "Pinyin finals are an essential part of mastering Chinese pronunciation. In this lesson, get started practicing finals by focusing on the first group of finals starting with the letter 'a'.",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAxNQ==?fallback=true",
        counts: {
            Pinyin: 16,
            Audio: "3:24",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c156",
        duration: "3:55",
        title: "Pinyin Finals - Part 2",
        thumbnail: "pinyin-finals-part-2.jpg",
        intro: "In this lesson, continue learning all of the possible pinyin finals group by group - this time jumping into the finals starting with the letter 'e'.",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAxNg==?fallback=true",
        counts: {
            Pinyin: 21,
            Audio: "3:35",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c157",
        duration: "6:56",
        title: "Pinyin Finals - Part 3",
        thumbnail: "pinyin-finals-part-3.jpg",
        intro: "In the next lesson of the pinyin finals series, learn all about the first section of the finals that start with the letter 'i'. This group can be a little tricky so make sure to pay attention and practice!",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAxNw==?fallback=true",
        counts: {
            Pinyin: 23,
            Audio: "2:52",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c158",
        duration: "5:12",
        title: "Pinyin Finals - Part 4",
        thumbnail: "pinyin-finals-part-4.jpg",
        intro: "In this lesson, finish up the group of finals you started in the last lesson - the finals that start with the letter 'i'. Make sure you watch all the way to the end to learn an essential spelling rule associated with this group.",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAxOA==?fallback=true",
        counts: {
            Pinyin: 20,
            Audio: "3:44",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c15b",
        duration: "3:28",
        title: "Pinyin Finals - Part 5",
        thumbnail: "pinyin-finals-part-5.jpg",
        intro: "In this lesson, continue learning the pinyin finals and focus on the finals that start with the letter 'o'. There are only three so listen close and make sure you don't miss any!",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAxOQ==?fallback=true",
        counts: {
            Pinyin: 12,
            Audio: "2:43",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c15c",
        duration: "4:22",
        title: "Pinyin Finals - Part 6",
        thumbnail: "pinyin-finals-part-6.jpg",
        intro: "In this lesson, get started learning the first section of the 'u' finals in pinyin. 'U' might not be pronounced as you think, so make sure you pay close attention. You'll learn new pieces of vocab and practice your tones while you're at it!",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAyMA==?fallback=true",
        counts: {
            Pinyin: 26,
            Audio: "3:44",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c15d",
        duration: "5:14",
        title: "Pinyin Finals - Part 7",
        thumbnail: "pinyin-finals-part-7.jpg",
        intro: "In part seven of the pinyin final series, continue learning the rest of the finals that start with the letter 'u'. You'll also learn an important tip about a few cases where the 'u' doesn't appear, but is pronounced anyway.",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAyMQ==?fallback=true",
        counts: {
            Pinyin: 16,
            Audio: "3:02",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c15e",
        duration: "6:05",
        title: "The Pinyin Letter \xDC",
        thumbnail: "the-pinyin-letter-u.jpg",
        intro: "Many sounds in Chinese already exist in English, but some of the new ones can be tricky for English speakers. In this lesson, take a short break from finals and focus on the special pinyin final \xFC and how to pronounce it.",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAyMg==?fallback=true",
        counts: {
            Audio: "1:19",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c15f",
        duration: "5:03",
        title: "Pinyin Finals - Part 8",
        thumbnail: "pinyin-finals-part-8.jpg",
        intro: "In this lesson, get ready to finish off your work on pinyin finals by focusing on all the finals that start with the letter '\xFC'. The special spelling rules from the previous lesson apply here, so make sure to pay special attention!",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAyMw==?fallback=true",
        counts: {
            Pinyin: 20,
            Audio: "3:53",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c162",
        duration: "4:32",
        title: "Pinyin Initials - Part 1",
        thumbnail: "pinyin-initials-part-1.jpg",
        intro: "You've learned the Chinese finals, and now it's time to start learning 'initials'. Fortunately, most Chinese initial sounds are already found in English! So you can focus on the ones that matter. In this video, get started with 'j', 'q', and 'x'.",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAyNA==?fallback=true",
        counts: {
            Pinyin: 17,
            Audio: "5:28",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c163",
        duration: "7:49",
        title: "Pinyin Initials - Part 2",
        thumbnail: "pinyin-initials-part-2.jpg",
        intro: "In this lesson, continue your study of Chinese initials with four new sounds: 'zh', 'ch', 'sh', and 'r'. Remember, these sounds are not found in English so make sure to pay close attention to the explanations and practice yourself!",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAyNg==?fallback=true",
        counts: {
            Pinyin: 20,
            Audio: "6:37",
            Quiz: 10
        }
    }, {
        lessonId: "5f9c5385c32d410f1447c164",
        duration: "6:00",
        title: "Pinyin Initials - Part 3",
        thumbnail: "pinyin-initials-part-3.jpg",
        intro: "In your final lesson on Chinese initials, learn the 'z', 'c', and 's' sounds to close out your mastery of Chinese pronunciation! Once again, remember to pay close attention how to form these sounds with your mouth as they may not come as easily as other Chinese sounds.",
        url: "https://videos.cdn.spotlightr.com/watch/MTQxMTAyNw==?fallback=true",
        counts: {
            Pinyin: 14,
            Audio: "6:28",
            Quiz: 10
        }
    }];
    window.addEventListener("load", function() {
        function h(e, i) {
            var o = [];
            e.forEach(({
                title: t,
                thumbnail: a,
                duration: n
            }, s) => {
                o.push(`<li data-index="${s}"> 
              <img src="https://cdn.yoyochinese.com/images/webpage/front/thumbnails/${a}">
              <div>
                <div class="title">${t}</div>
                <div class="duration">${n}</div>
              </div>
            </li>`)
            }), i.find(".video-list-index").html(o.join("")), i.find(".video-list-index").on("click", "li", function(t) {
                i.find("li.active").removeClass("active"), $(t.currentTarget).addClass("active"), r(e[$(t.currentTarget).data().index], i), $(document.scrollingElement).animate({
                    scrollTop: document.scrollingElement.scrollTop + i.find(".main-iframe")[0].getBoundingClientRect().top - 120
                })
            })
        }

        function r({
            title: e,
            intro: i,
            url: o,
            counts: t,
            lessonId: a
        }, n) {
            let s = n.find(".main-iframe");
            s.attr(s.hasClass("lazyload") ? "data-src" : "src", o), n.find(".video-title").html(e), n.find(".video-info").html(i);
            var c = t ? Object.keys(t).map(l => `<li class='${l}'><span>${l}</span>${t[l]}</li>`) : [];
            n.find(".count-list").html(c.join("")), t && (t.Pinyin || t.Flashcards || t.Audio || t.Dialogue) ? (n.find(".media-count").show(), n.find(".practice-link").attr("href", "https://yoyochinese.com/auth?returnType=practice&returnId=" + a).show(), n.find(".quiz-link").addClass("yy-button yy-button--primary yy-button--text").html("Skip to Quiz")) : t && t.Quiz ? (n.find(".media-count").show(), n.find(".practice-link").hide(), n.find(".quiz-link").removeClass("yy-button yy-button--primary yy-button--text").html('<button class="yy-button yy-button--primary yy-button--small">Start Quiz</button>')) : n.find(".media-count").hide(), t && t.Quiz ? n.find(".quiz-link").attr("href", "https://yoyochinese.com/auth?returnType=quiz&returnId=" + a).show() : n.find(".quiz-link").hide()
        }

        function d() {
            var e = $(".tabs-nav.active"),
                i = $(".tabs-bar");
            i.width(e[0].offsetWidth), i.css("left", e[0].offsetLeft + "px")
        }
        h(u, $(".pinyin.video-player-list")), r(u[0], $(".pinyin.video-player-list")), d()
    });
})();
  </script>
</body>

</html>