<h2>Edit Snippet</h2>
<form method="post" class="mt-3">
    <div class="mb-3">
        <label class="form-label">Name</label>
        <input type="text" name="name" class="form-control" value="<?php echo HTML::chars($snippet->name); ?>" required>
    </div>
    <div class="mb-3">
        <label class="form-label">Description</label>
        <textarea name="description" class="form-control"><?php echo HTML::chars($snippet->description); ?></textarea>
    </div>
    <div class="mb-3">
        <label class="form-label">Content</label>
        <textarea name="content" class="form-control" rows="10" required><?php echo HTML::chars($snippet->content); ?></textarea>
    </div>
    <div class="mb-3">
        <label class="form-label">Type</label>
        <select name="type" class="form-select" required>
            <option value="php" <?php echo $snippet->type === 'php' ? 'selected' : ''; ?>>PHP</option>
            <option value="sql" <?php echo $snippet->type === 'sql' ? 'selected' : ''; ?>>SQL</option>
            <option value="html" <?php echo $snippet->type === 'html' ? 'selected' : ''; ?>>HTML (Forms)</option>
            <option value="css" <?php echo $snippet->type === 'css' ? 'selected' : ''; ?>>CSS</option>
            <option value="js" <?php echo $snippet->type === 'js' ? 'selected' : ''; ?>>JavaScript</option>
            <option value="other" <?php echo $snippet->type === 'other' ? 'selected' : ''; ?>>Other</option>
        </select>
    </div>
    <button type="submit" class="btn btn-primary">Save</button>
    <a href="/metrics/code" class="btn btn-secondary">Cancel</a>
</form>