<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculadora de Importação</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.0.3/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 p-6">
    <div class="max-w-lg mx-auto bg-white p-6 rounded-lg shadow-md" x-data="calcApp()">
        <h1 class="text-2xl font-bold mb-4">Calculadora de Importação</h1>

        <form @submit.prevent="calculate">
            <!-- Input Fields -->
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">Valor FOB:</label>
                <input type="number" step="0.01" class="w-full p-2 border border-gray-300 rounded" x-model="fob">
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">Frete Internacional:</label>
                <input type="number" step="0.01" class="w-full p-2 border border-gray-300 rounded" x-model="frete">
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">Seguro:</label>
                <input type="number" step="0.01" class="w-full p-2 border border-gray-300 rounded" x-model="seguro">
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">Imposto de Importação (II %):</label>
                <input type="number" step="0.01" class="w-full p-2 border border-gray-300 rounded" x-model="ii">
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">IPI (%):</label>
                <input type="number" step="0.01" class="w-full p-2 border border-gray-300 rounded" x-model="ipi">
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">PIS-Importação (%):</label>
                <input type="number" step="0.01" class="w-full p-2 border border-gray-300 rounded" x-model="pis_import">
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">COFINS-Importação (%):</label>
                <input type="number" step="0.01" class="w-full p-2 border border-gray-300 rounded" x-model="cofins_import">
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">ICMS (%):</label>
                <input type="number" step="0.01" class="w-full p-2 border border-gray-300 rounded" x-model="icms">
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">Margem de Lucro (%):</label>
                <input type="number" step="0.01" class="w-full p-2 border border-gray-300 rounded" x-model="margem">
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">MVA (%):</label>
                <input type="number" step="0.01" class="w-full p-2 border border-gray-300 rounded" x-model="mva">
            </div>

            <!-- Submit Button -->
            <div>
                <button type="submit" class="w-full p-2 bg-blue-600 text-white rounded">Calcular</button>
            </div>
        </form>

        <!-- Display Results -->
        <div class="mt-6" x-show="result">
            <h2 class="text-xl font-bold">Resultados:</h2>
            <p>CIF: <span x-text="result.cif"></span></p>
            <p>II: <span x-text="result.ii_value"></span></p>
            <p>IPI: <span x-text="result.ipi_value"></span></p>
            <p>PIS-Importação: <span x-text="result.pis_import_value"></span></p>
            <p>COFINS-Importação: <span x-text="result.cofins_import_value"></span></p>
            <p>ICMS: <span x-text="result.icms_value"></span></p>
            <p>CTM: <span x-text="result.ctm"></span></p>
            <p>PVB: <span x-text="result.pvb"></span></p>
            <p>Base de Cálculo ST: <span x-text="result.base_calculo_st"></span></p>
            <p>ICMS Final: <span x-text="result.icms_final"></span></p>
            <p>ICMS-ST: <span x-text="result.icms_st"></span></p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/alpinejs@2.8.2" defer></script>
    <script>
        function calcApp() {
            return {
                fob: 100000,
                frete: 6000,
                seguro: 100,
                ii: 10,
                ipi: 15,
                pis_import: 2.1,
                cofins_import: 9.65,
                icms: 18,
                margem: 30,
                mva: 30,
                result: null,

                async calculate() {
                    const response = await fetch('/metrics/ia/cost/calculate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            fob: this.fob,
                            frete: this.frete,
                            seguro: this.seguro,
                            ii: this.ii,
                            ipi: this.ipi,
                            pis_import: this.pis_import,
                            cofins_import: this.cofins_import,
                            icms: this.icms,
                            margem: this.margem,
                            mva: this.mva,
                        }),
                    });

                    this.result = await response.json();
                },
            };
        }
    </script>
</body>
</html>