<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DRE - Demonstração do Resultado do Exercício</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.0.3/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 p-6">
    <div class="max-w-lg mx-auto bg-white p-6 rounded-lg shadow-md" x-data="dreApp()">
        <h1 class="text-2xl font-bold mb-4">DRE - Demonstração do Resultado do Exercício</h1>

        <form @submit.prevent="addDreRecord">
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">Descrição:</label>
                <select class="w-full p-2 border border-gray-300 rounded" x-model="description">
                    <option value="Receita Bruta">Receita Bruta</option>
                    <option value="Deduções da Receita Bruta">Deduções da Receita Bruta</option>
                    <option value="Custo das Mercadorias Vendidas (CMV)">Custo das Mercadorias Vendidas (CMV)</option>
                    <option value="Despesas Operacionais">Despesas Operacionais</option>
                    <option value="Despesas Financeiras">Despesas Financeiras</option>
                    <option value="Receitas Financeiras">Receitas Financeiras</option>
                    <option value="Imposto de Renda e Contribuição Social">Imposto de Renda e Contribuição Social</option>
                </select>
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">Valor:</label>
                <input type="number" step="0.01" class="w-full p-2 border border-gray-300 rounded" x-model="value">
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">Período:</label>
                <input type="date" class="w-full p-2 border border-gray-300 rounded" x-model="period">
            </div>
            <div>
                <button type="submit" class="w-full p-2 bg-blue-600 text-white rounded">Adicionar Registro</button>
            </div>
        </form>

        <div class="mt-6">
            <button @click="fetchDre" class="w-full p-2 bg-green-600 text-white rounded">Visualizar DRE</button>
        </div>

        <div class="mt-6" x-show="dre.length > 0">
            <h2 class="text-xl font-bold">DRE - Período: <span x-text="period"></span></h2>
            <ul>
                <template x-for="(value, key) in dre" :key="key">
                    <li class="mb-2">
                        <strong x-text="key"></strong>: <span x-text="value.toFixed(2)"></span>
                    </li>
                </template>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/alpinejs@2.8.2" defer></script>
    <script>
        function dreApp() {
            return {
                description: 'Receita Bruta',
                value: '',
                period: '',
                dre: {},

                async addDreRecord() {
                    const response = await fetch('/metrics/ia/dre/add', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            description: this.description,
                            value: this.value,
                            period: this.period,
                        }),
                    });

                    const result = await response.json();

                    if (result.status === 'success') {
                        this.value = '';
                    }
                },

                async fetchDre() {
                    const response = await fetch(`/metrics/ia/dre/calculate?period=${this.period}`);
                    this.dre = await response.json();
                }
            };
        }
    </script>
</body>
</html>
