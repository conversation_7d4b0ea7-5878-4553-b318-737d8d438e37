<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoramento de KPIs</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.0.3/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 p-6">
    <div class="max-w-lg mx-auto bg-white p-6 rounded-lg shadow-md" x-data="kpiApp()">
        <h1 class="text-2xl font-bold mb-4">Monitoramento de KPIs</h1>

        <form @submit.prevent="addKpi">
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">Nome do KPI:</label>
                <input type="text" class="w-full p-2 border border-gray-300 rounded" x-model="kpi_name">
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">Valor:</label>
                <input type="number" step="0.01" class="w-full p-2 border border-gray-300 rounded" x-model="value">
            </div>
            <div class="mb-4">
                <label class="block mb-2 text-sm font-bold text-gray-700">Data:</label>
                <input type="date" class="w-full p-2 border border-gray-300 rounded" x-model="date_recorded">
            </div>
            <div>
                <button type="submit" class="w-full p-2 bg-blue-600 text-white rounded">Adicionar KPI</button>
            </div>
        </form>

        <div class="mt-6" x-show="kpis.length > 0">
            <h2 class="text-xl font-bold">KPIs Registrados:</h2>
            <ul>
                <template x-for="kpi in kpis" :key="kpi.id">
                    <li class="mb-2">
                        <strong x-text="kpi.kpi_name"></strong>: <span x-text="kpi.value"></span> (Registrado em: <span x-text="kpi.date_recorded"></span>)
                    </li>
                </template>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/alpinejs@2.8.2" defer></script>
    <script>
        function kpiApp() {
            return {
                kpi_name: '',
                value: '',
                date_recorded: '',
                kpis: [],

                async fetchKpis() {
                    const response = await fetch('/metrics/ia/kpi');
                    this.kpis = await response.json();
                },

                async addKpi() {
                    const response = await fetch('/metrics/ia/kpi/add', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            kpi_name: this.kpi_name,
                            value: this.value,
                            date_recorded: this.date_recorded,
                        }),
                    });

                    const result = await response.json();

                    if (result.status === 'success') {
                        this.fetchKpis();
                        this.kpi_name = '';
                        this.value = '';
                        this.date_recorded = '';
                    }
                },

                async calculateKpi(kpi_name) {
                    const response = await fetch('/metrics/ia/kpi/calculate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ kpi_name }),
                    });

                    const result = await response.json();
                    alert(`Resultado para ${kpi_name}: ${result.result}`);
                },

                async init() {
                    this.fetchKpis();
                }
            };
        }
    </script>
</body>
</html>
