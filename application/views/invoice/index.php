<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Details - <?php echo $invoice['invoice']; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-primary-600 shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-white">Sistema de Invoices</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-white">Gerado em: <?php echo date('d/m/Y H:i'); ?></span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8 max-w-full"> <!-- Changed from default container width -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-primary-500 px-6 py-4">
                <h3 class="text-2xl font-semibold text-white">Detalhes do Embarque - <?php echo $invoice['invoice']; ?></h3>
            </div>
            <div class="p-6">
                <!-- Shipment Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-medium text-primary-700 mb-4">Informações Gerais</h4>
                        <div class="bg-white rounded-lg border border-gray-200">
                            <table class="min-w-full divide-y divide-gray-200">
                                <?php
                                $generalInfo = [
                                    'Invoice Number' => $invoice['invoice'],
                                    'BL Number' => $invoice['bl'],
                                    'RG' => $invoice['rg'],
                                    'Shipper' => $invoice['shipper'],
                                    'Port Origin' => $invoice['portOrigin'],
                                    'Port Destination' => $invoice['portDestination']
                                ];
                                foreach ($generalInfo as $label => $value):
                                ?>
                                <tr>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700"><?php echo $label; ?></th>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo $value; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </table>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-medium text-primary-700 mb-4">Datas e Status</h4>
                        <div class="bg-white rounded-lg border border-gray-200">
                            <table class="min-w-full divide-y divide-gray-200">
                                <tr>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Date</th>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo date('d/m/Y', strtotime($invoice['date'])); ?></td>
                                </tr>
                                <tr>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Expected Arrival</th>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo date('d/m/Y', strtotime($invoice['arrival'])); ?></td>
                                </tr>
                                <tr>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Express Tracking</th>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo $invoice['expressTracking']; ?></td>
                                </tr>
                                <tr>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Observations</th>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo nl2br($invoice['obs']); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Financial Information -->
                <div class="mt-8">
                    <h4 class="text-lg font-medium text-primary-700 mb-4">Informações Financeiras</h4>
                    <div class="bg-white rounded-lg border border-primary-200 overflow-hidden">
                        <table class="min-w-full divide-y divide-primary-200">
                            <tr>
                                <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Amount</th>
                                <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Exchange Rate</th>
                                <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Insurance</th>
                                <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Freight</th>
                                <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Numerário</th>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm text-gray-900">USD <?php echo number_format($invoice['ammount'], 2); ?></td>
                                <td class="px-4 py-3 text-sm text-gray-900">R$ <?php echo number_format($invoice['usd_real'], 4); ?></td>
                                <td class="px-4 py-3 text-sm text-gray-900">USD <?php echo number_format($invoice['insurance'], 2); ?></td>
                                <td class="px-4 py-3 text-sm text-gray-900">USD <?php echo number_format($invoice['freight'], 2); ?></td>
                                <td class="px-4 py-3 text-sm text-gray-900">R$ <?php echo number_format($invoice['numerario'], 2); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Shipping Information -->
                <div class="mt-8">
                    <h4 class="text-lg font-medium text-primary-700 mb-4">Informações de Envio</h4>
                    <div class="bg-white rounded-lg border border-primary-200 overflow-hidden">
                        <table class="min-w-full divide-y divide-primary-200">
                            <tr>
                                <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Cartons</th>
                                <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Gross Weight</th>
                                <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Net Weight</th>
                                <th class="px-4 py-3 bg-gray-50 text-left text-sm font-medium text-gray-700">Volume</th>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm text-gray-900"><?php echo $invoice['cartons']; ?></td>
                                <td class="px-4 py-3 text-sm text-gray-900"><?php echo number_format($invoice['gross_weight'], 2); ?> kg</td>
                                <td class="px-4 py-3 text-sm text-gray-900"><?php echo number_format($invoice['net_weight'], 2); ?> kg</td>
                                <td class="px-4 py-3 text-sm text-gray-900"><?php echo number_format($invoice['volume'], 2); ?> m³</td>
                            </tr>
                        </table>
                      
                    </div>
                </div>

                <!-- NCM Summary -->
                <div class="mt-8">
                    <h4 class="text-lg font-medium text-primary-700 mb-4">Resumo por NCM</h4>
                    <div class="bg-white rounded-lg border border-primary-200 overflow-x-auto">
                        <table class="min-w-full divide-y divide-primary-200">
                            <thead>
                                <tr class="bg-primary-50">
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">#</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">NCM</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Quantidade</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">FOB Total</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Peso Total</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Volume Total</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-primary-200">
                                <?php 
                                $ncmSummary = [];
                                $totalNCMQuantity = 0;
                                $totalNCMFob = 0;
                                $totalNCMWeight = 0;
                                $totalNCMVolume = 0;
                                
                                // Agrupar por NCM
                                foreach ($invoice['products'] as $product) {
                                    $ncm = $product['NCM'];
                                    if (!isset($ncmSummary[$ncm])) {
                                        $ncmSummary[$ncm] = [
                                            'quantity' => 0,
                                            'fob_total' => 0,
                                            'weight_total' => 0,
                                            'volume_total' => 0
                                        ];
                                    }
                                    
                                    $ncmSummary[$ncm]['quantity'] += $product['quant'];
                                    $ncmSummary[$ncm]['fob_total'] += $product['ammount'];
                                    $ncmSummary[$ncm]['weight_total'] += ($product['PesoSet'] * $product['quant']);
                                    $ncmSummary[$ncm]['volume_total'] += ($product['Volume'] * $product['quant']);
                                    
                                    // Acumular totais gerais
                                    $totalNCMQuantity += $product['quant'];
                                    $totalNCMFob += $product['ammount'];
                                    $totalNCMWeight += ($product['PesoSet'] * $product['quant']);
                                    $totalNCMVolume += ($product['Volume'] * $product['quant']);
                                }
                                
                                // Ordenar por NCM
                                ksort($ncmSummary);
                                
                                $counter = 1; // Initialize counter here
                                foreach ($ncmSummary as $ncm => $totals): 
                                ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 text-sm text-gray-500"><?php echo $counter++; ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo $ncm; ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['quantity'], 0); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">USD <?php echo number_format($totals['fob_total'], 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['weight_total'], 2); ?> kg</td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['volume_total'], 4); ?> m³</td>
                                </tr>
                                <?php endforeach; ?>
                                <!-- Linha de totais -->
                                <tr class="bg-gray-50 font-semibold">
                                    <td class="px-4 py-3 text-sm text-gray-900">Totais</td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totalNCMQuantity, 0); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">USD <?php echo number_format($totalNCMFob, 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totalNCMWeight, 2); ?> kg</td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totalNCMVolume, 4); ?> m³</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- Products List -->
                <div class="mt-8">
                    <h4 class="text-lg font-medium text-primary-700 mb-4">Produtos da Invoice</h4>
                    <div class="bg-white rounded-lg border border-primary-200 overflow-x-auto">
                        <table class="min-w-full divide-y divide-primary-200">
                            <thead>
                                <tr class="bg-primary-50">
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">#</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Modelo</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Produto</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Marca</th>                          
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Quantidade</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">FOB Unit</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Total</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Peso Unit.</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Peso</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Volume Unit.</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Volume</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <?php 
                                $totalQuantity = 0;
                                $totalFOB = 0;
                                $totalAmount = 0;
                                $totalWeight = 0;
                                $totalVolume = 0;
                                $counter = 1;
                                
                                foreach ($invoice['products'] as $product): 
                                    $totalQuantity += $product['quant'];
                                    $totalFOB += $product['fob'];
                                    $totalAmount += $product['ammount'];
                                    $totalWeight += ($product['PesoSet'] * $product['quant']);
                                    $totalVolume += ($product['Volume'] * $product['quant']);
                                ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 text-sm text-gray-500"><?php echo $counter++; ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo $product['modelo']; ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo $product['xProduct']; ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo $product['xBrand']; ?></td>                                 
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($product['quant'], 0); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">USD <?php echo number_format($product['fob'], 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">USD <?php echo number_format($product['ammount'], 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($product['PesoSet'], 2); ?> kg</td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($product['PesoSet'] * $product['quant'], 2); ?> kg</td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($product['Volume'], 4); ?> m³</td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($product['Volume'] * $product['quant'], 4); ?> m³</td>
                                </tr>
                                <?php endforeach; ?>
                                <tr class="bg-gray-50 font-semibold">
                                    <td colspan="4" class="px-4 py-3 text-sm text-gray-900">Totais</td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totalQuantity, 0); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">-</td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">USD <?php echo number_format($totalAmount, 2); ?></td>
                                    <td colspan="2" class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totalWeight, 2); ?> kg</td>
                                    <td colspan="2" class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totalVolume, 4); ?> m³</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                 <!-- Products Calc-->
                 <div class="mt-8">
                    <h4 class="text-lg font-medium text-primary-700 mb-4">Cálculo dos Produtos da DI</h4>
                    <div class="bg-white rounded-lg border border-primary-200 overflow-x-auto">
                        <table class="min-w-full divide-y divide-primary-200">
                            <thead>
                                <tr class="bg-primary-50">
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">#</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Modelo</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Produto</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Marca</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">NCM</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">FOB (R$)</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Frete (R$)</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Seguro (R$)</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">IPI (R$)</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">COFINS (R$)</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">PIS (R$)</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">II (R$)</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">ICMS</th>

                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                            <?php 
                                $totalQuantity = 0;
                                $totalFOB = 0;
                                $totalAmount = 0;
                                $totalFRETE = 0;
                                $totalSEGURO = 0;
                                $totalPIS = 0;
                                $totalCOFINS = 0;
                                $totalIPI = 0;
                                $totalII = 0;
                                $totalICMS = 0;

                                $counter = 1;
                                
                                foreach ($invoice['products'] as $product): 
                                    $freteR = ($product['peso']/$totalNCMWeight)*$product['freteR'];
                                    $seguroR =($product['ammount']/$totalNCMFob)*$product['seguroR'];
                                    $totalQuantity += $product['quant'];
                                    $totalFOB += $product['fobR'];
                                    $totalFRETE += $freteR;
                                    $totalSEGURO +=  $seguroR;
                                    $totalPIS += $product['pisR'];
                                    $totalCOFINS += $product['cofinsR'];
                                    $totalIPI += $product['ipiR'];
                                    $totalII += $product['iiR'];
                                    $totalICMS += $product['icmsR'];

                                    $totalAmount += $product['ammount'];
                                    $totalWeight += ($product['PesoSet'] * $product['quant']);
                                    $totalVolume += ($product['Volume'] * $product['quant']);
                                ?>   
                                
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 text-sm text-gray-500"><?php echo $counter++; ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo $product['modelo']; ?></td>
                    
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo $product['xBrand']; ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo $product['NCM']; ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($product['quant'], 0); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($product['fobR'], 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format( $freteR, 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format( $seguroR, 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($product['ipiR'], 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($product['cofinsR'], 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($product['pisR'], 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($product['iiR'], 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($product['icmsR'], 2); ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <tr class="bg-gray-50 font-semibold">
                                    <td colspan="4" class="px-4 py-3 text-sm text-gray-900">Totais</td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totalQuantity, 0); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($totalFOB, 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($totalFRETE, 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($totalSEGURO, 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($totalIPI, 2); ?></td>                                    
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($totalCOFINS, 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($totalPIS, 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($totalII, 2); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900 text-right">R$ <?php echo number_format($totalICMS, 2); ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                      <!-- Products Calc-->
                      <div class="mt-8">
                            <h4 class="text-lg font-medium text-primary-700 mb-4">Resumo do Cálculo dos Produtos da DI</h4>
                            <div class="bg-white rounded-lg border border-primary-200 overflow-x-auto">
                                <table class="min-w-full divide-y divide-primary-200">
                                    <thead>
                                        <tr class="bg-primary-50">
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">NCM</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">FOB (R$)</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Frete (R$)</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Seguro (R$)</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">IPI (R$)</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">COFINS (R$)</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">PIS (R$)</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">II (R$)</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Quantidade</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Peso (kg)</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Volume (m³)</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        <?php foreach ($invoice['calc']['TotaisPorNCM'] as $ncm => $totals): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-4 py-3 text-sm text-gray-900"><?php echo $ncm; ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['fobR'], 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['freteR'], 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['seguroR'], 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['ipiR'], 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['cofinsR'], 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['pisR'], 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['iiR'], 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['quantidade'], 0, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['peso'], 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format($totals['volume'], 4, ',', '.'); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                        
                                        <!-- Linha de totais -->
                                        <tr class="bg-primary-50 font-semibold">
                                            <td class="px-4 py-3 text-sm text-gray-900">Totais</td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format(array_sum(array_column($invoice['calc']['TotaisPorNCM'], 'fobR')), 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format(array_sum(array_column($invoice['calc']['TotaisPorNCM'], 'freteR')), 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format(array_sum(array_column($invoice['calc']['TotaisPorNCM'], 'seguroR')), 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format(array_sum(array_column($invoice['calc']['TotaisPorNCM'], 'ipiR')), 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format(array_sum(array_column($invoice['calc']['TotaisPorNCM'], 'cofinsR')), 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format(array_sum(array_column($invoice['calc']['TotaisPorNCM'], 'pisR')), 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format(array_sum(array_column($invoice['calc']['TotaisPorNCM'], 'iiR')), 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format(array_sum(array_column($invoice['calc']['TotaisPorNCM'], 'quantidade')), 0, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format(array_sum(array_column($invoice['calc']['TotaisPorNCM'], 'peso')), 2, ',', '.'); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 text-right"><?php echo number_format(array_sum(array_column($invoice['calc']['TotaisPorNCM'], 'volume')), 4, ',', '.'); ?></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="bg-primary-600 shadow-lg mt-8">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="text-sm text-white">
                    &copy; <?php echo date('Y'); ?> Sistema de Invoices
                </div>
                <div class="text-sm text-white">
                    Referência da Invoice: <?php echo $invoice['invoice']; ?>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
