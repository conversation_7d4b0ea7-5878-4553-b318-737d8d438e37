<!DOCTYPE html>
<html lang="pt-br">

<head>
  <title>Metas </title>

  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">


        <!-- awesome CSS -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">
        
        <!-- tailwindcss CSS -->
        <!--<link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet">-->
        
        <!-- tailwindcss CSS -->
        <script src="https://cdn.tailwindcss.com"></script>
         
        <!-- jquery 3.6 -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"
        integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="
        crossorigin="anonymous"></script>
        
        <!-- winbox bundle -->
        <script src="https://rawcdn.githack.com/nextapps-de/winbox/0.2.0/dist/winbox.bundle.js"></script>



       
        <!-- twitter-bootstrap//-->
        <!--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.0.1/css/bootstrap-grid.min.css" integrity="sha512-Aa+z1qgIG+Hv4H2W3EMl3btnnwTQRA47ZiSecYSkWavHUkBF2aPOIIvlvjLCsjapW1IfsGrEO3FU693ReouVTA==" crossorigin="anonymous" referrerpolicy="no-referrer" />-->
        
       
        

        <script src="https://cdnjs.cloudflare.com/ajax/libs/jeditable.js/2.0.19/jquery.jeditable.min.js" integrity="sha512-Jn+modod+CgT0sdSdiL5Zr74JeTDTR2DGzeSRG1oUVnZjZFnkdSPOFUiwAr/St/lIvYEyBaZ6F46vJE4fbHg6A==" crossorigin="anonymous"></script>
        
        <!--        /* Livequery   *///-->
        <!--<script src="https://office.vallery.com.br/jquery/livequery/jquery.livequery.js"></script>-->
        
        
        <!-- htmx -->
        <!--<script src="https://unpkg.com/htmx.org@1.8.6"></script>-->
        <script src="https://unpkg.com/htmx.org@1.6.0"></script>
        <script src="https://unpkg.com/htmx.org/dist/ext/client-side-templates.js"></script>
        <script src="https://unpkg.com/hyperscript.org@0.0.9"></script>
        <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.x.x/dist/alpine.min.js" defer></script>
        
        
        <!--<link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/prelude.css">-->
        <!--<link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/rainbow.css">-->
        <!--<link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/progress.css">-->
        
         <!-- mustache -->
        <script src="https://cdn.jsdelivr.net/npm/mustache@4.2.0/mustache.min.js"></script>
        
        <!-- Tabesorter  *///-->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.2/js/jquery.tablesorter.combined.min.js"></script>
        <!--Datatables-->
        <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/dt/jszip-2.5.0/dt-1.12.1/af-2.4.0/b-2.2.3/b-colvis-2.2.3/b-html5-2.2.3/b-print-2.2.3/cr-1.5.6/date-1.1.2/fc-4.1.0/fh-3.2.4/kt-2.7.0/r-2.3.0/rg-1.2.0/rr-1.2.8/sc-2.0.7/sb-1.3.4/sp-2.0.2/sl-1.4.0/sr-1.1.1/datatables.min.css"/>
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js"></script>
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js"></script>
        <script type="text/javascript" src="https://cdn.datatables.net/v/dt/jszip-2.5.0/dt-1.12.1/af-2.4.0/b-2.2.3/b-colvis-2.2.3/b-html5-2.2.3/b-print-2.2.3/cr-1.5.6/date-1.1.2/fc-4.1.0/fh-3.2.4/kt-2.7.0/r-2.3.0/rg-1.2.0/rr-1.2.8/sc-2.0.7/sb-1.3.4/sp-2.0.2/sl-1.4.0/sr-1.1.1/datatables.min.js"></script>


</head>

<body>
    
<section  class="py-10 bg-gray-100">
   
  <div class="mx-auto  max-w-8lg  grid-cols-1 gap-1 p-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1">
      <div class="relative flex items-end overflow-hidden rounded-xl">
          <img src="{{{data.0.Maps}}}" alt="Meso Photo" width=200px>

        </div>
  <p class="text-2xl  text-capitalize " >{{{data.0.NomeMeso}}}</p>

  {{#data}}
      <article class=" rounded-xl bg-white p-2 shadow-lg ">
    
            <p class="text-xl  text-capitalize " >{{{cidade}}} 
            {{#Data}} 
                <span class="text-md text-green=500 font-medium"> - {{{qMes}}}
                        <span class=" text-xs text-gray-600 ">unidades vendidos no mês </span>
                </span>  <div class="mt-1 p-2">


          <section  class="py-10 bg-stone-100">
            <div class="mx-auto grid max-w-8xl ">
             
              <table  class="table min-w-full border-collapse block md:table">
          		<thead class="block md:table-header-group text-xs">
          			<tr class="border border-green-500 md:border-none block md:table-row absolute -top-full md:top-auto -left-full md:left-auto  md:relative ">
          			
          			   <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">#</th>
          			    <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Vendedor</th>
          				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Cliente</th>
          				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Cidade</th>
            			<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Estado</th>
          		        <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Jan</th>
        		        <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Fev</th>
              		    <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Mar</th>
                  <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Abr</th>
                  <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Mai</th>
                  <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Jun</th>              		    
         				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">2023</th>
         				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">2022</th>
         				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">sJan</th>
         				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">sFev </th>
         				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">sMar</th>
         				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">sAbr</th>
         				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">sMai</th>
         				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">sJun</th>         				
          				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">s2023</th>
          				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">s2022</th>
          				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Atrasados</th>
          				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell break-normal	">Última Compra Dias</th>
          			    <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Large Customers</th>
          			</tr>
          		</thead>
          		<tbody class="block md:table-row-group">
          		  
             {{#Clientes}}
             
             <tr class=" border border-grey-500 md:border-none block md:table-row text-xs">
               
          				 
          	   <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell ">
                    <span class="inline-block w-1/3 md:hidden font-bold">counter</span>{{{counter}}}</td>    					  
                <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell ">
                    <span class="inline-block w-1/3 md:hidden font-bold">Vendedor</span>{{{nick}}}</td>    
                
                <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell capitalize text-md"><a color="#000600" width="80%" height="80%" class="winbox-iframe cursor-pointer" rel="/K3/tip/vcustomer/{{{idcli}}}/" title="{{{nome}}}">{{{nome}}} </a></td>
                
                
                
                
                <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell	">
                <span class="inline-block w-1/3 md:hidden font-bold">Cidade</span>{{{NomeMunic}}} {{{bairro}}}</td>
                
                
                <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell">{{{estado}}}</td>
                
                
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-green-600 p-2 text-white ">{{{Jan}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-green-500 p-2 text-white ">{{{Fev}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-green-600 p-2 text-white ">{{{Mar}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-green-500 p-2 text-white ">{{{Abr}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-green-600 p-2 text-white ">{{{Mai}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-green-500 p-2 text-white ">{{{Jun}}}</td>

                
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-green-900 p-2 text-white ">{{{qAno}}}</td>
                
                  <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-orange-900 p-2 text-white">{{{qAnoPassado}}}</td>
                
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-blue-600 p-2 text-white 	">{{{sswJan}}}</td>
                 <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-blue-500 p-2 text-white 	">{{{sswFev}}}</td>
                 <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-blue-400 p-2 text-white 	">{{{sswMar}}}</td>	
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-blue-600 p-2 text-white 	">{{{sswAbr}}}</td>
                 <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-blue-500 p-2 text-white 	">{{{sswMai}}}</td>
                 <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-blue-400 p-2 text-white 	">{{{sswJun}}}</td>	
        
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-blue-900 p-2 text-white 	">{{{ssw}}}<a color="#008000" width="1500" height="600" class="winbox-iframe cursor-pointer text-xs rounded hover:rounded-lg border border-green-500" rel="/metrics/ssw/index/?cnpj={{{cnpj}}}" title="{{{nome}}}">ver</a>
                </td>			  
                
                  <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-orange-900 p-2 text-white">{{{sswPassado}}}</td>
                
                    
                
                
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell text-sm 	">{{{Atrasados}}}</td>
                
                <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell text-sm {{{rclass}}}	">{{{LastPurchase}}}</td>
                
                <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell">{{{hearts}}}</td>


          				
          			</tr>
            
            {{/Clientes}}
          		</tbody>
          		<tfoot>
          		    <td colspan="5" class="p-2 md:border md:border-grey-500 text-left block md:table-cell "></td> 
          		    <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell border border-green-500 text-green-700 font-bold">{{{TT.jan}}}</td> 
          		    <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell  border border-green-500  text-green-700 font-bold">{{{TT.fev}}}</td> 
          		    <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell  border border-green-500  text-green-700 font-bold">{{{TT.mar}}}</td>
          		     <td colspan="2" class="p-2 md:border md:border-grey-500 text-left block md:table-cell "></td> 
          		    <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell  border border-blue-500  text-blue-700 font-bold">{{{TT.sjan}}}</td> 
          		    <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell  border border-blue-500  text-blue-700 font-bold">{{{TT.sfev}}}</td> 
          		    <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell  border border-blue-500  text-blue-700 font-bold">{{{TT.smar}}}</td> 
          		</tfoot>
          	</table>
          </div>
          </section>
        </div>
            {{/Data}}
            </p>

    </article>
  {{/data}}
  
 
</div>

</section>

 
<script>

   $(document).ready(function(){

      // $( "#start" ).click();
       
        // $( "#start" ).livequery('click');
       
    $('.winbox-iframe').on('click', function(event) 
         {
          var address = $(this).attr('rel');
            var title = $(this).attr('title') + ' - ' + address;
            var color   = $(this).attr('color');
            var width   = $(this).attr('width');
            var height  = $(this).attr('height');
            
            var winbox = new WinBox(
            {
                title: title,
                root: document.body,
                //     // html: "<h1>Lorem Ipsum</h1>"
                // html: '<iframe src="..." allow="..." sandbox="allow-scripts" referrerpolicy="..."></iframe>',
                // html: '<iframe  src="https://web.whatsapp.com/"  sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>',
                url: address,
                background: color,
                border:2,
                // class: "iframe",
                x: "center",
                // y: "center",
                // width: width,
                // height: height,
                 top: "10%",
                // right: "10%",
                // bottom: "0%",
                // left: "10%",
                width: "90%",
                height: 960,
                // minheight: 55,
                // minwidth: 100,
                // maxheight: 1280,
                // maxwidth: 1600,
                autosize: true,
               
            });
          
        }); 
        
     $('.table').dataTable({
            
                // order: ['Supplier', 'Brand', 'NCM'],
                // // "scrollCollapse": true,
                // "scrollX": true,
                // order: [[1, 'asc']],
                // rowGroup: {
                //     dataSrc: 4
                // },
                
                // "sDom": '<"bottom"flp><"clear">',
                // "searching": true,
                "paging": false,
                // select: {
                //     style: 'multi'
                // },
                // "bFilter": false,
                // "colReorder": true
	        })
            
   } );
</script>

</body>
</html>




