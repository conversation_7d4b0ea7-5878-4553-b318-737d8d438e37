

<section  class="py-10 bg-gray-100">
   
  <div class="mx-auto grid max-w-7xl  grid-cols-1 gap-6 p-6 sm:grid-cols-2 md:grid-cols-6 lg:grid-cols-1">
      <div class="relative flex items-end overflow-hidden rounded-xl">
          <img src="{{{data.0.Maps}}}" alt="Meso Photo" width=200px>
            <span hx-trigger="every 180s" hx-get="/metrics/mesos/mesos">
       
    </span>
        </div>
  <p class="text-2xl  text-capitalize " >{{{data.0.NomeMeso}}}</p>
   <span hx-trigger="load" hx-get="/metrics/mesos/distance/1/{{{data.0.Meso}}}/" class="font-black text-black"><i class="fas fa-spinner fa-spin text-gray"></i></span>
  {{#data}}
  
      <article class=" rounded-xl bg-white p-2 shadow-lg hover:shadow-xl hover:transform hover:scale-105 duration-300 ">
    
      <span  >
      
            
            <p class="text-xl  text-capitalize " >{{{cidade}}} 
            {{#Data}} 
                <span class="text-md text-green=500 font-medium" id="mak.inv|nome|id|1678894" > - {{{qMes}}}
                        <span class=" text-xs text-gray-600 ">unidades vendidos no mês </span>
                </span>  <div class="mt-1 p-2">


          <section  class="py-10 bg-stone-100">
            <div class="mx-auto grid max-w-7xl ">
             
              <table  class="table min-w-full border-collapse block md:table">
          		<thead class="block md:table-header-group">
          			<tr class="border border-green-500 md:border-none block md:table-row absolute -top-full md:top-auto -left-full md:left-auto  md:relative ">
          			
          			<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Vendedor</th>
          					<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Cliente</th>
          				
          				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Cidade</th>
            				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Estado</th>
        			
          		    <th class="bg-green-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Mar</th>
        		    <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Fev</th>
              		    <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Jan</th>
         				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">2023</th>
         				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">2022</th>
          				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">s2023</th>
          				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Atrasados</th>
          		
          				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell break-normal	">Última Compra Dias</th>
          			</tr>
          		</thead>
          		<tbody class="block md:table-row-group">
          		  
             {{#Clientes}}
             
             <tr class=" border border-grey-500 md:border-none block md:table-row text-md">
               
          				 
          						  
          					<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell ">
          				  <span class="inline-block w-1/3 md:hidden font-bold">Vendedor</span>{{{nick}}}</td>    
          				<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell capitalize text-md">
          				  
          				    <span class="inline-block w-1/3 md:hidden font-bold">Nome</span>
          				    <span
          				          class="cursor-pointer"
          				          hx-push-url="true" 
          				          hx-target="#Grid" 
                            hx-get="https://dev.office.internut.com.br/metrics/mesos/customer/1/{{{idcli}}}/"  >{{{nome}}}</span>
                      
                  </td>
          			
          				
          				  
          				  
          				<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell	">
          				  <span class="inline-block w-1/3 md:hidden font-bold">Cidade</span>{{{NomeMunic}}}</td>
          				  
          			
          				<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell">
          				  <span class="inline-block w-1/3 md:hidden font-bold">Estado</span>{{{estado}}}</td>
          				  
          				  
          				<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-green-600 p-2 text-white ">
          				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Mar</span>{{{qMes}}}</td>
              			
          			<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell ">
          				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Fev</span>{{{Fev}}}</td>
          				  
          			<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell ">
          				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Jan</span>{{{Jan}}}</td>
          				  
          			<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell 	">
          				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Ano</span>{{{qAno}}}</td>
          				  
          					<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell ">
          				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Ano Passado</span>{{{qAnoPassado}}}</td>
          	
          			<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-blue-600 p-2 text-white 	">
          				  <span class="inline-block w-1/3 md:hidden font-bold text-right">SSW</span>{{{ssw}}}</td>			  
          				  
          					  
      				  
          
          				<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell text-sm 	">
          				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Atrasados</span>{{{Atrasados}}}</td>
          
          				<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell text-sm {{{rclass}}}	">
          				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Última Compra</span>{{{LastPurchase}}}</td>
          		
          				
          			</tr>
            
            {{/Clientes}}
          			
          			
          		</tbody>
          	</table>
          </div>
          </section>
        </div>
            {{/Data}}
            </p>
          
    
            
      
        
      </span>
    </article>
  {{/data}}
  
 
</div>

</section>

