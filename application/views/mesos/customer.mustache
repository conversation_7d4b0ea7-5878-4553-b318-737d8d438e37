
<style>
  body {
    background: #e2e8f0;
  }
    *:hover {
      transition: all 150ms ease-in;
    }
  </style>
  
  <div class="antialiased max-w-7xl mx-auto my-12 bg-gray-300 px-8">
    <div class="relative block md:flex items-center">
      <div class="w-full  relative z-1 bg-gray-100 rounded shadow-lg overflow-hidden">
        <div class="text-lg font-medium text-blue-500  p-8 text-center border-b border-gray-200 tracking-wide">  <span class="font-bold text-xl	"> {{{data.0.nome}}}</span></span> - estado de {{{data.0.estado}}}
         <span class="font-bold text-md	"> {{{data.0.ender}}}</span></span> - cidade de {{{data.0.cidade}}}</div>
        <div class="block sm:flex md:block lg:flex items-center justify-center">
         
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium"> {{{data.0.limite}}}</span>
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">limite de crédito </span>
          </div>
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium"> {{{data.0.qAtingida}}}</span>
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades vendidas no mês </span>
          </div>
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium"> {{{data.0.qMiss}}}</span>
                <span class="text-xl font-medium"> ({{{data.0.qPerc}}}% )</span>
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades faltando no mês </span>
          </div>
        </div>
      </div>
    </div>
  </div>

<section  class="py-10 bg-stone-100">
  <div class="mx-auto grid max-w-7xl ">
   
<div class="flex items-center justify-center">
  <div x-data="{ filters: ['Pedidos', 'Produtos', 'Premium'], links: ['tab1','tab2','tab3'],  selected: 'All' }" class="inline-flex rounded-lg my-3 bg-gray-100 bg-opacity-30 mx-auto">
    <template x-for="(filter, index, link) in filters,links">
      <button @click="selected = filter" 
      
                :class="[(index === filters.length -1) 
                        && '!rounded-r-lg', (index === 0) 
                        && '!rounded-l-lg', filter === selected 
                        && 'border-green-500 bg-green-500 text-white']"
                        
                class="py-[10px] sm:py-2 my-1 px-[12px] sm:px-6 inline-flex items-center justify-center font-medium border border-gray-50 text-center focus:bg-primary text-black text-sm sm:text-base capitalize bg-white"
                x-text="filter"
                
                :hx-get="/metrics/mesos/1+filter"

                hx-get="/metrics/mesos/tab2" 
                hx-target="#tab-contents" 
        >
      </button>
    </template>
    <div id="tab-contents" hx-get="/metrics/mesos/tab1" hx-trigger="load"></div>
  </div>
</div>
<h1 x-data="{ message: 'I ❤️ Alpine' }" x-text="message"></h1>


    
</div>
</section>