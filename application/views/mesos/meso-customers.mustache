
<!DOCTYPE html>
<html lang="pt-br">

<head>
  <title>Metas </title>

  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">


        <!-- awesome CSS -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">
        
        <!-- tailwindcss CSS -->
        <!--<link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet">-->
        
        <!-- tailwindcss CSS -->
        <script src="https://cdn.tailwindcss.com"></script>
         
        <!-- jquery 3.6 -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"
        integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="
        crossorigin="anonymous"></script>
        
        <!-- winbox bundle -->
        <script src="https://rawcdn.githack.com/nextapps-de/winbox/0.2.0/dist/winbox.bundle.js"></script>



       
        <!-- twitter-bootstrap//-->
        <!--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.0.1/css/bootstrap-grid.min.css" integrity="sha512-Aa+z1qgIG+Hv4H2W3EMl3btnnwTQRA47ZiSecYSkWavHUkBF2aPOIIvlvjLCsjapW1IfsGrEO3FU693ReouVTA==" crossorigin="anonymous" referrerpolicy="no-referrer" />-->
        
       
        

        <script src="https://cdnjs.cloudflare.com/ajax/libs/jeditable.js/2.0.19/jquery.jeditable.min.js" integrity="sha512-Jn+modod+CgT0sdSdiL5Zr74JeTDTR2DGzeSRG1oUVnZjZFnkdSPOFUiwAr/St/lIvYEyBaZ6F46vJE4fbHg6A==" crossorigin="anonymous"></script>
        
        <!--        /* Livequery   *///-->
        <!--<script src="https://office.vallery.com.br/jquery/livequery/jquery.livequery.js"></script>-->
        
         
        <!-- htmx -->
        <!--<script src="https://unpkg.com/htmx.org@1.8.6"></script>-->
        <script src="https://unpkg.com/htmx.org@1.6.0"></script>
        <script src="https://unpkg.com/htmx.org/dist/ext/client-side-templates.js"></script>
        <script src="https://unpkg.com/hyperscript.org@0.9.8"></script>
        <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.x.x/dist/alpine.min.js" defer></script>
        
        <script src="https://cdn.datatables.net/plug-ins/1.13.4/api/sum().js"></script>
        <!--<link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/prelude.css">-->
        <!--<link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/rainbow.css">-->
        <!--<link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/progress.css">-->
        
         <!-- mustache -->
        <script src="https://cdn.jsdelivr.net/npm/mustache@4.2.0/mustache.min.js"></script>
        
        <!-- Tabesorter  *///-->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.2/js/jquery.tablesorter.combined.min.js"></script>
        <!--Datatables-->
        <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/dt/jszip-2.5.0/dt-1.12.1/af-2.4.0/b-2.2.3/b-colvis-2.2.3/b-html5-2.2.3/b-print-2.2.3/cr-1.5.6/date-1.1.2/fc-4.1.0/fh-3.2.4/kt-2.7.0/r-2.3.0/rg-1.2.0/rr-1.2.8/sc-2.0.7/sb-1.3.4/sp-2.0.2/sl-1.4.0/sr-1.1.1/datatables.min.css"/>
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js"></script>
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js"></script>
        <script type="text/javascript" src="https://cdn.datatables.net/v/dt/jszip-2.5.0/dt-1.12.1/af-2.4.0/b-2.2.3/b-colvis-2.2.3/b-html5-2.2.3/b-print-2.2.3/cr-1.5.6/date-1.1.2/fc-4.1.0/fh-3.2.4/kt-2.7.0/r-2.3.0/rg-1.2.0/rr-1.2.8/sc-2.0.7/sb-1.3.4/sp-2.0.2/sl-1.4.0/sr-1.1.1/datatables.min.js"></script>

<!--<script src="//cdnjs.cloudflare.com/ajax/libs/numeral.js/2.0.6/numeral.min.js"></script>-->

<!--        /* JFrame  *///-->
<!--<script src="https://garage.pimentech.net/pimentech/js/jquery.jframe.js"></script>-->
<!--<script src="/K4/jframe.js"></script>-->
<!--        /* Jeditable  *///-->
<!--<script src="/K3/media/jquery/jeditable/jquery.jeditable.js"></script>-->
<!--        /* Tabesorter  *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.2/js/jquery.tablesorter.combined.min.js"></script>-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.3/js/extras/jquery.tablesorter.pager.min.js"></script>-->

<!--        /* Sparkline  *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>-->
<!--        /* HandleBars  *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.1.2/handlebars.min.js"></script>-->
<!--        /* unsemantic *///-->
<!--<script src="https://unsemantic.com/javascripts/application.js?1527127334"></script>-->
<!--        /* Moment.js *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>-->
<!--        /* imagesloaded *///-->
<!--<script src="https://unpkg.com/imagesloaded@4/imagesloaded.pkgd.min.js"></script>-->
<!--        /* bootstrap-select -->
<!--<script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.9/dist/js/bootstrap-select.min.js"></script>-->
 

</head>

<body>
 

 
 
 <div class="lg:container lg:mx-auto">
    <!-- This example requires Tailwind CSS v2.0+ -->
    
  <nav class="bg-gray-900">
  <div class="max-w-7xl mx-auto px-2 sm:px-6 lg:px-8">
    <div class="relative flex items-center justify-between h-16">
      <div class="absolute inset-y-0 left-0 flex items-center sm:hidden">
        <!-- Mobile menu button-->
        <button type="button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white" aria-controls="mobile-menu" aria-expanded="false">
         <span class="sr-only">Open main menu</span>
          <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
          <svg class="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <div class="flex-1 flex items-center justify-center sm:items-stretch sm:justify-start">
        <div class="flex-shrink-0 flex items-center">
          <img src="https://cdn.rolemak.com.br/media/rolemak-branco.png" width="170px" alt="Rolemak" title="Rolemak" border="0">
        </div>
        <!--<div class="hidden sm:block sm:ml-6">-->
        <!--  <div class="flex space-x-4">-->
        <!--    <a hx-get="/cdn/apps/ams/counting/index.html" hx-target="#screen" class="bg-gray-900 text-white px-3 py-2 rounded-md text-sm font-medium" aria-current="page">Máquinas</a>-->
        <!--    <a href="#" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Peças</a>-->
        <!--    <a href="#" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Autopeças</a>-->
        <!--    <a href="#" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Rolamentos</a>-->
            <!--<div  hx-trigger="load" hx-get="/cdn/apps/ams/product/search.html"></div>-->
        <!--  </div>-->
        <!--</div>-->
      </div>
    </div>
  </div>

  <!-- Mobile menu, show/hide based on menu state. -->
  <!--<div class="sm:hidden" id="mobile-menu">-->
  <!--  <div class="px-2 pt-2 pb-3 space-y-1">-->
      <!-- Current: "bg-gray-900 text-white", Default: "text-gray-300 hover:bg-gray-700 hover:text-white" -->
  <!--    <a hx-get="/cdn/sage/payments.html" hx-target="#screen" class="bg-gray-900 text-white px-3 py-2 rounded-md text-sm font-medium" aria-current="page">Apuração</a>-->

  <!--    <a href="#" class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium">Equipe</a>-->

  <!--    <a href="#" class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium">Projetos</a>-->

  <!--    <a href="#" class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium">Calendário</a>-->
  <!--  </div>-->
  <!--</div>-->
</nav>
</div>  

 

  <section  class="py-10 bg-stone-100 lg:container lg:mx-auto">
  <div class="mx-auto grid max-w-7xl ">
   
    

   {{#data}}
   

          <div class="m-4 max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl">
  <div class="md:flex">
    <div class="md:shrink-0">
      <img class="h-48 w-full object-cover md:h-full md:w-48" src="{{{Maps}}}" alt="Modern building architecture">
    </div>
    <div class="p-8">
      <div class="uppercase tracking-wide text-sm text-indigo-500 font-semibold">Região {{NomeMeso}} {{Meso}}</div>
      <a href="#" class="block mt-1 text-lg leading-tight font-medium text-black hover:underline">Estado de {{{NomeUF}}}</a>
      <p class="mt-2 text-slate-500">Meta  {{Meta}}</p>
    </div>
  </div>
</div>
        
        
           
         <table class="table table-auto">

           <thead>
             <tr>
                

                <th rowspan="2" class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Cliente</th>
                <th rowspan="2" class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-center block md:table-cell">Endereco</th>			
                <th rowspan="2" class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-center block md:table-cell">Vendedor</th>	
                <th colspan="9" class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-center block md:table-cell">Unidades Vendidas por Período</th>				
                
              
            </tr>
             <tr>


			          <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Ano</th>
			     
          		  <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Jan</th>
        		    <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Fev</th>
              	<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Mar</th>
                <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Abr</th>
                <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Mai</th>
                <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Jun</th>  
                 <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Média</th>
          		 <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Saldo</th>
          		 
                </tr>
           </thead>
         {{#Cidades}}
         <tr  class="text-sm hover:bg-sky-300">
           
         <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell capitalize text-md">
             <a color="#000600" width="80%" height="80%" class="winbox-iframe cursor-pointer" rel="/K3/tip/vcustomer/{{{idcli}}}/" title="{{{nome}}}">{{{nome}}} </a>
             
             
              <a color="#000600" width="80%" height="80%" class="winbox-iframe cursor-pointer" rel="https://office.vallery.com.br/K7/offer/sales/product/107/{{{idcli}}}?offer=1&segment=1&unity=8&discount=1" title="Gerar oferta">in</a>
            
               <a target="_blank" class=" cursor-pointer" href="https://office.vallery.com.br/K7/offer/sales/product/{{vendedor}}/{{{idcli}}}?offer=1&segment=1&unity=8&discount=1&time=1" title="Gerar oferta">out</a>
             
             
         </td>
     
      
                <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell bg-blue-500 p-2 text-white ">{{ender}} {{nro}} {{{bairro}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell bg-teal-700 p-2 text-white ">{{{nick}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-teal-500 p-2 text-white ">{{{qAno}}}</td>
                
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-gray-500 p-2 text-white ">{{{Jan}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-gray-500 p-2 text-white ">{{{Fev}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-gray-500 p-2 text-white ">{{{Mar}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-gray-500 p-2 text-white ">{{{Abr}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-gray-500 p-2 text-white ">{{{Mai}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-orange-500 p-2 text-white ">{{{Jun}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-lime-500 p-2 text-white ">{{{Avg}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell {{cor}} p-2 text-white ">{{{Diff}}}</td>
                
          </tr>
          {{/Cidades}}

        </table>

  
  {{/data}}

</div>
</section>


<script>

$(document).ready(function () {
  
        // $('.winbox-iframe').on('click', function(event) 
        // {
        //     var address = $(this).attr('rel');
        //     var title = $(this).attr('title') + ' - ' + address;
        //     var color   = $(this).attr('color');
        //     var width   = $(this).attr('width');
        //     var height  = $(this).attr('height');
            
        //     var winbox = new WinBox(
        //     {
        //         title: title,
        //         root: document.body,
        //         //     // html: "<h1>Lorem Ipsum</h1>"
        //         // html: '<iframe src="..." allow="..." sandbox="allow-scripts" referrerpolicy="..."></iframe>',
        //         // html: '<iframe  src="https://web.whatsapp.com/"  sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>',
        //         url: address,
        //         background: color,
        //         border: 4,
        //         class: "iframe",
        //         x: "center",
        //         y: "center",
        //         width: width,
        //         height: height
        //         // top: "0%",
        //         // right: "0%",
        //         // bottom: "0%",
        //         // left: "0%",
               
        //     });
          
        // }); 
  
   $('.winbox-iframe').on('click', function(event) 
         {
          var address = $(this).attr('rel');
            var title = $(this).attr('title') + ' - ' + address;
            var color   = $(this).attr('color');
            var width   = $(this).attr('width');
            var height  = $(this).attr('height');
            
            var winbox = new WinBox(
            {
                title: title,
                root: document.body,
                //     // html: "<h1>Lorem Ipsum</h1>"
                // html: '<iframe src="..." allow="..." sandbox="allow-scripts" referrerpolicy="..."></iframe>',
                // html: '<iframe  src="https://web.whatsapp.com/"  sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>',
                url: address,
                background: "#999",
                border: 1,
                // class: "iframe",
                x: "center",
                // y: "center",
                // width: width,
                // height: height,
                 top: "0%",
                // right: "10%",
                // bottom: "0%",
                // left: "10%",
                width: "99%",
                height: "1028px",
                // minheight: 55,
                // minwidth: 100,
                // maxheight: 1280,
                // maxwidth: 1600,
                autosize: true,
               
            });
          
        }); 


    var table =  $('.main').dataTable({
        order: false,
        lengthMenu: [
            [1, 25, 50, -1],
            [1, 5, 10, 'All'],
        ],
    });
        
    var table =  $('.table').dataTable({
       order: [[3, 'desc']],
       stateSave: true,
    //   dom: 'Bfrtip',
        buttons: [
            'columnsToggle',
                  
        ],
        
    
        colReorder: true,
        
        
        

            
          //     order: ['Meta', 'Brand', 'NCM'],
      //           // // "scrollCollapse": true,
      //           // "scrollX": true,
      //           // order: [[1, 'asc']],
      //           // rowGroup: {
      //           //     dataSrc: 4
      //           // },
                
      //           // "sDom": '<"bottom"flp><"clear">',
      //           // "searching": true,
      //           "paging": false,
      //           // select: {
      //           //     style: 'multi'
      //           // },
      //           // "bFilter": false,
      //           // "colReorder": true
	     });
	     
});
       
        
</script>

 
</body>
</html>




