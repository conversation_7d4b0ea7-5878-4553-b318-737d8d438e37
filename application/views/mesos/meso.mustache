<style>
  body {
    background: #e2e8f0;
  }
    *:hover {
      transition: all 150ms ease-in;
    }
  </style>
  
  <div class="antialiased max-w-7xl mx-auto my-12 bg-gray-300 px-8">
    <div class="relative block md:flex items-center">
      <div class="w-full  relative z-1 bg-gray-100 rounded shadow-lg overflow-hidden">
        <div class="text-lg font-medium text-blue-500  p-8 text-center border-b border-gray-200 tracking-wide"> Mesoregião  <span class="font-bold text-xl	"> {{{data.0.NomeMeso}}}</span></span> do estado de {{{data.0.NomeUF}}}</div>
        <div class="block sm:flex md:block lg:flex items-center justify-center">
         
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium"> {{{data.0.Meta}}}</span>
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades no mês </span>
          </div>
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium"> {{{data.0.qAtingida}}}</span>
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades vendidas no mês </span>
          </div>
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium"> {{{data.0.qMiss}}}</span>
                <span class="text-xl font-medium"> ({{{data.0.qPerc}}}% )</span>
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades faltando no mês </span>
          </div>
        </div>
      </div>
    </div>
  </div>

<section  class="py-10 bg-stone-100">
  <div class="mx-auto grid max-w-7xl ">
   
    
    <table id='invoice' class=" min-w-full border-collapse block md:table">
		<thead class="block md:table-header-group">
			<tr class="border border-green-500 md:border-none block md:table-row absolute -top-full md:top-auto -left-full md:left-auto  md:relative ">
			
			<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Vendedor</th>
					<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Cliente</th>
				
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Cidade</th>
			
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Estado</th>
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">2022</th>
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">s2023</th>
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">2023</th>
		    <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Jan</th>
		    <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Fev</th>
		    <th class="bg-green-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Mar</th>
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Atrasados</th>
		
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell break-normal	">Última Compra Dias</th>
			</tr>
		</thead>
		<tbody class="block md:table-row-group">
		  
   {{#data}}
   
   <tr class=" border border-grey-500 md:border-none block md:table-row">
     
				 
						  
					<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell text-sm	">
				  <span class="inline-block w-1/3 md:hidden font-bold">Vendedor</span>{{{nick}}}</td>    
				<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell capitalize text-md">
				  
				    <span class="inline-block w-1/3 md:hidden font-bold">Nome</span>
				    <span
				          class="cursor-pointer"
				          hx-push-url="true" 
				          hx-target="#Grid" 
                  hx-get="https://dev.office.internut.com.br/metrics/mesos/customer/1/{{{idcli}}}/"  >{{{nome}}}</span>
            
        </td>
			
				
				  
				  
				<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell	">
				  <span class="inline-block w-1/3 md:hidden font-bold">Cidade</span>{{{NomeMunic}}}</td>
				  
			
				<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell">
				  <span class="inline-block w-1/3 md:hidden font-bold">Estado</span>{{{estado}}}</td>
			
					<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell ">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Ano Passado</span>{{{qAnoPassado}}}</td>
	
			<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-blue-600 p-2 text-white 	">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">SSW</span>{{{ssw}}}</td>			  
				  
			<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell 	">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Ano</span>{{{qAno}}}</td>
					  
			<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell ">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Jan</span>{{{Jan}}}</td>
				  
			<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell ">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Fev</span>{{{Fev}}}</td>
				  
			<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell ">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Fev</span>{{{Mar}}}</td>
			<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell ">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Fev</span>{{{Abr}}}</td>
			<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell ">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Fev</span>{{{mai}}}</td>



				<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-green-600 p-2 text-white ">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Mar</span>{{{qMes}}}</td>
				  

				<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell text-sm 	">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Atrasados</span>{{{Atrasados}}}</td>

				<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell text-sm {{{rclass}}}	">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Última Compra</span>{{{LastPurchase}}}</td>
		
				
			</tr>
  
  {{/data}}
			
			
		</tbody>
	</table>
</div>
</section>

<script>
  
  
</script>