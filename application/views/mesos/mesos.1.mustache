
 
 <div class="antialiased max-w-7xl mx-auto my-12 bg-gray-300 px-8">
    <div class="relative block md:flex items-center">
      <div class="w-full  relative z-1 bg-gray-100 rounded shadow-lg overflow-hidden">
        <div class="text-lg font-medium text-blue-500  p-8 text-center border-b border-gray-200 tracking-wide"> Resultado por principais Mesoregiões do Brasil</div>
        <div class="block sm:flex md:block lg:flex items-center justify-center">
         
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium text-blue-500"> {{{data.0.tqDia}}}/<span class="text-gray-500">{{{data.0.MetaDia}}}/<span class="text-green-500">{{{data.0.qMedia}}}</span></span></span>
              
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades vendidas no dia/meta/média </span>
          </div>
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium text-blue-500"> {{{data.0.qAtingida}}}/<span class="text-gray-500">{{{data.0.MetaTillNow}}}</span></span>
              
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades vendidas no mês/meta </span>
          </div>
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium text-orange-400"> {{{data.0.Previsao}}}/<span class="text-gray-500">{{{data.0.Metas}}}</span></span>
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades previstas no mês/meta </span>
          </div>
        
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium text-red-500"> {{{data.0.qMiss}}}/<span class="text-gray-500">{{{data.0.DaysMissing}}}</span>/{{{data.0.MediaDiariaAtingir}}}</span>
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades faltando no mês/dias/média diária a atingir </span>
          </div>
  
         
        </div>
      </div>
    </div>
  </div>
  
  <section  class="py-10 bg-stone-100">
  <div class="mx-auto grid max-w-7xl ">
   
    
    <table id='invoice' class=" min-w-full border-collapse block md:table">
		<thead class="block md:table-header-group">
			<tr class="border border-green-500 md:border-none block md:table-row absolute -top-full md:top-auto -left-full md:left-auto  md:relative ">
			
			  <th class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell">Mapa Região</th>
			  <th class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell">Meso Região</th>

			
				<th class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell">Estado</th>
        <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Jan</th>				
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Fev</th>
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Mar</th>
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Meta</th>
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">% Atingida</th>
					<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Dia</th>
					<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">MetaDia</th>
			</tr>
		</thead>
		<tbody class="block md:table-row-group">
		  
   {{#data}}
   
   <tr class=" border border-grey-500 md:border-none block md:table-row">
     
				 
						<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell">
				  <span class="inline-block w-1/3 md:hidden font-bold">Meso</span> <img src="{{{Maps}}}" width="128px" alt="Meso Photo"></td>    
						  
					<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell">
				  <span class="inline-block w-1/3 md:hidden font-bold">Meso</span>
				  <a  rel="/metrics/mesos/meso/1/{{{Meso}}}/" 
                href="#" 
                class="q-meili bold  text-capitalize text-md" 
                title="{{{FantasiaMeso}}}" 
                data-hasqtip="0"> <span class="text-xs">{{{NomeMeso}}} ({{{qClientes}}}) </span>
            </a>
            
                  <div class="cursor-pointer	" hx-push-url="true" 
                hx-get="https://dev.office.internut.com.br/metrics/mesos/meso/1/{{{Meso}}}/" 
                hx-target="#Grid"   >
            <p class="text-xs" >{{{Cidades}}}</p>
        </div></td>    

				
				  
			
				<td class="p-2 md:border md:border-grey-500 text-left block md:table-cell  text-md">
				  <span class="inline-block w-1/3 md:hidden ">Estado</span>{{{sUF}}} </td>


					<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell ">
				  <span class="inline-block w-1/3 md:hidden text-right">Mes</span>{{{Jan}}}</td>
			
					<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell ">
				  <span class="inline-block w-1/3 md:hidden  text-right">Mes</span>{{{Fev}}}</td>
	
					<td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-green-700 p-2 text-white  ">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Mes</span>{{{qMes}}}</td>
	
			    <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell  bg-orange-600 p-2 text-white 	">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Meta</span>{{{Meta}}}</td>			  
				  
			    <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell p-2 {{{class}}} ">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Perc</span>{{Perc}}</td>
				  
				  <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell p-2 bg-green-500 p-2 text-white  ">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Dia</span>{{qDia}}</td>
				  
				    <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell p-2  ">
				  <span class="inline-block w-1/3 md:hidden font-bold text-right">Dia</span>{{MesoMetaDia}}</td>
					  

		
				
			</tr>
  
  {{/data}}
			
			
		</tbody>
	</table>
</div>
</section>

<section  class="py-10 bg-gray-100">
   
  <div class="mx-auto grid max-w-7xl  grid-cols-1 gap-6 p-6 sm:grid-cols-2 md:grid-cols-6 lg:grid-cols-5">
  {{#data}}
  
      <article class=" rounded-xl bg-white p-2 shadow-lg hover:shadow-xl hover:transform hover:scale-105 duration-300 ">
    
      <span  >
        <div class="relative flex items-end overflow-hidden rounded-xl">
          <img src="{{{Maps}}}" alt="Meso Photo">
        </div>


        <div class="cursor-pointer	" hx-push-url="true" 
                hx-get="https://dev.office.internut.com.br/metrics/mesos/meso/1/{{{Meso}}}/" 
                hx-target="#Grid"   >
            <p class="text-xs" >{{{FantasiaMeso}}}</p>
        </div>
            
        <div class="mt-1 p-2">
           
          <div>
            <h2 class="{{{tclass}}} "> 
             <div class="cursor-pointer	" hx-push-url="true" 
                hx-get="/metrics/mesos/cidades/1/{{{Meso}}}/" 
                hx-target="#Grid"   >
            <p class="text-xs" >{{{NomeMeso}}}</p>
            </div>
        
           
           </h2> 
                           
            <p class="mt-1 text-sm text-slate-400"> {{{NomeUF}}}</p>
          
          </div>
            <a  rel="/metrics/mesos/meso/1/{{{Meso}}}/" 
                href="#" 
                class="q-meili bold  text-capitalize text-md" 
                title="{{{FantasiaMeso}}}" 
                data-hasqtip="0"> <span class="text-xs">Ver Clientes ({{{qClientes}}}) </span>
            </a>
            
          <div class="mt-4 flex items-end justify-between">
            <p class="text-md font-bold text-blue-500">
              <div class="mt-4  text-center">
                <div class="inline-flex items-center">
                    <span class="text-xl font-medium" id="mak.inv|nome|id|1678894" >{{{qMes}}}/{{{Meta}}}<span class="block text-xs text-gray-600 mt-2">unidades meta/vendidos no mês </span></span>
                    <span class="text-xl font-medium {{{class}}} ">{{{Perc}}}%/<span class="text-gray-500">{{{data.0.DaysPassedPerc}}}%</span>  <span class="block text-xs text-gray-600 mt-2  {{{class}}} ">Porcentagem atingida/meta </span> </span>
                  </div>
                </div>
            </p>
          </div>
        </div>
      </span>
    </article>
  {{/data}}
  
  
  <div class="self-center	"><img src="https://atlassocioeconomico.rs.gov.br/upload/recortes/202205/12112806_51487_GDO.png" ></div>
</div>

</section>

