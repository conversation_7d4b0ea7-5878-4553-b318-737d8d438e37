

  <section  class="py-10 bg-stone-100 lg:container lg:mx-auto">
  <div class="mx-auto grid max-w-7xl ">
   
    
    <table id='invoice' class=" table  border-collapse block md:table">
		<thead class="block md:table-header-group">
			<tr class="border border-green-500 md:border-none block md:table-row absolute -top-full md:top-auto -left-full md:left-auto  md:relative ">
			
			  <th class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell">Mapa Região</th>
			  <th class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell">Meso Região</th>

			
				<th class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell">Estado</th>
	        <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Media 22</th>				
			
          		        <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Jan</th>
        		        <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Fev</th>
              		    <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Mar</th>
                  <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Abr</th>
                  <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Mai</th>
                  <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Jun</th>    
                  <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Jul</th>   
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Meta</th>

				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">% Atingida / Meta {{{data.0.DaysPassedPerc}}}%</th>
				<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Semana / Meta</th>
					<th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Hoje / Meta</th>
		
			</tr>
		</thead>
		<tbody class="block md:table-row-group">
		  
   {{#data}}
   
   <tr class=" border border-grey-500 md:border-none block md:table-row">
     
				 
      <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell"><span class="inline-block w-1/3 md:hidden font-bold">Meso</span> <img src="{{{Maps}}}" width="256px" alt="Meso Photo"></td>    
      
      <td nowrap class="p-2 md:border md:border-grey-500 text-left block md:table-cell">
        <span class="inline-block w-1/3 md:hidden font-bold ">Meso</span>
           
           <a color="#000600" width="80%" height="80%" class="winbox-iframe cursor-pointer text-base text-green-700" rel="/metrics/mesos/full/1/{{{Meso}}}/" title="{{{NomeMeso}}}">{{{NomeMeso}}}</a>
           
          
        <p>{{{Cidades}}}</p>
      </td>    
      
      <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell  text-md">
          <span class="inline-block w-1/3 md:hidden ">Estado</span>{{{sUF}}} <span class="text-xs">({{qClientes}}) </span></td>
      
      <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell text-gray-400">
          <span class="inline-block w-1/3 md:hidden text-right">Mes</span>{{{qMediaAnoPassado}}}</td>
      
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-gray-600 p-2 text-white ">{{{Jan}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-gray-500 p-2 text-white ">{{{Fev}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-gray-600 p-2 text-white ">{{{Mar}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-gray-500 p-2 text-white ">{{{Abr}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-gray-600 p-2 text-white ">{{{Mai}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-gray-500 p-2 text-white ">{{{Jun}}}</td>
                <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell bg-green-500 p-2 text-white ">{{{Jul}}}</td>                

         
        <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell  text-blue-500"> 
        <span class="inline-block w-1/3 md:hidden font-bold text-right">Mes</span><span class="text-orange-600 	">{{{Meta}}}</span></td>			  
      
      <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell p-2 {{{class}}} ">
        <span class="inline-block w-1/3 md:hidden font-bold text-right">Perc</span>{{Perc}}%</td>
      
      <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell  text-blue-500">  
        <span class="inline-block w-1/3 md:hidden font-bold text-right">Mes</span>{{{qSemana}}}/<span class="text-orange-600 	">{{{MetaSemana}}}</span></td>	
      
      <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell p-2 text-blue-500 p-2 ">
          <span class="inline-block w-1/3 md:hidden font-bold text-right">Dia</span>{{qDia}}/<span class=" text-orange-500  ">      
          {{MesoMetaDia}}</span></td>


		
				
			</tr>
  
  {{/data}}
			
			
		</tbody>
	</table>
</div>
</section>


<script>
  
        // $('.winbox-iframe').on('click', function(event) 
        // {
        //     var address = $(this).attr('rel');
        //     var title = $(this).attr('title') + ' - ' + address;
        //     var color   = $(this).attr('color');
        //     var width   = $(this).attr('width');
        //     var height  = $(this).attr('height');
            
        //     var winbox = new WinBox(
        //     {
        //         title: title,
        //         root: document.body,
        //         //     // html: "<h1>Lorem Ipsum</h1>"
        //         // html: '<iframe src="..." allow="..." sandbox="allow-scripts" referrerpolicy="..."></iframe>',
        //         // html: '<iframe  src="https://web.whatsapp.com/"  sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>',
        //         url: address,
        //         background: color,
        //         border: 4,
        //         class: "iframe",
        //         x: "center",
        //         y: "center",
        //         width: width,
        //         height: height
        //         // top: "0%",
        //         // right: "0%",
        //         // bottom: "0%",
        //         // left: "0%",
               
        //     });
          
        // }); 
  
   $('.winbox-iframe').on('click', function(event) 
         {
          var address = $(this).attr('rel');
            var title = $(this).attr('title') + ' - ' + address;
            var color   = $(this).attr('color');
            var width   = $(this).attr('width');
            var height  = $(this).attr('height');
            
            var winbox = new WinBox(
            {
                title: title,
                root: document.body,
                //     // html: "<h1>Lorem Ipsum</h1>"
                // html: '<iframe src="..." allow="..." sandbox="allow-scripts" referrerpolicy="..."></iframe>',
                // html: '<iframe  src="https://web.whatsapp.com/"  sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>',
                url: address,
                background: "#333",
                border: 1,
                // class: "iframe",
                x: "center",
                // y: "center",
                // width: width,
                // height: height,
                 top: "0%",
                // right: "10%",
                // bottom: "0%",
                // left: "10%",
                width: "70%",
                height: 1280,
                // minheight: 55,
                // minwidth: 100,
                // maxheight: 1280,
                // maxwidth: 1600,
                autosize: true,
               
            });
          
        }); 
        
       $('.table').dataTable({
            
                // order: ['Supplier', 'Brand', 'NCM'],
                // // "scrollCollapse": true,
                // "scrollX": true,
                // order: [[1, 'asc']],
                // rowGroup: {
                //     dataSrc: 4
                // },
                
                // "sDom": '<"bottom"flp><"clear">',
                // "searching": true,
                "paging": false,
                // select: {
                //     style: 'multi'
                // },
                // "bFilter": false,
                // "colReorder": true
	        })
           
        
        
</script>