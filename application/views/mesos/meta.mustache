
 
 <div class="antialiased max-w-7xl mx-auto my-12 bg-gray-300 px-8 ">
    <div class="relative block md:flex items-center">
      <div class="w-full  relative z-1 bg-gray-100 rounded shadow-lg overflow-hidden">
        <div class="text-lg font-medium text-blue-500  p-8 text-center border-b border-gray-200 tracking-wide"> Resultado por principais Mesoregiões do Brasil</div>
        <div class="block sm:flex md:block lg:flex items-center justify-center">
         
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium text-blue-500"> {{{data.0.tqDia}}}/<span class="text-gray-500">{{{data.0.MetaDia}}}/<span class="text-green-500">{{{data.0.qMedia}}}</span></span></span>
              
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades vendidas no dia/meta/média </span>
          </div>
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium text-blue-500"> {{{data.0.qAtingida}}}/<span class="text-gray-500">{{{data.0.MetaTillNow}}}</span></span>
              
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades vendidas no mês/meta </span>
          </div>
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium text-orange-400"> {{{data.0.Previsao}}}/<span class="text-gray-500">{{{data.0.Metas}}}</span></span>
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades previstas no mês/meta </span>
          </div>
        
          <div class="mt-4 mb-8 sm:m-8 md:m-0 md:mt-4 md:mb-8 lg:m-8 text-center">
            <div class="inline-flex items-center">
              <span class="text-3xl font-medium text-red-500"> {{{data.0.qMiss}}}/<span class="text-gray-500">{{{data.0.DaysMissing}}}</span>/{{{data.0.MediaDiariaAtingir}}}</span>
            
            </div>
            <span class="block text-sm text-gray-600 mt-2">unidades faltando no mês/dias/média diária a atingir </span>
          </div>
  
         
        </div>
      </div>
    </div>
  </div>
  
 


