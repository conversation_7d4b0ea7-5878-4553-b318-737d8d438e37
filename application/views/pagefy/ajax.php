

  <!-- tailwindcss CSS -->
  <!--<link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet">-->

  <!-- awesome font -->
  <!--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">-->

  <!-- htmx -->
  <!--<script src="https://unpkg.com/htmx.org@1.6.0"></script>-->
  <!--<script src="https://unpkg.com/htmx.org/dist/ext/client-side-templates.js"></script>-->
  <!--<script src="https://unpkg.com/hyperscript.org@0.0.9"></script>-->
  <!--<script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.x.x/dist/alpine.min.js" defer></script>-->
 
   <!--jquery 3.6 -->
  <!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"-->
  <!--  integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="-->
  <!--  crossorigin="anonymous"></script>-->
  <!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jeditable.js/2.0.19/jquery.jeditable.min.js"-->
  <!--  integrity="sha512-Jn+modod+CgT0sdSdiL5Zr74JeTDTR2DGzeSRG1oUVnZjZFnkdSPOFUiwAr/St/lIvYEyBaZ6F46vJE4fbHg6A=="-->
  <!--  crossorigin="anonymous"></script>-->


  <!-- jquery 3.6 -->
  <!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="   crossorigin="anonymous"></script>-->
  <!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jeditable.js/2.0.19/jquery.jeditable.min.js" integrity="sha512-Jn+modod+CgT0sdSdiL5Zr74JeTDTR2DGzeSRG1oUVnZjZFnkdSPOFUiwAr/St/lIvYEyBaZ6F46vJE4fbHg6A=="   <!--  crossorigin="anonymous"></script>-->

  <!-- Tabesorter  *///-->
  <!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.2/js/jquery.tablesorter.combined.min.js"></script>-->
 
  <!--        /* Livequery   *///-->
  <!--<script src="https://office.vallery.com.br/jquery/livequery/jquery.livequery.js"></script>-->
  
  <!-- mustache -->
  <!--<script src="https://cdn.jsdelivr.net/npm/mustache@4.2.0/mustache.min.js"></script>-->



  <div id="full" class="bg-gray-900 m-0.5">
    <!-- menu -->
    <!--<section hx-trigger="load" hx-get="/cdn/explore/tailwindcss/menu.html"></section>-->

    <!-- screen -->
    <div id="screen" class="h-full md:h-screen">
      <div class="flex items-center  min-h-screen bg-gray-100">
	      <div class="col-span-12">
		      <div class="overflow-auto lg:overflow-visible text-dark ">
		          <!--<table class="table text-gray-400 border-separate space-y-6 text-sm">-->
                <?=$content?>
              <!--</table>-->
              
                
          </div>
        </div>
      </div>  
    </div>
  </div>

  
   <script src="/cdn/explore/jeditable.js"></script>
  
  
        
    