
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover, shrink-to-fit=no">
    <meta name="robots" content="index, follow">
    <title>CashFlow</title>
    <link rel="stylesheet" href="https://nextapps-de.github.io/winbox/dist/css/winbox.min.css">
    <link rel="stylesheet" href="https://nextapps-de.github.io/winbox/dist/css/themes/modern.min.css">
    <link rel="stylesheet" href="https://nextapps-de.github.io/winbox/dist/css/themes/white.min.css">
    <script src="https://rawcdn.githack.com/nextapps-de/winbox/0.1.8/dist/winbox.bundle.js"></script>
    <!--<script src="dist/winbox.bundle.js"></script>-->
    
    <!--<link rel="stylesheet" href="https://nextapps-de.github.io/winbox/demo/style.css">-->
    <link rel="stylesheet" href="https://rsms.me/raster/raster2.css?v=20">
     <!-- tailwindcss CSS -->
    <!--<link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet">-->

<style>

:root {
  --fontSize: calc(100vh / 95);
  --margin: 6rem;
}
@media only screen and (max-width: 1000px) { :root {
  --fontSize: calc(100vmin / 70);
}}

html {
  background: #111;
  background-image: linear-gradient(4deg, #111111, #141414);
  background-size: 100%;
  background-attachment: fixed;
  color: rgba(252,252,252,0.99);
}
@media print {
  html { background: white }
}
body { padding: 0 0 3rem 0 }

footer { margin: var(--margin) }

.flex-h { flex-wrap: wrap; justify-content: center }
.black { color: black; }

.noise {
  background-image: url(https://rsms.me/raster/examples/noise512.png);
  background-size: 256px;
  opacity: 0.02;
  pointer-events: none;
  position: fixed;
  top:0;
  left:0;
  right:0;
  bottom:0;
}
@media print {
  .noise { display:none; }
}

.poster {
  width: 60rem;
  height: 84rem;
  background: #555;
  margin: var(--margin) calc(var(--margin) / 2);
  box-sizing: border-box;
  flex: 0 0 auto;
  box-shadow: 0px 2px 8px black;
}
@media print {
  .poster {
    box-shadow: none;
    page-break-before: always;
    page-break-after: always;
    page-break-inside: avoid;
    break-before: always;
    break-after: always;
    break-inside: avoid-page;
  }
}

.poster1 {
  background: #FEED01;
  background-image: linear-gradient(175deg, #FEED01, #F9E010);
  color: rgba(0,0,0,0.9);
}


/*.poster2 {*/
/*  background: #555;*/
/*  background-image:*/
/*    linear-gradient(20deg, rgba(255, 0, 0, 0.02), rgba(0, 255, 0, 0.03)),*/
/*    linear-gradient(175deg, #555, #555960);*/
/*    color: rgba(252,252,252,0.98);*/
/*}*/
.poster2 {
  background: #FFF;
  background-image: linear-gradient(175deg, #FFF, #FFF);
  color: rgba(0,0,0,0.9);
}

html.white-bg {
  background: white;
  color:rgba(0,0,0,0.99);
}
html.white-bg .poster {
  box-shadow: 0px 0.5rem 1.2rem rgba(0,0,0, 0.1);
}

.winbox.my-theme{
    background: #fff;
}
.winbox.my-theme .wb-body {
    color: #fff;
    background: #131820;
}
.winbox.my-theme .wb-title {
    color: #000;
}
.winbox.my-theme .wb-icon {
    filter: invert(1);
}
  </style>    
</head>
<body>
   <div class=noise></div>
  <div class="flex-w">
    <r-grid columns=4 class="poster poster2 padding1">
      <r-cell span=3-3>
        Rolemak<br>
        Cash Flow
      </r-cell>
      <r-cell span=4-4>
        Report 006<br>
        June 2021
      </r-cell>
      <r-cell span=row>
        <h1 class=large>
          <hr>
          Cash Flow<br>
          <hr>
          <!--Grid System<br>-->
        </h1>
      </r-cell>
      <!--<r-cell span=3-3>-->
      <!--  Minimal and straight-forward CSS grid system utilizing-->
      <!--  descriptive HTML rather than semantic CSS.-->
      <!--</r-cell>-->
      
      <r-cell span=1-4  class=h5 style="line-height:1.3;padding-top:0.5rem;font-weight:500">
            <?=$content?>
      </r-cell>
      
      <!--<r-cell span=3-1 class="bottom">-->
      <!--  4 column grid-->
      <!--</r-cell>-->
      <!--<r-cell class="bottom">-->
      <!--  <div class="scroll">-->
      <!--    <div class="wrapper">-->
      <!--      <div class="break"></div>-->
      <!--      <a  _="on click js window.tip['sage'](); end" >Sage</a>-->
      <!--      <a  _="on click js window.tip['fms'](); end" >FMS</a>-->
      <!--      <a  _="on click js window.tip['oc'](); end" >OC</a>-->
      <!--    </div>-->
      <!--  </div>-->
      <!--</r-cell>-->
    </r-grid>
    
   


  </div>
  
    <div class="background"></div>
 
    
    <script>

        (function(){

            let winbox;

            window.tip = {

                "oc": function(){

                    var winbox = new WinBox({

                        title: "OC",
                    //     // html: "<h1>Lorem Ipsum</h1>"
                         url: "https://office.vallery.com.br/oc/",
                         class: "my-theme",
                          x: "center",
    y: "center",
    width: "80%",
    height: "55%"
                        // background: "#222",
                        // right: 50,
                        // bottom: 0,
                        // left: 50,
                          // onfocus: function(){
                          //     this.setBackground("#000");
                          // },
                          // onblur: function(){
                          //     this.setBackground("#999");
                          // },
                          // onresize: function(width, height){
                          //     this.body.textContent = (
                          //         "width: " + width + ", " +
                          //         "height: " + height
                          //     );
                          // },
                          // onmove: function(x, y){
                          //     this.body.textContent = (
                          //         "x: " + x + ", " +
                          //         "y: " + y
                          //     );
                          // },
                        //   class: "modern"
                    });
                    // var winbox = new WinBox("Custom Viewport");
                    // winbox.title = "Rolemak Comercial Ltdax",
                    //winbox.url = "/cdn/ams/",
                    //  winbox.width = 800;
                    //  winbox.height = 500;
                    //  winbox.resize();
                    
                    //  winbox.x = 10;
                    //  winbox.y = 10;
                    // winbox.move();
                },
                "ships": function(){

                     var winbox = new WinBox("Embarques", {

                        url: "https://office.vallery.com.br/K3/embarques/",
                        class: "my-theme",
                        x: "center",
    y: "center",
    width: "60%",
    height: "60%"
                    });
                },
                "sage": function(){

                    new WinBox("SAGE", {

                        url: "https://office.vallery.com.br/sage/payments/",
                         class: "my-theme",
                        // background: "#9ac",
                         x: "center",
    y: "center",
    width: "80%",
    height: "80%"
                        
                        // left: 300,
                    });
                },
                
                
            };

        }());
        
    </script>
    
    <!-- htmx -->
    <script src="https://unpkg.com/htmx.org@1.3.3"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/client-side-templates.js"></script>
    <script src="https://unpkg.com/hyperscript.org@0.0.9"></script>

</body>
</html>