<!DOCTYPE html>
<html lang="pt-br">

<head>
  <title>Rolemak</title>

  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <!-- tailwindcss CSS -->
  <link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet">
   <script src="https://cdn.tailwindcss.com"></script>
    
<!-- Bootstrap CSS  Datatables-->
<link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.21/css/dataTables.bootstrap.min.css" />
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.21/css/jquery.dataTables.min.css">
<link href="https://mottie.github.io/tablesorter/css/theme.blue.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.5.0/css/bootstrap-datepicker3.min.css">

<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>


  <!-- awesome font -->
  <!--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">-->

  <!-- htmx -->
  <script src="https://unpkg.com/htmx.org@1.3.3"></script>
  <script src="https://unpkg.com/htmx.org/dist/ext/client-side-templates.js"></script>
  <script src="https://unpkg.com/hyperscript.org@0.0.9"></script>
  <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.x.x/dist/alpine.min.js" defer></script>
 
   <!--jquery 3.6 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"
    integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="
    crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jeditable.js/2.0.19/jquery.jeditable.min.js"
    integrity="sha512-Jn+modod+CgT0sdSdiL5Zr74JeTDTR2DGzeSRG1oUVnZjZFnkdSPOFUiwAr/St/lIvYEyBaZ6F46vJE4fbHg6A=="
    crossorigin="anonymous"></script>

  <link rel="stylesheet" href="https://nextapps-de.github.io/winbox/dist/css/winbox.min.css">
  <!--<link rel="stylesheet" href="https://nextapps-de.github.io/winbox/dist/css/themes/modern.min.css">-->
  <!--<link rel="stylesheet" href="https://nextapps-de.github.io/winbox/dist/css/themes/white.min.css">-->
  <script src="https://rawcdn.githack.com/nextapps-de/winbox/0.2.0/dist/winbox.bundle.js"></script>

  <!-- jquery 3.6 -->
  <!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="   crossorigin="anonymous"></script>-->
  <!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jeditable.js/2.0.19/jquery.jeditable.min.js" integrity="sha512-Jn+modod+CgT0sdSdiL5Zr74JeTDTR2DGzeSRG1oUVnZjZFnkdSPOFUiwAr/St/lIvYEyBaZ6F46vJE4fbHg6A=="   <!--  crossorigin="anonymous"></script>-->

  <!-- Tabesorter  *///-->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.2/js/jquery.tablesorter.combined.min.js"></script>
 
  <!--        /* Livequery   *///-->
  <script src="https://office.vallery.com.br/jquery/livequery/jquery.livequery.js"></script>
  
  <!-- mustache -->
  <!--<script src="https://cdn.jsdelivr.net/npm/mustache@4.2.0/mustache.min.js"></script>-->


</head>

<body>


 
    <div id="screen" class="h-full md:h-screen">
      <div class="flex items-center justify-center min-h-screen bg-gray-100">
	      <div class="col-span-12">
	          
		      <div class="overflow-auto lg:overflow-visible text-white text-xs">
		          <!--<table class="table text-gray-400 border-separate space-y-6 text-sm">-->
                <?=$content?>
              <!--</table>-->
              
                
          </div>
        </div>
      </div>  
    </div>
 



<script>
   $('.table').livequery(function() {
            $(this).tablesorter();
        });
        
</script>        
  
   <script src="/cdn/explore/jeditable.js"></script>
  
  
        <!-- htmx -->
<!--    <script src="https://unpkg.com/htmx.org@1.3.3"></script>-->
    <!--<script src="https://unpkg.com/htmx.org/dist/ext/client-side-templates.js"></script>-->
<!--    <script src="https://unpkg.com/hyperscript.org@0.0.9"></script>-->


<!--        /* JQuery *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/2.2.4/jquery.min.js"></script>-->
<!--<script type="text/javascript" language="javascript" src="https://code.jquery.com/jquery-3.5.1.js"></script>-->

<!--        /* Jquery ui  *///-->
<!--<script src="https://code.jquery.com/ui/1.12.0/jquery-ui.min.js"></script>-->

<!--        /* Popper  *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>-->

<!--        /* Bootstrap 4  *///-->

<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js" integrity="sha384-OgVRvuATP1z7JjHLkuOU7Xw704+h835Lr+6QL9UvYjZE3Ipu6Tp75j7Bh/kR0JKI" crossorigin="anonymous"></script>

<!--        /* Livequery   *///-->
<!--<script src="//legacy.vallery.com.br//K/media/jquery/livequery/jquery.livequery.js"></script>-->
<!--        /* qTip 2  *///-->
<!--<script src="//cdn.jsdelivr.net/qtip2/3.0.3/jquery.qtip.min.js"></script>-->

<!--        /* JFrame  *///-->
<!--<script src="//garage.pimentech.net/pimentech/js/jquery.jframe.js"></script>-->
<!--<script src="/K3/media/jquery/jquery.jframe.js"></script>-->

<!--        /* Jeditable  *///-->
<!--<script src="/K3/media/jquery/jeditable/jquery.jeditable.js"></script>-->
<!--<script src="/K3/media/jquery/jeditable/jquery.jeditable.datepicker.js"></script>-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.9.2/i18n/jquery.ui.datepicker-zh-TW.min.js"></script>-->

<!--        /* Tabesorter  *///-->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.2/js/jquery.tablesorter.combined.min.js"></script>

<!--        /* Sparkline  *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>-->

<!--        /* HandleBars  *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.1.2/handlebars.min.js"></script>-->

<!--        /* unsemantic *///-->
<!--<script src="https://unsemantic.com/javascripts/application.js?1527127334"></script>-->

<!--        /* Moment.js *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>-->

<!--        /* imagesloaded *///-->
<!--<script src="https://unpkg.com/imagesloaded@4/imagesloaded.pkgd.min.js"></script>-->

<!--        /* bootstrap-select -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.9/dist/js/bootstrap-select.min.js"></script>
<!--        /* infinite-scroll -->
<!--<script src="https://unpkg.com/infinite-scroll@3/dist/infinite-scroll.pkgd.min.js"></script>//-->

<!--        /* DatePicker *///-->
<script type='text/javascript' src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<!--<script type='text/javascript' src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/locales/bootstrap-datepicker.pt-BR.min.js"></script>-->
<script type='text/javascript' src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/locales/bootstrap-datepicker.zh-TW.min.js"></script>

<!--        /* Datatables *///-->
<!--<script type='text/javascript' src="https://cdn.datatables.net/plug-ins/1.10.21/api/sum().js"></script>-->
<script type="text/javascript" src="https://cdn.datatables.net/1.10.21/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.21/js/dataTables.bootstrap.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/bs4/jszip-2.5.0/dt-1.10.21/af-2.3.5/b-1.6.2/b-colvis-1.6.2/b-flash-1.6.2/b-html5-1.6.2/b-print-1.6.2/cr-1.5.2/fc-3.3.1/fh-3.1.7/kt-2.5.2/r-2.2.5/rg-1.1.2/rr-1.2.7/sc-2.0.2/sp-1.1.1/sl-1.3.1/datatables.min.css"/>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/v/bs4/jszip-2.5.0/dt-1.10.21/af-2.3.5/b-1.6.2/b-colvis-1.6.2/b-flash-1.6.2/b-html5-1.6.2/b-print-1.6.2/cr-1.5.2/fc-3.3.1/fh-3.1.7/kt-2.5.2/r-2.2.5/rg-1.1.2/rr-1.2.7/sc-2.0.2/sp-1.1.1/sl-1.3.1/datatables.min.js"></script>

<!--        /* Numeral *///-->
<script src="//cdnjs.cloudflare.com/ajax/libs/numeral.js/2.0.6/numeral.min.js"></script>

<link type="text/css" href="//gyrocode.github.io/jquery-datatables-checkboxes/1.2.12/css/dataTables.checkboxes.css" rel="stylesheet" />
<script type="text/javascript" src="//gyrocode.github.io/jquery-datatables-checkboxes/1.2.12/js/dataTables.checkboxes.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/awesomplete/1.1.5/awesomplete.js" integrity="sha512-t3zV/oAkSZUrvb/7p1q1+uh/i56JinQ+bmiulnHhbHZc7dq09CxJ1BByyi7A4+lF76+CcJtSVFtb7na7Oe7Cgg==" crossorigin="anonymous"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/awesomplete/1.1.5/awesomplete.min.css" integrity="sha512-RT/9M3vzjYojy97YiNTZbyrQ4nJ+gNoinZKTGSVvLITijfQIAIDNicUe+U2KaeOWo9j7IbRzRisx/7eglOc+wA==" crossorigin="anonymous" />

  
   <script>

    
         $('.winbox-iframe').livequery('click', function(event) 
         {
            var address = $(this).attr('rel');
            var title = $(this).attr('title');
            
            var winbox = new WinBox(
            {
                title: title,
                root: document.body,
                //     // html: "<h1>Lorem Ipsum</h1>"
                // html: '<iframe src="..." allow="..." sandbox="allow-scripts" referrerpolicy="..."></iframe>',
                // html: '<iframe  src="https://web.whatsapp.com/"  sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>',
                url: address,
                background: "#666",
                border: 4,
                class: "iframe",
                top: "0%",
                right: "0%",
                bottom: "0%",
                left: "0%"
                //   class: "modern"
            });
          
        }); 
        
         $('.tablex').livequery(function(){
	        $(this).dataTable({
            
                // order: ['Supplier', 'Brand', 'NCM'],
                searchPanes:{
                    layout: 'columns-4',
                    viewTotal: true,
                    columns: [8],
                },
                dom: 'Pfrtip',
                // // "scrollCollapse": true,
                // "scrollX": true,
                //order: [[1, 'asc']],
                // rowGroup: {
                //     dataSrc: 4
                // },
                
                // "sDom": '<"bottom"flp><"clear">',
                "searching": true,
                "paging": true,
                // select: {
                //     style: 'multi'
                // },
                // "bFilter": false,
                // "colReorder": true
	        })
            });
        
        
          $('.table').livequery(function(){
	        $(this).dataTable({
	           //// fixedColumns:   true,
	           "dom": '<"top"fl Brt<"clear">>rt<"bottom"ifp<"clear">>',
	           "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            //   dom: 'Bfrtip',
	           buttons: [
                    // {
                    //     extend: 'colvis',
                    //     collectionLayout: 'fixed two-column'
                    // },
                    //  'columnsToggle',
                    
                    'copy', 
                    // 'csv', 
                    {
                        extend: 'excelHtml5',
                        title: 'export-file',
                        exportOptions: {
                            columns: ':visible'
                        }
                    },
                    {
                        extend: 'pdfHtml5',
                        title: 'export-file',
                        orientation: 'landscape',
                        pageSize: 'A4',
                        exportOptions: {
                           columns: ':visible'
                        },
                        // download: 'open',
                        // exportOptions: {
                        //     columns: [ 0, 1, 2, 5 ]
                        // }
                    },
                    // {
                    //     text: 'JSON',
                    //     action: function ( e, dt, button, config ) {
                    //         var data = dt.buttons.exportData();
         
                    //         $.fn.dataTable.fileSave(
                    //             new Blob( [ JSON.stringify( data ) ] ),
                    //             'Export.json'
                    //         );
                    //     }
                    // },
                    {
                        extend: 'print',
                        customize: function ( win ) {
                            $(win.document.body)
                                .css( 'font-size', '10pt' )
                                // .prepend(
                                //     '<img src="http://datatables.net/media/images/logo-fade.png" style="position:absolute; top:0; left:0;" />'
                                // );
         
                            $(win.document.body).find( 'table' )
                                .addClass( 'compact' )
                                .css( 'font-size', 'inherit' );
                        }
                    },
                    
                    
                ],
                // fixedColumns:   {
                //     leftColumns: 2
                // },
                // scrollY:        '50vh',
                // scrollCollapse: true,
                // paging:         true,
                // scrollX: true,
                // // "scrollY": '200px',
                // order: [
                //     [0, 'asc']
                // ],
                // // select: true,
                // colReorder: true,
                // autoFill: {
                //     focus: 'click'
                // },
                // keys:    true,
                // select: {
                //     style: 'multi'
                // },
                // "pagingType": "simple",


                // // "sDom": '<"bottom"flp><"clear">',
                //  "paging": false,
                // "bFilter": false
            
	        })
        });
        
        const config = {
  type: 'line',
  data: data,
  options: {
    responsive: true,
    plugins: {
      title: {
        display: true,
        text: (ctx) => 'Point Style: ' + ctx.chart.data.datasets[0].pointStyle,
      }
    }
  }
};
const data = {
  labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6'],
  datasets: [
    {
      label: 'Dataset',
      data: Utils.numbers({count: 6, min: -100, max: 100}),
      borderColor: Utils.CHART_COLORS.red,
      backgroundColor: Utils.transparentize(Utils.CHART_COLORS.red, 0.5),
      pointStyle: 'circle',
      pointRadius: 10,
      pointHoverRadius: 15
    }
  ]
};
const actions = [
  {
    name: 'pointStyle: circle (default)',
    handler: (chart) => {
      chart.data.datasets.forEach(dataset => {
        dataset.pointStyle = 'cirlce';
      });
      chart.update();
    }
  },
  {
    name: 'pointStyle: cross',
    handler: (chart) => {
      chart.data.datasets.forEach(dataset => {
        dataset.pointStyle = 'cross';
      });
      chart.update();
    }
  },
  {
    name: 'pointStyle: crossRot',
    handler: (chart) => {
      chart.data.datasets.forEach(dataset => {
        dataset.pointStyle = 'crossRot';
      });
      chart.update();
    }
  },
  {
    name: 'pointStyle: dash',
    handler: (chart) => {
      chart.data.datasets.forEach(dataset => {
        dataset.pointStyle = 'dash';
      });
      chart.update();
    }
  },
  {
    name: 'pointStyle: line',
    handler: (chart) => {
      chart.data.datasets.forEach(dataset => {
        dataset.pointStyle = 'line';
      });
      chart.update();
    }
  },
  {
    name: 'pointStyle: rect',
    handler: (chart) => {
      chart.data.datasets.forEach(dataset => {
        dataset.pointStyle = 'rect';
      });
      chart.update();
    }
  },
  {
    name: 'pointStyle: rectRounded',
    handler: (chart) => {
      chart.data.datasets.forEach(dataset => {
        dataset.pointStyle = 'rectRounded';
      });
      chart.update();
    }
  },
  {
    name: 'pointStyle: rectRot',
    handler: (chart) => {
      chart.data.datasets.forEach(dataset => {
        dataset.pointStyle = 'rectRot';
      });
      chart.update();
    }
  },
  {
    name: 'pointStyle: star',
    handler: (chart) => {
      chart.data.datasets.forEach(dataset => {
        dataset.pointStyle = 'star';
      });
      chart.update();
    }
  },
  {
    name: 'pointStyle: triangle',
    handler: (chart) => {
      chart.data.datasets.forEach(dataset => {
        dataset.pointStyle = 'triangle';
      });
      chart.update();
    }
  }
];
    </script>
  
        
    
</body>

</html>