

  <div id="full" class="bg-gray-900 m-0.5">
    <!-- menu -->
    <!--<section hx-trigger="load" hx-get="/cdn/explore/tailwindcss/menu.html"></section>-->

    <!-- screen -->
    <div id="screen" class="h-full md:h-screen">
      <div class="flex items-center  min-h-screen bg-gray-100">
	      <div class="col-span-12">
		      <div class="overflow-auto lg:overflow-visible text-dark ">
		          <!--<table class="table text-gray-400 border-separate space-y-6 text-sm">-->
                <?=$content?>
              <!--</table>-->
              
                
          </div>
        </div>
      </div>  
    </div>
  </div>

  
   <script src="/cdn/explore/jeditable.js"></script>
  
   
<script>
  $('.table').livequery(function() {
      $(this).tablesorter(
      {
      
       theme : "bootstrap",
       widthFixed: false,

        // widget code contained in the jquery.tablesorter.widgets.js file
        // use the zebra stripe widget if you plan on hiding any rows (filter widget)
        // the uitheme widget is NOT REQUIRED!
        // widgets : [ "filter", "columns", "zebra" ],
        widgets : [ "columns" ],
    
        widgetOptions : {
          // using the default zebra striping class name, so it actually isn't included in the theme variable above
          // this is ONLY needed for bootstrap theming if you are using the filter widget, because rows are hidden
          zebra : ["even", "odd"],
    
          // class names added to columns when sorted
          columns: [ "primary", "secondary", "tertiary" ],
    
          // reset filters button
          filter_reset : ".reset",
    
          // extra css class name (string or array) added to the filter element (input or select)
          filter_cssFilter: [
            'form-control',
            'form-control',
           
          ]
    
    }
    }).tablesorterPager({

        // target the pager markup - see the HTML block below
        container: $(".ts-pager"),
    
        // target the pager page select dropdown - choose a page
        cssGoto  : ".pagenum",
    
        // remove rows from the table to speed up the sort of large tables.
        // setting this to false, only hides the non-visible rows; needed if you plan to add/remove rows with the pager enabled.
        removeRows: false,
    
        // output string - default is '{page}/{totalPages}';
        // possible variables: {page}, {totalPages}, {filteredPages}, {startRow}, {endRow}, {filteredRows} and {totalRows}
        output: '{startRow} - {endRow} / {filteredRows} ({totalRows})'
    
      });
        });
        
</script>          
        
