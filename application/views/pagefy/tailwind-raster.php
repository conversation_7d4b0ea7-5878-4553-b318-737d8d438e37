
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Raster grid examples</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <link rel="stylesheet" href="https://rsms.me/raster/raster2.css?v=20">
   
    <!-- tailwindcss CSS -->
  <!--<link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet">-->

  <!-- awesome font -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">

  <!-- htmx -->
  <script src="https://unpkg.com/htmx.org@1.3.3"></script>
  <script src="https://unpkg.com/htmx.org/dist/ext/client-side-templates.js"></script>
  <script src="https://unpkg.com/hyperscript.org@0.0.9"></script>
  <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.x.x/dist/alpine.min.js" defer></script>
 
   <!--jquery 3.6 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"
    integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="
    crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jeditable.js/2.0.19/jquery.jeditable.min.js"
    integrity="sha512-Jn+modod+CgT0sdSdiL5Zr74JeTDTR2DGzeSRG1oUVnZjZFnkdSPOFUiwAr/St/lIvYEyBaZ6F46vJE4fbHg6A=="
    crossorigin="anonymous"></script>

  <link rel="stylesheet" href="https://nextapps-de.github.io/winbox/dist/css/winbox.min.css">
  <!--<link rel="stylesheet" href="https://nextapps-de.github.io/winbox/dist/css/themes/modern.min.css">-->
  <!--<link rel="stylesheet" href="https://nextapps-de.github.io/winbox/dist/css/themes/white.min.css">-->
  <script src="https://rawcdn.githack.com/nextapps-de/winbox/0.2.0/dist/winbox.bundle.js"></script>

  <!-- Tabesorter  *///-->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.2/js/jquery.tablesorter.combined.min.js"></script>
 
  <!--        /* Livequery   *///-->
  <script src="https://office.vallery.com.br/jquery/livequery/jquery.livequery.js"></script>
  
  <!-- mustache -->
  <script src="https://cdn.jsdelivr.net/npm/mustache@4.2.0/mustache.min.js"></script>

   
   
   
  <style>

:root {
  --fontSize: calc(100vh / 95);
  --margin: 6rem;
}
@media only screen and (max-width: 1000px) { :root {
  --fontSize: calc(100vmin / 70);
}}

html {
  background: #111;
  background-image: linear-gradient(4deg, #ffffff, #141414);
  background-size: 100%;
  background-attachment: fixed;
  /*color: rgba(252,252,252,0.99);*/
}
@media print {
  html { background: white }
}
body { padding: 0 0 3rem 0 }

footer { margin: var(--margin) }

.flex-h { flex-wrap: wrap; justify-content: center }
.black { color: black; }

.noise {
  background-image: url(noise512.png);
  background-size: 256px;
  opacity: 0.02;
  pointer-events: none;
  position: fixed;
  top:0;
  left:0;
  right:0;
  bottom:0;
}
@media print {
  .noise { display:none; }
}

/*.poster {*/
/*  width: 60rem;*/
/*  height: 84rem;*/
/*  background: #555;*/
/*  margin: var(--margin) calc(var(--margin) / 2);*/
/*  box-sizing: border-box;*/
/*  flex: 0 0 auto;*/
/*  box-shadow: 0px 2px 8px black;*/
/*}*/
/*@media print {*/
/*  .poster {*/
/*    box-shadow: none;*/
/*    page-break-before: always;*/
/*    page-break-after: always;*/
/*    page-break-inside: avoid;*/
/*    break-before: always;*/
/*    break-after: always;*/
/*    break-inside: avoid-page;*/
/*  }*/
/*}*/

.poster1 {
  background: #FEED01;
  background-image: linear-gradient(175deg, #FEED01, #F9E010);
  color: rgba(0,0,0,0.9);
}

.poster2 {
  background: #555;
  background-image:
    linear-gradient(20deg, rgba(255, 0, 0, 0.02), rgba(0, 255, 0, 0.03)),
    linear-gradient(175deg, #555, #555960);
    color: rgba(252,252,252,0.98);
}

html.white-bg {
  background: white;
  color:rgba(0,0,0,0.99);
}
html.white-bg .poster {
  box-shadow: 0px 0.5rem 1.2rem rgba(0,0,0, 0.1);
}

  </style>
</head>
<body>
  <div class=noise></div>
  
  <div class="flex-h">


    <r-grid columns=8 class="poster poster1 padding1">
      <r-cell span=row><br></r-cell>
<!--      <r-cell span=1-4>-->
<!--        <h1 class="large right" style="font-weight:500;padding-right:4rem">-->
<!--          International<br>-->
<!--          Typographic-->
<!--        </h1>-->
<!--      </r-cell>-->
<!--      <r-cell span=5-7>-->
<!--        <h1 class=large style="font-weight:500">-->
<!--          <br>-->
<!--          Style-->
<!--        </h1>-->
<!--      </r-cell>-->
<!--      <r-cell span=3+2 class="h5 right" style="padding-right:4rem">-->
<!--        Aka Swiss Style-->
<!--      </r-cell>-->
<!--      <r-cell span=5-7>-->
<!--        <p>-->
<!--The International Typographic Style, also known as the Swiss Style, is a graphic design style that emerged in Russia, the Netherlands, and Germany in the 1920s and was developed by designers in Switzerland during the 1950s.-->
<!--        </p>-->
<!--        <p>-->
<!--          The International Typographic Style has had profound influence on graphic design as a part of the modernist movement, impacting many design-related fields including architecture and art. It emphasizes cleanness, readability, and objectivity.-->
<!--        </p>-->
<!--      </r-cell>-->
<!--      <r-cell span=5.. style="font-size:4rem;font-weight:200;margin-left:-0.2em">-->
<!--        &darr;-->
<!--      </r-cell>-->
<!--      <r-cell span=1-4>-->
<!--        <h1 class=xxxlarge style="font-weight:800;line-height:0.9">-->
<!--          1920<br>-->
<!--          1959-->
          <!-- <span style="font-weight:300;letter-spacing:0.3rem">1959</span> -->
<!--        </h1>-->
<!--      </r-cell>-->
      
       <r-cell span=row  style="font-size:1.2rem;font-weight:600;margin-left:-0.2em"><br>
       <?=$content?></r-cell>
      <!--<r-cell span=2 class=h3 style="line-height:1.5;padding-top:0.7rem;font-weight:500">-->
      <!--  the solution to the design problem should emerge from its content-->
      <!--</r-cell>-->

      <!--<r-cell span=1-2 class=xsmall style="height:2rem">-->
      <!--  Akzidenz Grotesk<br>-->
      <!--  Univers<br>-->
      <!--  Neue Haas Grotesk<br>-->
      <!--  Helvetica-->
      <!--</r-cell>-->
      <!--<r-cell span=2 class=xsmall style="height:2rem">-->
      <!--  Basel School of Design<br>-->
      <!--  Kunstgewerbeschule Zürich<br>-->
      <!--  McGraw-Hill<br>-->
      <!--  MIT-->
      <!--</r-cell>-->
      <!--<r-cell span=2 class=xsmall style="height:2rem">-->
      <!--  Ernst Keller<br>-->
      <!--  Josef Müller-Brockmann<br>-->
      <!--  Rudolph de Harak<br>-->
      <!--  Jacqueline Casey-->
      <!--</r-cell>-->
      <!--<r-cell span=2 class=xsmall style="height:2rem">-->
      <!--  Universal form of graphic expression through objective<br>-->
      <!--  and impersonal presentation.-->
      <!--</r-cell>-->

    </r-grid>

    <!--<r-grid columns=4 class="poster poster1 padding1">-->
    <!-- <?=$content?>-->
    <!--</r-grid>-->


  </div>

  <!--<footer>-->
  <!--  <div style="margin-left:-0.15rem">-->
  <!--    <a href="javascript:document.documentElement.classList.toggle('white-bg')">&uarr; Toggle background</a><br>-->
      <!--<a href="./">&larr; Back to examples</a>-->
  <!--  </div>-->
  <!--</footer>-->



  
   <script src="/cdn/explore/jeditable.js"></script>
  
   <script>

     
         $('.winbox-iframe').livequery('click', function(event) 
         {
            var address = $(this).attr('rel');
            var title = $(this).attr('title') + ' - ' + address;
            var color   = $(this).attr('color');
            var width   = $(this).attr('width');
            var height  = $(this).attr('height');
            
            var winbox = new WinBox(
            {
                title: title,
                root: document.body,
                //     // html: "<h1>Lorem Ipsum</h1>"
                // html: '<iframe src="..." allow="..." sandbox="allow-scripts" referrerpolicy="..."></iframe>',
                // html: '<iframe  src="https://web.whatsapp.com/"  sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>',
                url: address,
                background: color,
                border: 4,
                class: "iframe",
                x: "center",
                y: "center",
                width: width,
                height: height
                // top: "0%",
                // right: "0%",
                // bottom: "0%",
                // left: "0%",
               
            });
          
        }); 
        
     
</script>

<script>
  $('.table').livequery(function() {
      $(this).tablesorter(
      {
      
       theme : "bootstrap",
       widthFixed: false,

        // widget code contained in the jquery.tablesorter.widgets.js file
        // use the zebra stripe widget if you plan on hiding any rows (filter widget)
        // the uitheme widget is NOT REQUIRED!
        // widgets : [ "filter", "columns", "zebra" ],
        widgets : [ "columns" ],
    
        widgetOptions : {
          // using the default zebra striping class name, so it actually isn't included in the theme variable above
          // this is ONLY needed for bootstrap theming if you are using the filter widget, because rows are hidden
          zebra : ["even", "odd"],
    
          // class names added to columns when sorted
          columns: [ "primary", "secondary", "tertiary" ],
    
          // reset filters button
          filter_reset : ".reset",
    
          // extra css class name (string or array) added to the filter element (input or select)
          filter_cssFilter: [
            'form-control',
            'form-control',
           
          ]
    
    }
    }).tablesorterPager({

        // target the pager markup - see the HTML block below
        container: $(".ts-pager"),
    
        // target the pager page select dropdown - choose a page
        cssGoto  : ".pagenum",
    
        // remove rows from the table to speed up the sort of large tables.
        // setting this to false, only hides the non-visible rows; needed if you plan to add/remove rows with the pager enabled.
        removeRows: false,
    
        // output string - default is '{page}/{totalPages}';
        // possible variables: {page}, {totalPages}, {filteredPages}, {startRow}, {endRow}, {filteredRows} and {totalRows}
        output: '{startRow} - {endRow} / {filteredRows} ({totalRows})'
    
      });
        });
        
</script>          
        


</body>
</html>
