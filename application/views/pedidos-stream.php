<?php header('Access-Control-Allow-Origin: *'); ?>
<!DOCTYPE html>
<html x-data="data()" lang="en">

    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"> 
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Dashboard</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap"
              rel="stylesheet" />

        <!-- tailwindcss CSS -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.0.2/tailwind.min.css" rel="stylesheet">

        <!-- awesome font -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">

        <!-- Favicon -->
        <!--<script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.x.x/dist/alpine.min.js" defer></script>//-->

        <!-- twitter-bootstrap//-->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.0.1/css/bootstrap-grid.min.css" integrity="sha512-Aa+z1qgIG+Hv4H2W3EMl3btnnwTQRA47ZiSecYSkWavHUkBF2aPOIIvlvjLCsjapW1IfsGrEO3FU693ReouVTA==" crossorigin="anonymous" referrerpolicy="no-referrer" />


        <link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/prelude.css">
        <link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/rainbow.css">
        <link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/progress.css">
    </head>

    <body class="bg-gray-900" >

        <!-- This example requires Tailwind CSS v2.0+ -->
        <div class="flex flex-col">
            <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                    <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Pedido
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Data
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Segmento
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200 results">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900 pedidoID"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500 pedidoData"></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500 pedidoSegmento"></div>
                                    </td>
                                </tr>

                                <!-- More people... -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- htmx -->
        <script src="https://unpkg.com/htmx.org@1.3.3"></script>
        <script src="https://unpkg.com/htmx.org/dist/ext/client-side-templates.js"></script>
        <script src="https://unpkg.com/hyperscript.org@0.0.9"></script>

        <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js"></script>
        <script type="text/javascript" src="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/js/rainbow.min.js"></script>
        <script type="text/javascript" src="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/src/jquery-asPieProgress.js"></script>
        <script>
            var urlToChangeStream = 'https://api-v1.vallery.com.br/v1/Pedidos/change-stream?_format=event-stream';
            var src = new EventSource(urlToChangeStream);

            src.addEventListener('data', function(msg) {

                var data = JSON.parse(msg.data);

                if ( data.type == 'create' )
                {
                    var html = '<tr><td class="px-6 py-4 whitespace-nowrap”><div class="flex items-center"> <div class="ml-4"> <div class="text-sm font-medium text-gray-900 pedidoID">'+data.data.id+'</div> </div> </div> </td> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-500 pedidoData">'+data.data.pedidoDataEmisao+'</div> </td> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-500 pedidoSegmento">'+data.data.segmentoPOID+'</div> </td> </tr>';
                    $('#results').prepend(html);
                }
            });
        </script>

    </body>

</html>