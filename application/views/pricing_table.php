<!-- application/views/pricing_table.php -->
<table class="min-w-full table-auto border-collapse border border-gray-300">
    <thead>
        <tr class="bg-gray-800 text-white">
            <th class="px-4 py-2 border border-gray-300 text-left">Nome</th>
            <th class="px-4 py-2 border border-gray-300 text-right">Valor</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($data as $key => $value): ?>
            <tr class="odd:bg-gray-100 even:bg-gray-200">
                <th class="px-4 py-2 border border-gray-300 font-medium text-gray-700"><?= htmlspecialchars($key) ?></th>
                <td class="px-4 py-2 border border-gray-300 text-blue-600 text-right">
                    <?php if (is_array($value)): ?>
                        <?= View::factory('pricing_table', ['data' => $value]) ?> <!-- Recursive call for nested arrays -->
                    <?php else: ?>
                        <?php if(is_numeric($value) and $value>0): ?>
                        <?=number_format((float)$value,2,",",".") ?>
                    <?php else: ?>
                        <?= htmlspecialchars($value) ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </td>
            </tr>
        <?php endforeach; ?>
    </tbody>
</table>
