<!DOCTYPE html>
<html lang="pt-br">

<head>
  <title>Rolemak</title>

  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <!-- tailwindcss CSS -->
  <link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet">

  <!-- awesome font -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">

  <!-- htmx -->
  <script src="https://unpkg.com/htmx.org@1.3.3"></script>
  <script src="https://unpkg.com/htmx.org/dist/ext/client-side-templates.js"></script>
  <script src="https://unpkg.com/hyperscript.org@0.0.9"></script>
  <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.x.x/dist/alpine.min.js" defer></script>
 
   <!--jquery 3.6 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"
    integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="
    crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jeditable.js/2.0.19/jquery.jeditable.min.js"
    integrity="sha512-Jn+modod+CgT0sdSdiL5Zr74JeTDTR2DGzeSRG1oUVnZjZFnkdSPOFUiwAr/St/lIvYEyBaZ6F46vJE4fbHg6A=="
    crossorigin="anonymous"></script>

  <link rel="stylesheet" href="https://nextapps-de.github.io/winbox/dist/css/winbox.min.css">
  <link rel="stylesheet" href="https://nextapps-de.github.io/winbox/dist/css/themes/modern.min.css">
  <link rel="stylesheet" href="https://nextapps-de.github.io/winbox/dist/css/themes/white.min.css">
  <script src="https://rawcdn.githack.com/nextapps-de/winbox/0.2.0/dist/winbox.bundle.js"></script>

  <!-- jquery 3.6 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="   crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jeditable.js/2.0.19/jquery.jeditable.min.js" integrity="sha512-Jn+modod+CgT0sdSdiL5Zr74JeTDTR2DGzeSRG1oUVnZjZFnkdSPOFUiwAr/St/lIvYEyBaZ6F46vJE4fbHg6A=="   <!--  crossorigin="anonymous"></script>

  <!-- Tabesorter  *///-->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.2/js/jquery.tablesorter.combined.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.3/js/extras/jquery.tablesorter.pager.min.js"></script>
  
 
  <!--        /* Livequery   *///-->
  <script src="https://legacy.vallery.com.br//K/media/jquery/livequery/jquery.livequery.js"></script>
  
  <!-- mustache -->
  <script src="https://cdn.jsdelivr.net/npm/mustache@4.2.0/mustache.min.js"></script>


</head>

<body>

<div class="flex flex-col">
  <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
    <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
      <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
               <?php   
          $counter=0; 
          foreach($data as $key => $val) {  
            $counter++;
                  if($counter>1) continue;
            ?>
              <tr>
                 <td>ooo</td>
                  <?php 
                  
                  foreach($val as $k => $v) 
                  { 
                  ?>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><?=$k?></th>
          
                  <?php 
            
                  }  ?>
              </tr>
          <?php } ?>
          
            
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr>
               <?php foreach($data as $key => $val) { ?>
              <tr>
                 <td><?=$key?></td>
                  <?php 
                  $sub = 0; 
                  foreach($val as $k => $v) { 
                    $sub+=$v;
                    ?>
                  
                    
                      <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      <?=number_format($v,0,',','.')?>
                    </div>
                    <!--<div class="text-sm text-gray-500">-->
                    <!--  <EMAIL>-->
                    <!--</div>-->
                  </div>
                </div>
              </td>
              
                  <?php } ?>
                   <td><?=number_format($sub,0,',','.')?></td>
              </tr>
          <?php } ?>
          
            </tr>

            <!-- More people... -->
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

    <!-- screen -->
    <div id="screen" class="h-full md:h-screen ">
 
        <table class="table">
          <thead>
          <?php   
          $counter=0; 
          foreach($data as $key => $val) {  
            $counter++;
                  if($counter>1) continue;
            ?>
              <tr>
                 <td>ooo</td>
                  <?php 
                
                  foreach($val as $k => $v) 
                  { 
                  ?>
                      <th><?=$k?></th>
                  <?php 
            
                  }  ?>
              </tr>
          <?php } ?>
          </thead>
          
          <tbody>
          <?php foreach($data as $key => $val) { ?>
              <tr>
                 <td><?=$key?></td>
                  <?php 
                  $sub = 0; 
                  foreach($val as $k => $v) 
                  { 
                    $sub+=$v;
                  ?>
                      <td><?=number_format($v,0,',','.')?></td>
                  <?php } ?>
                      <td><?=number_format($sub,0,',','.')?></td>
              </tr>
          <?php } ?>
          </tbody>
        </table>
              
                
          </div>
  
   <script src="/cdn/explore/jeditable.js"></script>
  
   <script>

     
         $('.winbox-iframe').livequery('click', function(event) 
         {
            var address = $(this).attr('rel');
            var title = $(this).attr('title') + ' - ' + address;
            var color   = $(this).attr('color');
            var width   = $(this).attr('width');
            var height  = $(this).attr('height');
            
            var winbox = new WinBox(
            {
                title: title,
                root: document.body,
                //     // html: "<h1>Lorem Ipsum</h1>"
                // html: '<iframe src="..." allow="..." sandbox="allow-scripts" referrerpolicy="..."></iframe>',
                // html: '<iframe  src="https://web.whatsapp.com/"  sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>',
                url: address,
                background: color,
                border: 4,
                class: "iframe",
                x: "center",
                y: "center",
                width: width,
                height: height
                // top: "0%",
                // right: "0%",
                // bottom: "0%",
                // left: "0%",
               
            });
          
        }); 
        
     
</script>

<script>
  $('.table').livequery(function() {
      $(this).tablesorter(
      {
      
       theme : "bootstrap",
       widthFixed: false,

        // widget code contained in the jquery.tablesorter.widgets.js file
        // use the zebra stripe widget if you plan on hiding any rows (filter widget)
        // the uitheme widget is NOT REQUIRED!
        // widgets : [ "filter", "columns", "zebra" ],
        widgets : [ "columns" ],
    
        widgetOptions : {
          // using the default zebra striping class name, so it actually isn't included in the theme variable above
          // this is ONLY needed for bootstrap theming if you are using the filter widget, because rows are hidden
          zebra : ["even", "odd"],
    
          // class names added to columns when sorted
          columns: [ "primary", "secondary", "tertiary" ],
    
          // reset filters button
          filter_reset : ".reset",
    
          // extra css class name (string or array) added to the filter element (input or select)
          filter_cssFilter: [
            'form-control',
            'form-control',
           
          ]
    
    }
    }).tablesorterPager({

        // target the pager markup - see the HTML block below
        container: $(".ts-pager"),
    
        // target the pager page select dropdown - choose a page
        cssGoto  : ".pagenum",
    
        // remove rows from the table to speed up the sort of large tables.
        // setting this to false, only hides the non-visible rows; needed if you plan to add/remove rows with the pager enabled.
        removeRows: false,
    
        // output string - default is '{page}/{totalPages}';
        // possible variables: {page}, {totalPages}, {filteredPages}, {startRow}, {endRow}, {filteredRows} and {totalRows}
        output: '{startRow} - {endRow} / {filteredRows} ({totalRows})'
    
      });
        });
        
</script>          
        
    
</body>

</html> / {filteredRows} ({totalRows})'
    
      });
        });
        
</script>          
        
    
</body>

</html>