<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Análise RFM - Métricas</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <span class="text-xl font-bold">Métricas</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mx-auto p-6">
        <h2 class="text-2xl font-bold mb-6">Análise RFM - Últimos <?php echo htmlspecialchars($periodo, ENT_QUOTES, 'UTF-8'); ?> meses</h2>
        
        <div class="overflow-x-auto shadow-md sm:rounded-lg">
            <table class="w-full text-sm text-left text-gray-500">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3">
                            #
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(1)">
                            ID Cliente ↕
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(1)">
                            Nome ↕
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(2)">
                            Score Recência ↕
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(3)">
                            Score Frequência ↕
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(4)">
                            Score Monetário ↕
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(5)">
                            Score Total ↕
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(6)">
                            Segmento ↕
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php $counter = 1; ?>
                    <?php foreach ($scores as $score): ?>
                    <tr class="bg-white border-b hover:bg-gray-50">
                        <td class="px-6 py-4"><?php echo $counter++; ?></td>
                        <td class="px-6 py-4"><?php echo htmlspecialchars($score['cliente_id'], ENT_QUOTES, 'UTF-8'); ?></td>
                        <td class="px-6 py-4"><?php echo htmlspecialchars($score['cliente_nome'], ENT_QUOTES, 'UTF-8'); ?></td>
                        <td class="px-6 py-4"><?php echo $score['recency_score']; ?></td>
                        <td class="px-6 py-4"><?php echo $score['frequency_score']; ?></td>
                        <td class="px-6 py-4"><?php echo $score['monetary_score']; ?></td>
                        <td class="px-6 py-4"><?php echo $score['score_total']; ?></td>
                        <td class="px-6 py-4"><?php echo htmlspecialchars($score['segmento'], ENT_QUOTES, 'UTF-8'); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <div class="mt-6">
            <a href="/metrics/rfm/export" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Exportar para CSV
            </a>
        </div>

        <script>
        function sortTable(n) {
            var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
            table = document.querySelector("table");
            switching = true;
            dir = "asc";
            
            while (switching) {
                switching = false;
                rows = table.rows;
                
                for (i = 1; i < (rows.length - 1); i++) {
                    shouldSwitch = false;
                    x = rows[i].getElementsByTagName("TD")[n];
                    y = rows[i + 1].getElementsByTagName("TD")[n];
                    
                    // Skip sorting for the counter column (n === 0)
                    if (n === 0) continue;
                    
                    let xValue = isNaN(x.innerHTML) ? x.innerHTML.toLowerCase() : parseFloat(x.innerHTML);
                    let yValue = isNaN(y.innerHTML) ? y.innerHTML.toLowerCase() : parseFloat(y.innerHTML);
                    
                    if (dir == "asc") {
                        if (xValue > yValue) {
                            shouldSwitch = true;
                            break;
                        }
                    } else if (dir == "desc") {
                        if (xValue < yValue) {
                            shouldSwitch = true;
                            break;
                        }
                    }
                }
                
                if (shouldSwitch) {
                    rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                    switching = true;
                    switchcount++;
                } else {
                    if (switchcount == 0 && dir == "asc") {
                        dir = "desc";
                        switching = true;
                    }
                }
            }
        }
        </script>
    </div>

    <footer class="bg-white shadow-lg mt-8">
        <div class="max-w-7xl mx-auto py-4 px-4">
            <div class="flex justify-between items-center">
                <div class="text-gray-500 text-sm">
                    © <?php echo date('Y'); ?> Métricas RFM
                </div>
                <div class="text-gray-500 text-sm">
                    Versão 1.0
                </div>
            </div>
        </div>
    </footer>
</body>
</html>