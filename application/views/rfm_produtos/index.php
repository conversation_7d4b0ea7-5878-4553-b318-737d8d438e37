<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Análise RFM de Produtos</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-6">
        <h2 class="text-2xl font-bold mb-6">Análise RFM de Produtos - Últimos <?php echo htmlspecialchars($periodo, ENT_QUOTES, 'UTF-8'); ?> meses</h2>
        
        <div class="overflow-x-auto shadow-md sm:rounded-lg mb-6">
            <table class="w-full text-sm text-left text-gray-500">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(0)">#</th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(1)">ID ↕</th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(2)">Modelo ↕</th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(3)">Marca ↕</th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(4)">Nome ↕</th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(5)">Subtotal ↕</th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(6)">Recência ↕</th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(7)">Frequência ↕</th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(8)">Monetário ↕</th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(9)">Total ↕</th>
                        <th scope="col" class="px-6 py-3 cursor-pointer hover:bg-gray-100" onclick="sortTable(10)">Segmento ↕</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $contador = 1; ?>
                    <?php foreach ($scores as $score): ?>
                    <tr class="bg-white border-b hover:bg-gray-50">
                        <td class="px-6 py-4 text-gray-400"><?php echo $contador++; ?></td>
                        <td class="px-6 py-4"><?php echo htmlspecialchars($score['produto_id'], ENT_QUOTES, 'UTF-8'); ?></td>
                        <td class="px-6 py-4"><?php echo htmlspecialchars($score['produto_modelo'], ENT_QUOTES, 'UTF-8'); ?></td>
                        <td class="px-6 py-4"><?php echo htmlspecialchars($score['produto_marca'], ENT_QUOTES, 'UTF-8'); ?></td>
                        <td class="px-6 py-4"><?php echo htmlspecialchars($score['produto_nome'], ENT_QUOTES, 'UTF-8'); ?></td>
                        <td class="px-6 py-4">R$ <?php echo number_format($score['subtotal'], 2, ',', '.'); ?></td>
                        <td class="px-6 py-4"><?php echo $score['recency_score']; ?></td>
                        <td class="px-6 py-4"><?php echo $score['frequency_score']; ?></td>
                        <td class="px-6 py-4"><?php echo $score['monetary_score']; ?></td>
                        <td class="px-6 py-4"><?php echo $score['score_total']; ?></td>
                        <td class="px-6 py-4 <?php 
                            switch ($score['segmento']) {
                                case 'Estrelas':
                                    echo 'text-yellow-600 font-bold';
                                    break;
                                case 'Produtos Principais':
                                    echo 'text-green-600 font-bold';
                                    break;
                                case 'Produtos Regulares':
                                    echo 'text-blue-600';
                                    break;
                                case 'Produtos em Risco':
                                    echo 'text-orange-600';
                                    break;
                                default:
                                    echo 'text-red-600';
                            }
                        ?>">
                            <?php echo htmlspecialchars($score['segmento'], ENT_QUOTES, 'UTF-8'); ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <div class="flex justify-between items-center">
            <div class="flex gap-4">
                <a href="/metrics/rfm_produtos/export" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Exportar para CSV
                </a>
            </div>
            <div class="text-sm text-gray-500">
                Total de produtos: <?php echo count($scores); ?>
            </div>
        </div>
    </div>

    <script>
    function sortTable(n) {
        var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
        table = document.querySelector("table");
        switching = true;
        dir = "asc";
        
        while (switching) {
            switching = false;
            rows = table.rows;
            
            for (i = 1; i < (rows.length - 1); i++) {
                shouldSwitch = false;
                x = rows[i].getElementsByTagName("TD")[n];
                y = rows[i + 1].getElementsByTagName("TD")[n];
                
                let xValue = isNaN(x.innerHTML) ? x.innerHTML.toLowerCase() : parseFloat(x.innerHTML);
                let yValue = isNaN(y.innerHTML) ? y.innerHTML.toLowerCase() : parseFloat(y.innerHTML);
                
                if (dir == "asc") {
                    if (xValue > yValue) {
                        shouldSwitch = true;
                        break;
                    }
                } else if (dir == "desc") {
                    if (xValue < yValue) {
                        shouldSwitch = true;
                        break;
                    }
                }
            }
            
            if (shouldSwitch) {
                rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                switching = true;
                switchcount++;
            } else {
                if (switchcount == 0 && dir == "asc") {
                    dir = "desc";
                    switching = true;
                }
            }
        }
    }
    </script>
</body>
</html>