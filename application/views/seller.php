<div class="row stats ">
        <div class="row">
            <div class="col-3">
                <figure class="highcharts-figure">
                  <div id="container-day<?=$addr?>"></div>
                </figure>
             </div>    
            
            <div class="col-3">
                <figure class="highcharts-figure">
                  <div id="container-month<?=$addr?>"></div>
                </figure>
             </div> 
            
              <div class="col-3">
                <figure class="highcharts-figure">
                  <div id="container-year<?=$addr?>"></div>
                </figure>
             </div>    

             <div class="col-3">    
                
                <figure class="highcharts-figure-customers">
                    <div id="container-customers<?=$addr?>"></div>
                    <!--<p class="highcharts-description">-->
                    <!--    Pie charts are very popular for showing a compact overview of a-->
                    <!--    composition or comparison. While they can be harder to read than-->
                    <!--    column charts, they remain a popular choice for small datasets.-->
                    <!--</p>-->
                </figure>
            </div>
    
             <div class="col-5">    
                
                <figure class="highcharts-figure-bubble">
                    <div id="container-bubble<?=$addr?>"></div>
                    <!--<p class="highcharts-description">-->
                    <!--    Pie charts are very popular for showing a compact overview of a-->
                    <!--    composition or comparison. While they can be harder to read than-->
                    <!--    column charts, they remain a popular choice for small datasets.-->
                    <!--</p>-->
                </figure>
            </div>
            
            
            
        </div>
      </div>

<script>


    /**
     * In the chart render event, add icons on top of the circular shapes
     */
    function renderIcons() {

        // Move icon
        if (!this.series[0].icon) {
            this.series[0].icon = this.renderer.path(['M', -8, 0, 'L', 8, 0, 'M', 0, -8, 'L', 8, 0, 0, 8])
                .attr({
                stroke: '#303030',
                'stroke-linecap': 'round',
                'stroke-linejoin': 'round',
                'stroke-width': 2,
                zIndex: 10
            })
                .add(this.series[2].group);
        }
        this.series[0].icon.translate(
            this.chartWidth / 2 - 10,
            this.plotHeight / 2 - this.series[0].points[0].shapeArgs.innerR -
            (this.series[0].points[0].shapeArgs.r - this.series[0].points[0].shapeArgs.innerR) / 2
        );

        // Exercise icon
        if (!this.series[1].icon) {
            this.series[1].icon = this.renderer.path(
                ['M', -8, 0, 'L', 8, 0, 'M', 0, -8, 'L', 8, 0, 0, 8,
                 'M', 8, -8, 'L', 16, 0, 8, 8]
            )
                .attr({
                stroke: '#ffffff',
                'stroke-linecap': 'round',
                'stroke-linejoin': 'round',
                'stroke-width': 2,
                zIndex: 10
            })
                .add(this.series[2].group);
        }
        this.series[1].icon.translate(
            this.chartWidth / 2 - 10,
            this.plotHeight / 2 - this.series[1].points[0].shapeArgs.innerR -
            (this.series[1].points[0].shapeArgs.r - this.series[1].points[0].shapeArgs.innerR) / 2
        );

        // Stand icon
        if (!this.series[2].icon) {
            this.series[2].icon = this.renderer.path(['M', 0, 8, 'L', 0, -8, 'M', -8, 0, 'L', 0, -8, 8, 0])
                .attr({
                stroke: '#303030',
                'stroke-linecap': 'round',
                'stroke-linejoin': 'round',
                'stroke-width': 2,
                zIndex: 10
            })
                .add(this.series[2].group);
        }

        this.series[2].icon.translate(
            this.chartWidth / 2 - 10,
            this.plotHeight / 2 - this.series[2].points[0].shapeArgs.innerR -
            (this.series[2].points[0].shapeArgs.r - this.series[2].points[0].shapeArgs.innerR) / 2
        );
    }

    function charts(btn)
    {
        var chart_day= Highcharts.chart('container-day<?=$addr?>', {

            chart: {
                type: 'solidgauge',
                height: '110%',
                events: {
                    render: renderIcons
                }
            },
            title: {
                text: 'Atividades do Dia',
                style: {
                    fontSize: '12px'
                }
            },
            subtitle: {
                text: 'Vendas, Tickets, Logs'
            },
            tooltip: {
                borderWidth: 0,
                backgroundColor: 'none',
                shadow: false,
                style: {
                    fontSize: '12px'
                },
                valueSuffix: '%',
                pointFormat: '{series.name}<br><span style="font-size:2em; color: {point.color}; font-weight: bold">{point.y} </span>',
                positioner: function (labelWidth) {
                    return {
                        x: (this.chart.chartWidth - labelWidth) / 2,
                        y: (this.chart.plotHeight / 2) + 15
                    };
                }
            },
            pane: {
                startAngle: 0,
                endAngle: 360,
                background: [{ // Track for Move
                    outerRadius: '112%',
                    innerRadius: '88%',
                    backgroundColor: Highcharts.color(Highcharts.getOptions().colors[2])
                    .setOpacity(0.2)
                    .get(),
                    borderWidth: 0
                }, { // Track for Exercise
                    outerRadius: '87%',
                    innerRadius: '63%',
                    backgroundColor: Highcharts.color(Highcharts.getOptions().colors[1])
                    .setOpacity(0.3)
                    .get(),
                    borderWidth: 0
                }, { // Track for Stand
                    outerRadius: '62%',
                    innerRadius: '38%',
                    backgroundColor: Highcharts.color(Highcharts.getOptions().colors[0])
                    .setOpacity(0.5)
                    .get(),
                    borderWidth: 0
                }]
            },
            yAxis: {
                min: 0,
                max: 100,
                lineWidth: 0,
                tickPositions: [],
                allowDecimals:false
            },
            plotOptions: {
                solidgauge: {
                    dataLabels: {
                        enabled: false
                    },
                    linecap: 'round',
                    stickyTracking: false,
                    rounded: true
                }
            },
            series: [
                {
                    name: 'Vendas',
                    data: 
                    [           {
                        color: Highcharts.getOptions().colors[2],
                        radius: '112%',
                        innerRadius: '88%',
                        y: 0,
                        x: 0,
                    }]
                }, 
                {
                    name: 'Tickets',
                    data: [{
                        color: Highcharts.getOptions().colors[1],
                        radius: '87%',
                        innerRadius: '63%',
                        y: 0
                    }
                          ]}, 
                {
                    name: 'Logs',
                    data: [{
                        color: Highcharts.getOptions().colors[0],
                        radius: '62%',
                        innerRadius: '38%',
                        y: 0
                    }]
                }
            ]
        });

        var chart_month= Highcharts.chart('container-month<?=$addr?>', {

            chart: {
                type: 'solidgauge',
                height: '110%',
                events: {
                    render: renderIcons
                }
            },
            title: {
                text: 'Atividades do Mês',
                style: {
                    fontSize: '12px'
                }
            },
            subtitle: {
                text: 'Vendas, Tickets, Logs'
            },
            colors: ['blue', 'green', 'yellow'],
            tooltip: {
                borderWidth: 0,
                backgroundColor: 'none',
                shadow: false,
                style: {
                    fontSize: '12px'
                },
                valueSuffix: '%',
                pointFormat: '{series.name}<br><span style="font-size:2em; color: {point.color}; font-weight: bold">{point.y}</span>',
                positioner: function (labelWidth) {
                    return {
                        x: (this.chart.chartWidth - labelWidth) / 2,
                        y: (this.chart.plotHeight / 2) + 15
                    };
                }
            },
            pane: {
                startAngle: 0,
                endAngle: 360,
                background: [{ // Track for Move
                    outerRadius: '112%',
                    innerRadius: '88%',
                    backgroundColor: Highcharts.color(Highcharts.getOptions().colors[2])
                    .setOpacity(0.2)
                    .get(),
                    borderWidth: 0
                }, { // Track for Exercise
                    outerRadius: '87%',
                    innerRadius: '63%',
                    backgroundColor: Highcharts.color(Highcharts.getOptions().colors[1])
                    .setOpacity(0.3)
                    .get(),
                    borderWidth: 0
                }, { // Track for Stand
                    outerRadius: '62%',
                    innerRadius: '38%',
                    backgroundColor: Highcharts.color(Highcharts.getOptions().colors[0])
                    .setOpacity(0.5)
                    .get(),
                    borderWidth: 0
                }]
            },
            yAxis: {
                min: 0,
                max: 100,
                lineWidth: 0,
                tickPositions: [],
                allowDecimals:false
            },
            plotOptions: {
                solidgauge: {
                    dataLabels: {
                        enabled: false
                    },
                    linecap: 'round',
                    stickyTracking: false,
                    rounded: true
                }
            },
            series: [
                {
                    name: 'Vendas',
                    data: 
                    [           {
                        color: Highcharts.getOptions().colors[2],
                        radius: '112%',
                        innerRadius: '88%',
                        y: 0
                    }]
                }, 
                {
                    name: 'Tickets',
                    data: [{
                        color: Highcharts.getOptions().colors[1],
                        radius: '87%',
                        innerRadius: '63%',
                        y: 0
                    }
                          ]}, 
                {
                    name: 'Logs',
                    data: [{
                        color: Highcharts.getOptions().colors[0],
                        radius: '62%',
                        innerRadius: '38%',
                        y: 0
                    }]
                }
            ]
        });

        var chart_year= Highcharts.chart('container-year<?=$addr?>', {

            chart: {
                type: 'solidgauge',
                height: '110%',
                events: {
                    render: renderIcons
                }
            },
            title: {
                text: 'Atividades do Ano',
                style: {
                    fontSize: '12px'
                }
            },
            subtitle: {
                text: 'Vendas, Tickets, Logs'
            },
            colors: ['blue', 'green', 'yellow'],
            tooltip: {
                borderWidth: 0,
                backgroundColor: 'none',
                shadow: false,
                style: {
                    fontSize: '12px'
                },
                valueSuffix: '%',
                pointFormat: '{series.name}<br><span style="font-size:2em; color: {point.color}; font-weight: bold">{point.y}</span>',
                positioner: function (labelWidth) {
                    return {
                        x: (this.chart.chartWidth - labelWidth) / 2,
                        y: (this.chart.plotHeight / 2) + 15
                    };
                }
            },
            pane: {
                startAngle: 0,
                endAngle: 360,
                background: [{ // Track for Move
                    outerRadius: '112%',
                    innerRadius: '88%',
                    backgroundColor: Highcharts.color(Highcharts.getOptions().colors[2])
                    .setOpacity(0.2)
                    .get(),
                    borderWidth: 0
                }, { // Track for Exercise
                    outerRadius: '87%',
                    innerRadius: '63%',
                    backgroundColor: Highcharts.color(Highcharts.getOptions().colors[1])
                    .setOpacity(0.3)
                    .get(),
                    borderWidth: 0
                }, { // Track for Stand
                    outerRadius: '62%',
                    innerRadius: '38%',
                    backgroundColor: Highcharts.color(Highcharts.getOptions().colors[0])
                    .setOpacity(0.5)
                    .get(),
                    borderWidth: 0
                }]
            },
            yAxis: {
                min: 0,
                max: 100,
                lineWidth: 0,
                tickPositions: [],
                allowDecimals:false
            },
            plotOptions: {
                solidgauge: {
                    dataLabels: {
                        enabled: false
                    },
                    linecap: 'round',
                    stickyTracking: false,
                    rounded: true
                }
            },
            series: [
                {
                    name: 'Vendas',
                    data: 
                    [           {
                        color: Highcharts.getOptions().colors[2],
                        radius: '112%',
                        innerRadius: '88%',
                        y: 0
                    }]
                }, 
                {
                    name: 'Tickets',
                    data: [{
                        color: Highcharts.getOptions().colors[1],
                        radius: '87%',
                        innerRadius: '63%',
                        y: 0
                    }
                          ]}, 
                {
                    name: 'Logs',
                    data: [{
                        color: Highcharts.getOptions().colors[0],
                        radius: '62%',
                        innerRadius: '38%',
                        y: 0
                    }]
                }
            ]
        });


        Highcharts.setOptions({
            colors: ['#333', '#CB2326', '#DDDF00', '#24CBE5', '#64E572', '#FF9655', '#CB2326',      '#6AF9C4']

        });


        //if (request) { request.abort() }

        // var seller = $(btn).attr('rel');
        var address ='/prm/stats/index' +btn;
      
        request = $.ajax({
            url: address, //"/prm/stats/index/1/107/",
            method: "GET",
            dataType : 'JSON',
            success: function(data) {
                //$(".request-time").text(`${data.processingTimeMs} ms`);
                // $.each(data, function (k, value) {
                //     console.log(k,value);
                // });

                // chart.series[0].name="Vendas do dia";
                //chart_day.tooltip.update({pointFormat:'{series.name}<br><span style="font-size:2em; color: {point.color}; font-weight: bold">{point.y}</span>}'}),

                chart_day.series[0].data[0].update({x: data.vendas_dia, y: data.tx_vendas_dia, z: data.vendas_meta_dia    })
                chart_day.series[1].data[0].update({x: data.tickets , y: data.tx_ticket_dia, z: data.ticket_meta_dia   })
                chart_day.update({
                    tooltip: {
                        pointFormat: '{series.name}<br> <span style="font-size:2em; color: 000; font-weight: bold">{point.y}</span><br/>{point.x}/{point.z}',
                        valueSuffix: ' % ',
                        shared: true
                    },

                });

                chart_day.redraw();

                chart_month.series[0].data[0].update({x: data.vendas_mes, y: data.tx_vendas_mes, z:data.vendas_meta_mes })
                chart_month.series[1].data[0].update({x: data.tickets_mes, y: data.tx_ticket_mes,z:data.ticket_meta_mes    })
                chart_month.update({
                    tooltip: {
                        pointFormat: '{series.name}<br> <span style="font-size:2em; color: 000; font-weight: bold">{point.y}</span><br/>{point.x}/{point.z}',
                        valueSuffix: ' % ',
                        shared: true
                    },
                });
                chart_month.redraw();
                // chart_customers.series[0].data[0].update({x: 10, y: data.ativos90  })
                // chart_customers.series[0].data[1].update({x: 10, y: data.inativos  })
                // chart_customers.series[0].data[2].update({x: 10, y: data.inativados  })
                // chart_customers.redraw();


                chart_year.series[0].data[0].update({x: data.compras_ano, y: data.tx_vendas_ano, z:data.vendas_meta_ano })
                chart_year.update({
                    tooltip: {
                        pointFormat: '{series.name}<br> <span style="font-size:2em; color: 000; font-weight: bold">{point.y}</span><br/>{point.x}/{point.z}',
                        valueSuffix: ' % ',
                        shared: true
                    },
                });
                chart_year.redraw();




            },
            error: function(data) {
                console.log(data);
            }
        });
    }


    $(document).ready(function () {

        // stats
        var addr = '<?=$addr?>';
        // $(".stats-menu").on('click', function () {
        charts(addr)
            //console.log("charts");

        // });


    });


</script>    
