<!DOCTYPE html>
<html lang="pt-br">

<head>
  <title>SSW </title>

  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">


        <!-- awesome CSS -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">

        <!-- tailwindcss CSS -->
        <!--<link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet">-->

        <!-- tailwindcss CSS -->
        <script src="https://cdn.tailwindcss.com"></script>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.css" rel="stylesheet" />

        <!-- jquery 3.6 -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"
        integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="
        crossorigin="anonymous"></script>

        <!-- winbox bundle -->
        <script src="https://rawcdn.githack.com/nextapps-de/winbox/0.2.0/dist/winbox.bundle.js"></script>




        <!-- twitter-bootstrap//-->
        <!--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.0.1/css/bootstrap-grid.min.css" integrity="sha512-Aa+z1qgIG+Hv4H2W3EMl3btnnwTQRA47ZiSecYSkWavHUkBF2aPOIIvlvjLCsjapW1IfsGrEO3FU693ReouVTA==" crossorigin="anonymous" referrerpolicy="no-referrer" />-->




        <script src="https://cdnjs.cloudflare.com/ajax/libs/jeditable.js/2.0.19/jquery.jeditable.min.js" integrity="sha512-Jn+modod+CgT0sdSdiL5Zr74JeTDTR2DGzeSRG1oUVnZjZFnkdSPOFUiwAr/St/lIvYEyBaZ6F46vJE4fbHg6A==" crossorigin="anonymous"></script>

        <!--        /* Livequery   *///-->
        <!--<script src="https://office.vallery.com.br/jquery/livequery/jquery.livequery.js"></script>-->


        <!-- htmx -->
        <!--<script src="https://unpkg.com/htmx.org@1.8.6"></script>-->
        <script src="https://unpkg.com/htmx.org@1.6.0"></script>
        <script src="https://unpkg.com/htmx.org/dist/ext/client-side-templates.js"></script>
        <script src="https://unpkg.com/hyperscript.org@0.9.8"></script>
        <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.x.x/dist/alpine.min.js" defer></script>


        <!--<link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/prelude.css">-->
        <!--<link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/rainbow.css">-->
        <!--<link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/progress.css">-->

         <!-- mustache -->
        <script src="https://cdn.jsdelivr.net/npm/mustache@4.2.0/mustache.min.js"></script>

        <!-- Tabesorter  *///-->
        {{!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.2/js/jquery.tablesorter.combined.min.js"></script>--}}

        <!--Datatables-->
        <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/dt/jszip-2.5.0/dt-1.12.1/af-2.4.0/b-2.2.3/b-colvis-2.2.3/b-html5-2.2.3/b-print-2.2.3/cr-1.5.6/date-1.1.2/fc-4.1.0/fh-3.2.4/kt-2.7.0/r-2.3.0/rg-1.2.0/rr-1.2.8/sc-2.0.7/sb-1.3.4/sp-2.0.2/sl-1.4.0/sr-1.1.1/datatables.min.css"/>
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js"></script>
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js"></script>
        <script type="text/javascript" src="https://cdn.datatables.net/v/dt/jszip-2.5.0/dt-1.12.1/af-2.4.0/b-2.2.3/b-colvis-2.2.3/b-html5-2.2.3/b-print-2.2.3/cr-1.5.6/date-1.1.2/fc-4.1.0/fh-3.2.4/kt-2.7.0/r-2.3.0/rg-1.2.0/rr-1.2.8/sc-2.0.7/sb-1.3.4/sp-2.0.2/sl-1.4.0/sr-1.1.1/datatables.min.js"></script>

<!--<script src="//cdnjs.cloudflare.com/ajax/libs/numeral.js/2.0.6/numeral.min.js"></script>-->

<!--        /* JFrame  *///-->
<!--<script src="https://garage.pimentech.net/pimentech/js/jquery.jframe.js"></script>-->
<!--<script src="/K4/jframe.js"></script>-->
<!--        /* Jeditable  *///-->
<!--<script src="/K3/media/jquery/jeditable/jquery.jeditable.js"></script>-->
<!--        /* Tabesorter  *///-->
 <link rel="stylesheet" href="https://mottie.github.io/tablesorter/css/theme.blue.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.2/js/jquery.tablesorter.combined.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.3/js/extras/jquery.tablesorter.pager.min.js"></script>

<!--        /* Sparkline  *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>-->
<!--        /* HandleBars  *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.1.2/handlebars.min.js"></script>-->
<!--        /* unsemantic *///-->
<!--<script src="https://unsemantic.com/javascripts/application.js?1527127334"></script>-->
<!--        /* Moment.js *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>-->
<!--        /* imagesloaded *///-->
<!--<script src="https://unpkg.com/imagesloaded@4/imagesloaded.pkgd.min.js"></script>-->
<!--        /* bootstrap-select -->
<!--<script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.9/dist/js/bootstrap-select.min.js"></script>-->

 <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.js"></script>

</head>

<body>



<nav class="bg-white border-gray-200 dark:bg-gray-900">
  <div class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
    <a href="https://flowbite.com/" class="flex items-center space-x-3 rtl:space-x-reverse">

        <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">Daily</span>
    </a>


    <div class="flex md:order-2">
    <button type="button" data-collapse-toggle="navbar-search" aria-controls="navbar-search" aria-expanded="false" class="md:hidden text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 me-1">
      <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
      </svg>
      <span class="sr-only">Search</span>
    </button>
    <div class="relative hidden md:block">
      <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
        </svg>
        <span class="sr-only">Search icon</span>
      </div>
      <input type="text" id="search-navbar" class="block w-full p-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Search...">
    </div>
    <button data-collapse-toggle="navbar-search" type="button" class="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600" aria-controls="navbar-search" aria-expanded="false">
        <span class="sr-only">Open main menu</span>
        <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h15M1 7h15M1 13h15"/>
        </svg>
    </button>
  </div>
    <div class="items-center justify-between hidden w-full md:flex md:w-auto md:order-1" id="navbar-search">
      <div class="relative mt-3 md:hidden">
        <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
          <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
          </svg>
        </div>
        <input type="text" id="search-navbar" class="block w-full p-2 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Search...">
      </div>

    </div>



    <button data-collapse-toggle="navbar-default" type="button" class="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-green-500 rounded-lg md:hidden hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-200 dark:text-green-400 dark:hover:bg-gray-900 dark:focus:ring-green-600" aria-controls="navbar-default" aria-expanded="false">
        <span class="sr-only">Open main menu</span>
        <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h15M1 7h15M1 13h15"/>
        </svg>
    </button>


    <div class="hidden w-full md:block md:w-auto" id="navbar-default">
      <ul class="font-medium flex flex-col p-4 md:p-0 mt-4 border border-green-100 rounded-lg bg-green-50 md:flex-row md:space-x-8 rtl:space-x-reverse md:mt-0 md:border-0 md:bg-white dark:bg-green-800 md:dark:bg-gray-900 dark:border-green-700">
        <li>
          <a href="#" class="block py-2 px-3 text-white bg-blue-700 rounded md:bg-transparent md:text-blue-700 md:p-0 dark:text-white md:dark:text-blue-500" aria-current="page">Home</a>
        </li>
        <li>
            <button id="dropdownNavbarLink" data-dropdown-toggle="dropdownNavbar" class="flex items-center justify-between w-full py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 md:w-auto dark:text-white md:dark:hover:text-blue-500 dark:focus:text-white dark:border-gray-700 dark:hover:bg-gray-700 md:dark:hover:bg-transparent">Cnpj <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
  </svg></button>
            <!-- Dropdown menu -->
            <div id="dropdownNavbar" class="z-10 hidden font-normal bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600">
                <ul class="py-2 text-sm text-gray-700 dark:text-gray-400" aria-labelledby="dropdownLargeButton">

                  {{#data.cnpjs}}
                  <li>
                    <a href="/metrics/daily/?seller=welttec&group=cnpj&cnpj={{{cnpj}}}" class="capitalize text-xs block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">{{{nome}}}</a>
                  </li>
                  {{/data.cnpjs}}
                </ul>
            </div>
        </li>




        <li>
          <a href="/metrics/daily/?seller=welttec&group=cnpj&cnpj=30515462000102" class="block py-2 px-3 text-green-900 rounded hover:bg-green-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-900 dark:hover:text-white md:dark:hover:bg-transparent">Olive</a>
        </li>
        <li>
          <a href="/metrics/daily/?seller=welttec&group=bairro&bairro=luz" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">Luz</a>
        </li>
        <li>
          <a href="/metrics/daily/?seller=welttec&group=bairro&bairro=bom retiro" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">Bom Retiro</a>
        </li>
     <li>
          <a href="/metrics/daily/?seller=welttec&group=bairro&bairro=bras" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">Brás</a>
        </li>
        <li>
          <a href="/metrics/daily/?seller=welttec&group=cidade&cidade=sao paulo" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">São Paulo</a>
        </li>
        <li>
          <a href="/metrics/daily/?seller=welttec&group=estado&estado=sp" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">SP</a>
        </li>
  <li>
          <a href="/metrics/daily/?seller=welttec&group=estado&estado=lk" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">LK</a>
        </li>
        <li>
  {{!--        <a href="/metrics/daily/?seller=welttec&group=estado&estado=mg" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">MG</a>--}}
  {{!--      </li>--}}
  {{!--        <li>--}}
  {{!--        <a href="/metrics/daily/?seller=welttec&group=estado&estado=pr" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">PR</a>--}}
  {{!--      </li>--}}
  {{!--        <li>--}}
  {{!--        <a href="/metrics/daily/?seller=welttec&group=estado&estado=sc" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">SC</a>--}}
  {{!--      </li>--}}
  {{!--        <li>--}}
  {{!--        <a href="/metrics/daily/?seller=welttec&group=estado&estado=rs" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">RS</a>--}}
  {{!--      </li>--}}
  {{!--<li>--}}
  {{!--        <a href="/metrics/daily/?seller=welttec&group=estado&estado=rj" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">RJ</a>--}}
  {{!--      </li>        --}}

                <li>
          <a href="/metrics/daily/nfes?seller=welttec" target='_blank' class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">Blue Nf</a>
        </li>
                <li>
          <a href="/metrics/daily/nfrolemak"  target='_blank'  class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">Barra Funda Nf</a>
        </li>

                <li>
          <a href="/metrics/daily/nfBlumenau"  target='_blank'  class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">Blumenau Nf</a>
        </li>

        <li>
          <a href="/metrics/daily/nfTatuape"  target='_blank'  class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">Tatuapé Nf</a>
        </li>

        <li>
          <a href="/metrics/daily/nfLoja"  target='_blank'  class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">Loja Nf</a>
        </li>

                  <li>
                  <a href="/metrics/ssw/index/?data=2024&group=cnpj&seller=welttec"  target='_blank'  class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">Cnpj+</a>
        </li>


      </ul>
    </div>
  </div>
</nav>


 <section  class="py-10  lg:container lg:mx-auto">
  <div class="mx-auto grid ">

      <div class="capitalize p2 block text-3xl font-bold
          {{#data.seller-welttec}} text-green-600{{/data.seller-welttec}}
          {{#data.seller-welttec}} text-blue-600{{/data.seller-welttec}}
          {{#data.seller-ss}} text-red-600 {{/data.seller-ss}}"> {{{data.title}}} {{{data.nome}}}


{{!--          <button type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Default</button>--}}
{{!--<button type="button" class="py-2.5 px-5 me-2 mb-2 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Alternative</button>--}}
{{!--<button type="button" class="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700">Dark</button>--}}
{{!--<button type="button" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">Light</button>--}}
{{!--<button type="button" class="focus:outline-none text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">Green</button>--}}
{{!--<button type="button" class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">Red</button>--}}
{{!--<button type="button" class="focus:outline-none text-white bg-yellow-400 hover:bg-yellow-500 focus:ring-4 focus:ring-yellow-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:focus:ring-yellow-900">Yellow</button>--}}
{{!--<button type="button" class="focus:outline-none text-white bg-purple-700 hover:bg-purple-800 focus:ring-4 focus:ring-purple-300 font-medium rounded-lg text-sm px-5 py-2.5 mb-2 dark:bg-purple-600 dark:hover:bg-purple-700 dark:focus:ring-purple-900">Purple</button>--}}




                      </div>

  {{#data.group}}
  <span class="capitalize text-red-600 p2 block text-md font-bold">{{{nome}}} <span class="text-gray-600 p2 block text-xs font-bold">{{{ender}}} - {{{bairro}}} - {{{cidade}}} - {{{estado}}} - {{{cnpj}}}</span>
  </span>
  {{/data.group}}

    <table id='invoice' class=" table  border-collapse block md:table">
		<tbody class="block md:table-row-group">
			<tr class="border border-green-500 md:border-none block md:table-row absolute -top-full md:top-auto -left-full md:left-auto  md:relative ">

                <td class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell">Ano-Mês / Dia</td>

                <td class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell "><div class="text-1xl">Qtd-Mês</div></td>

                {{#data.rows.0.yearly.0}}

                    {{#monthly}}

                         <td class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell">
                            {{{day}}}
                        </td>

                    {{/monthly}}

                {{/data.rows.0.yearly.0}}

		    </tr>

           {{#data.rows}}

            {{#yearly}}

               <tr >

                    <td nowrap class="{{#bg}}bg-blue-600 text-white{{/bg}} {{^bg}}bg-gray-500  text-white{{/bg}} p-2 text-white md:border md:border-grey-200 block md:table-cell">

                        <div class=" p2 block text-xs">{{{year}}}-{{{month}}}</div>

                    </td>

                      <td nowrap class="bg-slate-300 text-white p-2 text-white md:border md:border-grey-200 block md:table-cell">


                        <div class="text-blue-500 p2 block text-lg font-bold">{{{qty_blue}}}</div>
                        <div class="text-green-500 p2 block text-lg font-bold">{{{qty_green}}} <span class="text-gray-500 p2 block text-xs font-bold">7 x {{{qty_green7}}}</span></div>


                    </td>
                {{#monthly}}

                    <td nowrap class=" {{#weekend}}bg-gray-200{{/weekend}} p-2 text-white md:border md:border-grey-200 block md:table-cell text-right">

                        <div class="text-blue-500 p2 block text-lg font-bold">{{{qty_blue}}}</div>

                        <div class="text-green-500 p2 block text-lg font-bold">{{{qty_green}}}</div>


                    </td>

                {{/monthly}}

                </tr>

            {{/yearly}}

                <tr>
                <td colspan=1 nowrap class="bg-green-600 text-white p-2 text-white md:border md:border-grey-200 block md:table-cell">

                        <div class="  text-right p2 block text-1xl font-bold">{{{year}}}</div>

                </td>
                <td colspan=1 nowrap class="bg-gray-200 text-white p-2 text-white md:border md:border-grey-200 block md:table-cell">

                    <div class="text-blue-500 p2 block text-lg font-bold">{{{qty_blue}}}</div>
                    <div class="text-green-500 p2 block text-lg font-bold">{{{qty_green}}} <span class="text-gray-500 p2 block text-xs font-bold">7 x {{{qty_green7}}}</span></div>


                </td>
                </tr>

            {{/data.rows}}


		</tbody>
	</table>
</div>
</section>


<script>

   $(document).ready(function(){


   $('.winbox-iframe').on('click', function(event)
         {
          var address = $(this).attr('rel');
            var title = $(this).attr('title') + ' - ' + address;
            var color   = $(this).attr('color');
            var width   = $(this).attr('width');
            var height  = $(this).attr('height');

            var winbox = new WinBox(
            {
                title: title,
                root: document.body,
                //     // html: "<h1>Lorem Ipsum</h1>"
                // html: '<iframe src="..." allow="..." sandbox="allow-scripts" referrerpolicy="..."></iframe>',
                // html: '<iframe  src="https://web.whatsapp.com/"  sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>',
                url: address,
                background: color,
                border: 4,
                // class: "iframe",
                x: "center",
                // y: "center",
                width: width,
                 height: height,
                //  top: "5%",
                // right: "10%",
                // bottom: "0%",
                // left: "10%",
                // width: "960",
                // height: 600,
                // minheight: 55,
                // minwidth: 100,
                // maxheight: 1280,
                // maxwidth: 1600,
                // autosize: true,

            });

        });

      // $( "#start" ).click();


      // $( "#start" ).livequery('click');

//  $(".table").tablesorter({
//     theme: 'blue',

//     // hidden filter input/selects will resize the columns, so try to minimize the change
//     widthFixed : true,

//     // initialize zebra striping and filter widgets
//     widgets: ["zebra", "filter"],

//     // headers: { 5: { sorter: false, filter: false } },

//     widgetOptions : {

//       // extra css class applied to the table row containing the filters & the inputs within that row
//       filter_cssFilter   : '',

//       // If there are child rows in the table (rows with class name from "cssChildRow" option)
//       // and this option is true and a match is found anywhere in the child row, then it will make that row
//       // visible; default is false
//       filter_childRows   : false,

//       // if true, filters are collapsed initially, but can be revealed by hovering over the grey bar immediately
//       // below the header row. Additionally, tabbing through the document will open the filter row when an input gets focus
//       filter_hideFilters : false,

//       // Set this option to false to make the searches case sensitive
//       filter_ignoreCase  : true,

//       // jQuery selector string of an element used to reset the filters
//       filter_reset : '.reset',

//       // Use the $.tablesorter.storage utility to save the most recent filters
//       filter_saveFilters : true,

//       // Delay in milliseconds before the filter widget starts searching; This option prevents searching for
//       // every character while typing and should make searching large tables faster.
//       filter_searchDelay : 300,

//       // Set this option to true to use the filter to find text from the start of the column
//       // So typing in "a" will find "albert" but not "frank", both have a's; default is false
//       filter_startsWith  : false,

//       // Add select box to 4th column (zero-based index)
//       // each option has an associated function that returns a boolean
//       // function variables:
//       // e = exact text from cell
//       // n = normalized value returned by the column parser
//       // f = search filter input value
//       // i = column index
//       filter_functions : {

//         // Add select menu to this column
//         // set the column value to true, and/or add "filter-select" class name to header
//         // '.first-name' : true,

//         // Exact match only
//         // 1 : function(e, n, f, i, $r, c, data) {
//         //   return e === f;
//         // },

//         // Add these options to the select dropdown (regex example)
//         // 2 : {
//         //   "A - D" : function(e, n, f, i, $r, c, data) { return /^[A-D]/.test(e); },
//         //   "E - H" : function(e, n, f, i, $r, c, data) { return /^[E-H]/.test(e); },
//         //   "I - L" : function(e, n, f, i, $r, c, data) { return /^[I-L]/.test(e); },
//         //   "M - P" : function(e, n, f, i, $r, c, data) { return /^[M-P]/.test(e); },
//         //   "Q - T" : function(e, n, f, i, $r, c, data) { return /^[Q-T]/.test(e); },
//         //   "U - X" : function(e, n, f, i, $r, c, data) { return /^[U-X]/.test(e); },
//         //   "Y - Z" : function(e, n, f, i, $r, c, data) { return /^[Y-Z]/.test(e); }
//         // },

//         // Add these options to the select dropdown (numerical comparison example)
//         // Note that only the normalized (n) value will contain numerical data
//         // If you use the exact text, you'll need to parse it (parseFloat or parseInt)
//         // 4 : {
//         //   "< $10"      : function(e, n, f, i, $r, c, data) { return n < 10; },
//         //   "$10 - $100" : function(e, n, f, i, $r, c, data) { return n >= 10 && n <=100; },
//         //   "> $100"     : function(e, n, f, i, $r, c, data) { return n > 100; }
//         // }
//       }

//     }

//   });

});

</script>

</body>
</html>




