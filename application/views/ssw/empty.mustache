<!DOCTYPE html>
<html lang="pt-br">

<head>
  <title>SSW </title>

  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">


        <!-- awesome CSS -->
        {{!--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">--}}
        
        <!-- tailwindcss CSS -->
        <!--<link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet">-->
        
        <!-- tailwindcss CSS -->
        <script src="https://cdn.tailwindcss.com"></script>
         
        <!-- jquery 3.6 -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"
        integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="
        crossorigin="anonymous"></script>
        
        <!-- winbox bundle -->
        <script src="https://rawcdn.githack.com/nextapps-de/winbox/0.2.0/dist/winbox.bundle.js"></script>



       
        <!-- twitter-bootstrap//-->
        <!--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.0.1/css/bootstrap-grid.min.css" integrity="sha512-Aa+z1qgIG+Hv4H2W3EMl3btnnwTQRA47ZiSecYSkWavHUkBF2aPOIIvlvjLCsjapW1IfsGrEO3FU693ReouVTA==" crossorigin="anonymous" referrerpolicy="no-referrer" />-->
        
       
        

        <script src="https://cdnjs.cloudflare.com/ajax/libs/jeditable.js/2.0.19/jquery.jeditable.min.js" integrity="sha512-Jn+modod+CgT0sdSdiL5Zr74JeTDTR2DGzeSRG1oUVnZjZFnkdSPOFUiwAr/St/lIvYEyBaZ6F46vJE4fbHg6A==" crossorigin="anonymous"></script>
        
        <!--        /* Livequery   *///-->
        <!--<script src="https://office.vallery.com.br/jquery/livequery/jquery.livequery.js"></script>-->
        
         
        <!-- htmx -->
        <!--<script src="https://unpkg.com/htmx.org@1.8.6"></script>-->
        <script src="https://unpkg.com/htmx.org@1.6.0"></script>
        <script src="https://unpkg.com/htmx.org/dist/ext/client-side-templates.js"></script>
        <script src="https://unpkg.com/hyperscript.org@0.9.8"></script>
        <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.x.x/dist/alpine.min.js" defer></script>
        
        
        <!--<link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/prelude.css">-->
        <!--<link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/rainbow.css">-->
        <!--<link rel="stylesheet" href="https://www.jqueryscript.net/demo/Animated-Circle-Progress-Bar-with-jQuery-SVG-asPieProgress/css/progress.css">-->
        
         <!-- mustache -->
        <script src="https://cdn.jsdelivr.net/npm/mustache@4.2.0/mustache.min.js"></script>
        
        <!-- Tabesorter  *///-->
        {{!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.2/js/jquery.tablesorter.combined.min.js"></script>--}}
        
        <!--Datatables-->
        {{!--<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/dt/jszip-2.5.0/dt-1.12.1/af-2.4.0/b-2.2.3/b-colvis-2.2.3/b-html5-2.2.3/b-print-2.2.3/cr-1.5.6/date-1.1.2/fc-4.1.0/fh-3.2.4/kt-2.7.0/r-2.3.0/rg-1.2.0/rr-1.2.8/sc-2.0.7/sb-1.3.4/sp-2.0.2/sl-1.4.0/sr-1.1.1/datatables.min.css"/>--}}
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js"></script>
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js"></script>
        <script type="text/javascript" src="https://cdn.datatables.net/v/dt/jszip-2.5.0/dt-1.12.1/af-2.4.0/b-2.2.3/b-colvis-2.2.3/b-html5-2.2.3/b-print-2.2.3/cr-1.5.6/date-1.1.2/fc-4.1.0/fh-3.2.4/kt-2.7.0/r-2.3.0/rg-1.2.0/rr-1.2.8/sc-2.0.7/sb-1.3.4/sp-2.0.2/sl-1.4.0/sr-1.1.1/datatables.min.js"></script>

<!--<script src="//cdnjs.cloudflare.com/ajax/libs/numeral.js/2.0.6/numeral.min.js"></script>-->

<!--        /* JFrame  *///-->
<!--<script src="https://garage.pimentech.net/pimentech/js/jquery.jframe.js"></script>-->
<!--<script src="/K4/jframe.js"></script>-->
<!--        /* Jeditable  *///-->
<!--<script src="/K3/media/jquery/jeditable/jquery.jeditable.js"></script>-->
<!--        /* Tabesorter  *///-->
 <link rel="stylesheet" href="https://mottie.github.io/tablesorter/css/theme.blue.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.2/js/jquery.tablesorter.combined.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.3/js/extras/jquery.tablesorter.pager.min.js"></script>

<!--        /* Sparkline  *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>-->
<!--        /* HandleBars  *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.1.2/handlebars.min.js"></script>-->
<!--        /* unsemantic *///-->
<!--<script src="https://unsemantic.com/javascripts/application.js?1527127334"></script>-->
<!--        /* Moment.js *///-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>-->
<!--        /* imagesloaded *///-->
<!--<script src="https://unpkg.com/imagesloaded@4/imagesloaded.pkgd.min.js"></script>-->
<!--        /* bootstrap-select -->
<!--<script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.9/dist/js/bootstrap-select.min.js"></script>-->
 

<style>
    tr.htmx-swapping td {
  opacity: 0;
  transition: opacity 10s ease-out;
}
</style>
</head>

<body>
 

 
   <section  class="py-10 bg-stone-100 lg:container lg:mx-auto">
  <div class="mx-auto grid max-w-8xl ">
   
    <div class="overflow-visible">
     <a href="/metrics/ssw/index/?data=2024&group=cnpj&seller=welttec" target="_blank">CNPJ blue</a>
        <a href="/metrics/ssw/index/?data=2024&group=cnpj&seller=ss" target="_blank">CNPJ red</a>
    <a href="/metrics/ssw/index/?data=2024&group=bairro&seller=welttec" target="_blank">Bairro</a>
    <a href="/metrics/ssw/index/?data=2024&group=cidade&seller=welttec" target="_blank">Cidade</a>
    <a href="/metrics/ssw/index/?data=2024&group=estado&seller=welttec" target="_blank">Estado</a>
    <a href="/metrics/ssw/index/?data=2024&group=seller" target="_blank">Seller</a>  
  </div>    

    

    
    <table id='invoice' class=" table  border-collapse block md:table">
		<thead class="block md:table-header-group">
			<tr class="border border-green-500 md:border-none block md:table-row absolute -top-full md:top-auto -left-full md:left-auto  md:relative ">
			
			    <th class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell">#</th>
                <th class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell">SSW</th>
                <th class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell">Seller</th>
                <th class="bg-gray-600 p-2 text-white md:border md:border-grey-500 text-left block md:table-cell">Action</th>
                <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Nfe</th>
  
               <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Data</th>		
                                 <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Qty</th>
                 <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Buyer</th>
            <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Vols</th>      
            <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Peso</th>
                
                  <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">P/V</th>
                 <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">VD</th>
                  <th class="bg-gray-600 p-2 text-white font-bold md:border md:border-grey-500 text-left block md:table-cell">Action</th>
                 
             


		
			</tr>
		</thead>
		<tbody class="block md:table-row-group" >
		  
   {{#data}}
   
   <tr class=" border border-grey-500 md:border-none block md:table-row text-md">
     
	  <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell">{{{counter}}}</td>    			 
      <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell">{{{SSWID}}}</td>    
      
    
      <td class="p-2  text-left ">
         <div class=" text-base text-blue-700" id="ssw-{{{SSWID}}}" name="ssw-{{{SSWID}}}" >{{{seller}}}</div>
      </td>    
       
      <td nowrap class="p-2 md:border md:border-grey-500 text-left block md:table-cell  text-md">   
          <div class="grid gap-1 grid-cols-8 grid-rows-1">
            <form hx-post="/K3/global/crud/update/"   hx-target="#ssw-{{{SSWID}}}">
            <input id="id" name="id" type="text" class="hidden" value="Analytics.ssw|seller|id|{{{SSWID}}}">
            <input id="value" name="value" type="text" class="hidden" value="welttec">
            <button type="submit"  class="rounded hover:rounded-lg border border-sky-500">Blue</button>
        </form>  
            <form hx-post="/K3/global/crud/update/"  hx-target="#ssw-{{{SSWID}}}">
            <input id="id" name="id" type="text" class="hidden" value="Analytics.ssw|seller|id|{{{SSWID}}}">
            <input id="value" name="value" type="text" class="hidden" value="ss">
            <button type="submit"   class="rounded hover:rounded-lg border border-red-500">Red</button>
        </form>
         <form hx-post="/K3/global/crud/update/"  hx-target="#ssw-{{{SSWID}}}">
            <input id="id" name="id" type="text" class="hidden" value="Analytics.ssw|seller|id|{{{SSWID}}}">
            <input id="value" name="value" type="text" class="hidden" value="lanmax">
            <button type="submit"   class="rounded hover:rounded-lg border border-red-500">Lan</button>
        </form>  
         <form hx-post="/K3/global/crud/update/"  hx-target="#ssw-{{{SSWID}}}">
            <input id="id" name="id" type="text" class="hidden" value="Analytics.ssw|seller|id|{{{SSWID}}}">
            <input id="value" name="value" type="text" class="hidden" value="brc">
            <button type="submit"   class="rounded hover:rounded-lg border border-red-500">BRC</button>
        </form>
         <form hx-post="/K3/global/crud/update/"  hx-target="#ssw-{{{SSWID}}}">
            <input id="id" name="id" type="text" class="hidden" value="Analytics.ssw|seller|id|{{{SSWID}}}">
            <input id="value" name="value" type="text" class="hidden" value="andrade">
            <button type="submit"   class="rounded hover:rounded-lg border border-red-500">And</button>
        </form>
        
         <form hx-post="/K3/global/crud/update/"  hx-target="#ssw-{{{SSWID}}}">
            <input id="id" name="id" type="text" class="hidden" value="Analytics.ssw|seller|id|{{{SSWID}}}">
            <input id="value" name="value" type="text" class="hidden" value="disk">
            <button type="submit"   class="rounded hover:rounded-lg border border-red-500">Disk</button>
        </form>
        
         <form hx-post="/K3/global/crud/update/"  hx-target="#ssw-{{{SSWID}}}">
            <input id="id" name="id" type="text" class="hidden" value="Analytics.ssw|seller|id|{{{SSWID}}}">
            <input id="value" name="value" type="text" class="hidden" value="rolemak">
            <button type="submit"   class="rounded hover:rounded-lg border border-red-500">Mak</button>
        </form>
         <form hx-post="/K3/global/crud/update/"  hx-target="#ssw-{{{SSWID}}}">
            <input id="id" name="id" type="text" class="hidden" value="Analytics.ssw|seller|id|{{{SSWID}}}">
            <input id="value" name="value" type="text" class="hidden" value="">
            <button type="submit"   class="rounded hover:rounded-lg border border-red-500">Blk</button>
        </form>
            
           
           <a color="#008000"  width="960" height="600"   class="winbox-iframe cursor-pointer text-sm rounded hover:rounded-lg " rel="/metrics/ssw/html/1/{{{SSWID}}}" title="{{{Cliente}}} nfe {{{nfe}}}  ">ver</a></div>
      </td>    
      
     
        <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell text-gray-600" nowrap>
            <div>{{{nfe}}} 
           <a color="#008000"  width="1400" height="600"   class="winbox-iframe cursor-pointer text-sm rounded hover:rounded-lg border border-green-500" rel="/metrics/ssw/index/?nfe={{{nfe}}}" title="{{{nfe}}}  ">ver</a>
           <a color="#008000"  width="1400" height="600"   class="winbox-iframe cursor-pointer text-sm rounded hover:rounded-lg border border-green-500" rel="/sqs/ssw/nfe/?nfe={{{nfe}}}" title="{{{nfe}}}  ">mak</a></td>
           </div>
        
        <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell nowrap text-xs" nowrap><div class="editable" id="Analytics.ssw|data|id|{{SSWID}}">{{{data}}}</div></td>
        

        <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell  text-orange-500">{{{sets}}}</td>			  
        
        <td class="p-2 md:border md:border-grey-500 text-left text-xs block md:table-cell">
             <a color="#008000" width="1500" height="600"  class="winbox-iframe cursor-pointer text-xs rounded hover:rounded-lg " rel="/K3/tip/vcustomer/{{{idcli}}}/" title="{{{Cliente}}}">{{{Cliente}}}-{{{cidade}}}-{{{estado}}}  {{{cnpj}}}</a>
           <a color="#008000" width="1500" height="600"  class=" cursor-pointer text-xs rounded hover:rounded-lg border border-blue-500" href="{{{link}}}" target="_blank" title="{{{link}}}">pesquisa ssw </a>
           
            <a color="#008000" width="1500" height="600"  class=" cursor-pointer text-xs rounded hover:rounded-lg border border-blue-500" href="{{{linkEsm}}}" target="_blank" title="{{{link}}}">esm </a>
        </td>
        
        <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell "><div class="editable" id="Analytics.ssw|volumes|id|{{SSWID}}">{{{volumes}}}</div></td>   
        
        <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell "><div class="editable" id="Analytics.ssw|peso|id|{{SSWID}}">{{{peso}}}</div></td> 
        
        <td class="p-2 md:border md:border-grey-500 text-right block md:table-cell ">{{{pv}}}</td>     
        
        <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell ">{{{nick}}} </td>
        <td class="p-2 md:border md:border-grey-500 text-left block md:table-cell "><button class="btn btn-danger" hx-delete="/metrics/ssw/delete/{{{SSWID}}}" hx-target="closest tr" hx-swap="outerHTML swap:1s">Delete</button></td>


      
      

		
				
			</tr>
  
  {{/data}}
			
			
		</tbody>
		
		<tfoot>
		    <tr>
		     
		      <td colspan=13 class="p-2 md:border md:border-grey-500 text-left block md:table-cell "><button class="btn btn-danger" hx-delete="/metrics/ssw/delete_by_cnpj/{{{data.0.cnpj}}}"  hx-confirm="Are you sure to remove all cnpj ssw records?" hx-target="closest tr" hx-swap="outerHTML swap:1s" >Delete all from this cnpj</button></td> 
		      
		    </tr>
		</tfoot>
	</table>
</div>
</section>


  <script src="/cdn/explore/jeditable.js"></script>
<script>

   $(document).ready(function(){

  
   $('.winbox-iframe').on('click', function(event) 
         {
          var address = $(this).attr('rel');
            var title = $(this).attr('title') + ' - ' + address;
            var color   = $(this).attr('color');
            var width   = $(this).attr('width');
            var height  = $(this).attr('height');
            
            var winbox = new WinBox(
            {
                title: title,
                root: document.body,
                //     // html: "<h1>Lorem Ipsum</h1>"
                // html: '<iframe src="..." allow="..." sandbox="allow-scripts" referrerpolicy="..."></iframe>',
                // html: '<iframe  src="https://web.whatsapp.com/"  sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>',
                url: address,
                background: color,
                border: 4,
                // class: "iframe",
                x: "center",
                // y: "center",
                width: width,
                 height: height,
                //  top: "5%",
                // right: "10%",
                // bottom: "0%",
                // left: "10%",
                // width: "960",
                // height: 600,
                // minheight: 55,
                // minwidth: 100,
                // maxheight: 1280,
                // maxwidth: 1600,
                // autosize: true,
               
            });
          
        }); 

      // $( "#start" ).click();
       
 
      // $( "#start" ).livequery('click');
       
 $(".table").tablesorter({
    theme: 'blue',

    // hidden filter input/selects will resize the columns, so try to minimize the change
    widthFixed : true,

    // initialize zebra striping and filter widgets
    widgets: ["zebra", "filter"],

    // headers: { 5: { sorter: false, filter: false } },

    widgetOptions : {

      // extra css class applied to the table row containing the filters & the inputs within that row
      filter_cssFilter   : '',

      // If there are child rows in the table (rows with class name from "cssChildRow" option)
      // and this option is true and a match is found anywhere in the child row, then it will make that row
      // visible; default is false
      filter_childRows   : false,

      // if true, filters are collapsed initially, but can be revealed by hovering over the grey bar immediately
      // below the header row. Additionally, tabbing through the document will open the filter row when an input gets focus
      filter_hideFilters : false,

      // Set this option to false to make the searches case sensitive
      filter_ignoreCase  : true,

      // jQuery selector string of an element used to reset the filters
      filter_reset : '.reset',

      // Use the $.tablesorter.storage utility to save the most recent filters
      filter_saveFilters : true,

      // Delay in milliseconds before the filter widget starts searching; This option prevents searching for
      // every character while typing and should make searching large tables faster.
      filter_searchDelay : 300,

      // Set this option to true to use the filter to find text from the start of the column
      // So typing in "a" will find "albert" but not "frank", both have a's; default is false
      filter_startsWith  : false,

      // Add select box to 4th column (zero-based index)
      // each option has an associated function that returns a boolean
      // function variables:
      // e = exact text from cell
      // n = normalized value returned by the column parser
      // f = search filter input value
      // i = column index
      filter_functions : {

        // Add select menu to this column
        // set the column value to true, and/or add "filter-select" class name to header
        // '.first-name' : true,

        // Exact match only
        // 1 : function(e, n, f, i, $r, c, data) {
        //   return e === f;
        // },

        // Add these options to the select dropdown (regex example)
        // 2 : {
        //   "A - D" : function(e, n, f, i, $r, c, data) { return /^[A-D]/.test(e); },
        //   "E - H" : function(e, n, f, i, $r, c, data) { return /^[E-H]/.test(e); },
        //   "I - L" : function(e, n, f, i, $r, c, data) { return /^[I-L]/.test(e); },
        //   "M - P" : function(e, n, f, i, $r, c, data) { return /^[M-P]/.test(e); },
        //   "Q - T" : function(e, n, f, i, $r, c, data) { return /^[Q-T]/.test(e); },
        //   "U - X" : function(e, n, f, i, $r, c, data) { return /^[U-X]/.test(e); },
        //   "Y - Z" : function(e, n, f, i, $r, c, data) { return /^[Y-Z]/.test(e); }
        // },

        // Add these options to the select dropdown (numerical comparison example)
        // Note that only the normalized (n) value will contain numerical data
        // If you use the exact text, you'll need to parse it (parseFloat or parseInt)
        // 4 : {
        //   "< $10"      : function(e, n, f, i, $r, c, data) { return n < 10; },
        //   "$10 - $100" : function(e, n, f, i, $r, c, data) { return n >= 10 && n <=100; },
        //   "> $100"     : function(e, n, f, i, $r, c, data) { return n > 100; }
        // }
      }

    }

  });

});
      
</script>
 
</body>
</html>




