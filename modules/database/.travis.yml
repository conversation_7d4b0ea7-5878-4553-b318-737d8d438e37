sudo: false

language: php

# Only build the main develop/master branches - feature branches will be covered by PRs
branches:
  only:
    - /^[0-9\.]+\/(develop|master)$/

cache:
  directories:
    - vendor
    - $HOME/.composer/cache

php:
  - 5.3
  - 5.4
  - 5.5
  - 5.6
  - 7.0
  - hhvm

matrix:
  include:
    - php: 5.3
      env: 'COMPOSER_PHPUNIT="lowest"'

before_script:
  - composer install --prefer-dist
  - if [ "$COMPOSER_PHPUNIT" = "lowest" ]; then composer update --prefer-lowest --with-dependencies phpunit/phpunit; fi;
  - vendor/bin/koharness

script:
  - cd /tmp/koharness && ./vendor/bin/phpunit --bootstrap=modules/unittest/bootstrap.php modules/unittest/tests.php

notifications:
  email: false
