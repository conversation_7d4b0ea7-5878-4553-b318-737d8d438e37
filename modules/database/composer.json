{"name": "kohana/database", "type": "kohana-module", "description": "The official Kohana module for database interactions, building queries, and prepared statements", "homepage": "http://kohanaframework.org", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["kohana", "framework", "database"], "authors": [{"name": "Kohana Team", "email": "<EMAIL>", "homepage": "http://kohanaframework.org/team", "role": "developer"}], "support": {"issues": "http://dev.kohanaframework.org", "forum": "http://forum.kohanaframework.org", "irc": "irc://irc.freenode.net/kohana", "source": "http://github.com/kohana/core"}, "require": {"composer/installers": "~1.0", "kohana/core": ">=3.3", "php": ">=5.3.6"}, "require-dev": {"kohana/core": "3.3.*@dev", "kohana/unittest": "3.3.*@dev", "kohana/koharness": "*@dev"}, "suggest": {"ext-mysql": "*", "ext-pdo": "*"}, "extra": {"branch-alias": {"dev-3.3/develop": "3.3.x-dev", "dev-3.4/develop": "3.4.x-dev"}, "installer-paths": {"vendor/{$vendor}/{$name}": ["type:kohana-module"]}}}