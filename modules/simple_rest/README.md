# kohana-simple-REST
==================

## Summary

Simple JSON only RESTful controller for Kohana in module form

Built against Kohana 3.2 [http://kohanaframework.org/3.2/guide/]

Currently it is hardcoded to only respond in JSON (application/json).

What it is primarily doing is routing the HTTP verbs into the corresponding Kohana actions.

i.e.  

Get /index.php/api/v1/users  --> will route to the controllers/api/users::action_get method

There are also default implementations for GET/POST/PUT/DELETE/PATCH in the base controller @see `fulfill_{method}_request` methods in Api_Controller

## Install

### Example Routes

This is a simple Kohana, module so place it in you module dir. This can be done by simply copying the few files or as a git submodule:

`git <NAME_EMAIL>:samkeen/kohana-simple-REST.git modules/simple_rest`


Then enable it in bootstrap.php

```php
<?php
# application/bootstrap.php
/**
 * Enable modules. Modules are referenced by a relative or absolute path.
 */
Kohana::modules(array(
  // 'auth'       => MODPATH.'auth',       // Basic authentication
	// 'cache'      => MODPATH.'cache',      // Caching with multiple backends
	// 'codebench'  => MODPATH.'codebench',  // Benchmarking tool
	   'database'   => MODPATH.'database',   // Database access
	// 'image'      => MODPATH.'image',      // Image manipulation
	// 'orm'        => MODPATH.'orm',        // Object Relationship Mapping
	// 'unittest'   => MODPATH.'unittest',   // Unit testing
	// 'userguide'  => MODPATH.'userguide',  // User guide and API documentation
	 'simple_rest'  => MODPATH.'simple_rest',  // User guide and API documentation
	));
```

Then add Routes to support RESTful URIs

```php
<?php
# application/bootstrap.php
/*
 * This is to suppport URI's in the form: `/api/v1/users/42`
 * The defaults section is optional, actually more RESTful to return 404 in 
 * those cases.
 */
Route::set('api', '<directory>/<api_version>(/<controller>(/<resource_id>))',
    array(
        'directory'   => 'api',
        'api_version' => 'v1'
    ))
    ->defaults(array(
    'controller' => 'home',
    'action'     => 'index',
));
/**
 * This is the catch-all for NON API requests
 * Could be used as api summary page with links to docs.
 */
Route::set('static', '(<controller>(/<action>(/<id>)))')
  ->defaults(array(
		'controller' => 'static',
		'action'     => 'index',
));
```

Finally, an example controller would be something like:

```php
<?php
# application/classes/controller/api/users.php

class Controller_Api_Users extends Api_Controller {

    static $table_name = 'users';
    /**
     * @see http://kohanaframework.org/3.2/guide/kohana/security/validation
     * @see http://kohanaframework.org/3.2/guide/api/Validation
     *
     * @var array
     */
    static $fields = array(
        'name' => array(
            'not_empty'
        ),
        'email' => array(
            'not_empty'
        )
    );

    function action_get()
  {
        return parent::fulfill_get_request();
	}
    function action_post()
	{
        if( ! $this->post_validate($this->request->post()))
        {
            $this->validation_error_response();
        }
        else
        {
            return $this->fulfill_post_request();
        }
	}
}
```

Note: All the base controller (`Api_Controller`) action methods for HTTP verbs (i.e. `action_get`) return 405
    $this->error_response(405, 'GET method not allowed');
To have your controller support a given HTTP verb, override that method in the concrete class and provide an implementation
such as the example above.