{"name": "kohana/unittest", "type": "kohana-module", "description": "PHPUnit integration for running unit tests on the Kohana framework", "homepage": "http://kohanaframework.org", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["kohana", "framework"], "authors": [{"name": "Kohana Team", "email": "<EMAIL>", "homepage": "http://kohanaframework.org/team", "role": "developer"}], "support": {"issues": "http://dev.kohanaframework.org", "forum": "http://forum.kohanaframework.org", "irc": "irc://irc.freenode.net/kohana", "source": "http://github.com/kohana/core"}, "require": {"composer/installers": "~1.0", "kohana/core": ">=3.3", "php": ">=5.3.3", "phpunit/phpunit": "3.7.24 - 4"}, "require-dev": {"kohana/core": "3.3.*@dev", "kohana/koharness": "*@dev"}, "extra": {"branch-alias": {"dev-3.3/develop": "3.3.x-dev", "dev-3.4/develop": "3.4.x-dev"}, "installer-paths": {"vendor/{$vendor}/{$name}": ["type:kohana-module"]}}}