# Was ist Kohana?

Kohana ist ein Open-Source-basiertes, [objektorientiertes](http://de.wikipedia.org/wiki/Objektorientierte_Programmierung) [MVC](http://de.wikipedia.org/wiki/Model_View_Controller "Model View Controller")-[Webframework](http://de.wikipedia.org/wiki/Web_Application_Framework) unter Verwendung von [PHP5](http://php.net/manual/de/intro-whatis "PHP Hypertext Preprocessor"). Es wird von Freiwilligen entwickelt, das darauf abzielt schnell, sicher und schlank zu sein.

[!!] <PERSON>hana ist unter der [BSD-Lizenz](http://kohanaframework.org/license) ver�ffentlicht, so dass man es rechtlich f�r alle Arten von Open-Source-, kommerzieller oder privater Projekte nutzen kann.

## Was macht Kohana besonders?

Durch den einzigartigen [Dateisystem](about.filesystem)-Aufbau ist alles erweiterbar und man braucht wenige oder keine [Einstellungen](about.configuration) vornehmen. Die [Fehlerbehandlung](debugging.errors) hilf<PERSON>, die Fehlerquelle schnell zu finden, und die [Fehlersuche](debugging) und [Programmanalyse](debugging.profiling) erm�glichen einen Einblick in die Anwendung.

Um die Sicherheit deiner Anwendung zu unterst�tzen, enth�lt Kohana Werkzeuge f�r [XSS-Entfernung](security.xss), [Eingabe-�berpr�fung](security.validation), [signierte Cookies](security.cookies), [Formular](security.forms)- und [HTML](security.html)-Erstellung. Die [Datenbank](security.database)-Schicht bietet Schutz vor [SQL-Injection](http://de.wikipedia.org/wiki/SQL-Injection). Nat�rlich wurde der gesamte offizielle Quelltext sorgf�ltig geschrieben und auf Sicherheit gepr�ft.

## Diese Dokumentation ist schei�e!

Wir bem�hen uns um eine vollst�ndige Dokumentation. Wenn eine Frage trotzdem offen bleibt, versuche es beim [inoffiziellen Wiki](http://kerkness.ca/wiki/doku.php). Wenn du etwas zum Handbuch beitragen oder �ndern willst, erstelle bitte [eine Kopie](http://github.com/kohana/userguide), bearbeite sie und stelle eine Anfrage zur Zusammenf�hrung. Falls du nicht mit git vertraut bist, kannst du auch ein [Feature-Vorschlag](http://dev.kohanaframework.org/projects/kohana3/issues) (Anmeldung erforderlich) machen.
