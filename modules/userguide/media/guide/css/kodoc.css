@import url("api.css");

html {
  background: #00262f;
  font-size: 70%;
}
body {
  margin: 0;
}
ol ol,
ol ul,
ul ul,
ul ol {
  margin-bottom: 0;
}
a img {
  border: 0;
}

h1 small,
h2 small,
h3 small,
h4 small {
  font-weight: normal;
  font-size: 0.7em;
}
h5 small,
h6 small {
  font-weight: normal;
  font-size: 0.8em;
}

dl dd {
  margin-left: 0;
}

code {
  color: #156d83;
  font-family: <PERSON><PERSON><PERSON>, "Bitstream Vera Sans Mono", "Courier New", Courier,
    monospace;
}

pre {
  background: #fff;
  border-radius: 10px;
  padding: 1.5em;
  font-size: 0.9em;
  margin-top: 1.5em;
  margin-bottom: 1.5em;
  overflow: auto;
}

.highlighted div.line {
  font-size: 1em;
  line-height: 1.3 !important;
}

table {
  background: #eee;
  border-left: 1px solid #ccc;
  border-top: 1px solid #ccc;
  margin-bottom: 1.5em;
  width: 100%;
}

table td,
table th {
  padding: 0.4em 0.8em;
  line-height: 1.4286;
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
}

table th {
  background: #ddd;
}

table tr:nth-child(even) {
  background: #fff;
}

.caps {
  text-transform: uppercase;
  font-size: 0.8em;
  font-weight: normal;
}
.status {
  text-transform: lowercase;
  font-variant: small-caps;
  font-weight: bold;
  color: #911;
}
.container .colborder {
  border-color: #d3d8bc;
}

.note {
  padding: 1.5em;
  padding-left: 5em;
  background: #f7f7f7 url("../img/lightbulb_48.png") no-repeat 1em center;
  border-radius: 0.6em;
  overflow: auto;
}

h1 a.permalink,
h2 a.permalink,
h3 a.permalink,
h4 a.permalink,
h5 a.permalink,
h6 a.permalink {
  font-size: 0.6em;
  line-height: 100%;
  vertical-align: middle;
  margin-left: 1em;
  padding: 0;
  font-weight: normal;
  display: none;
  position: inherit;
}

h1:hover a.permalink,
h2:hover a.permalink,
h3:hover a.permalink,
h4:hover a.permalink,
h5:hover a.permalink,
h6:hover a.permalink {
  display: inline;
}

#kodoc-header,
#kodoc-content,
#kodoc-footer {
  float: left;
  clear: both;
  width: 100%;
}

#kodoc-header {
  padding: 20px 0 2em;
  background-color: #159957;
  background-image: linear-gradient(120deg, #155799, #159957);
}
#kodoc-logo {
  display: block;
  float: left;
  color: #fff;
  font-size: 3em;
  margin-left: 35px;
  text-decoration: none;
  font-weight: 600;
  line-height: 1.4em;
}
#kodoc-menu {
  float: right;
  margin-top: 24px;
  background: #113c32;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
}
#kodoc-menu ul {
  float: left;
  margin: 0;
  padding: 0 0.5em 0 0;
}
#kodoc-menu li {
  display: block;
  float: left;
  margin: 0;
  padding: 0;
}
#kodoc-menu li.first {
  padding-left: 0.5em;
}
#kodoc-menu li a {
  display: block;
  height: 32px;
  line-height: 32px;
  padding: 0 0.8em;
  border-right: solid 1px #0f362d;
  border-left: solid 1px #144539;
  letter-spacing: 0.05em;
  text-decoration: none;
  text-transform: uppercase;
  color: #efefef;
  font-size: 90%;
}
#kodoc-menu li.first a {
  border-left: 0;
}
#kodoc-menu li.last a {
  border-right: 0;
}
#kodoc-menu li a:hover {
  background: #164e41;
  border-left-color: #195a4b;
  color: #fff;
  text-shadow: #fff 0 0 1px;
}

#kodoc-content {
  background: #fff;
}
#kodoc-content .wrapper {
  min-height: 390px;
  padding: 1em 0;
  background: #fff;
}
#kodoc-content div.page-toc {
  float: right;
  margin: 1em;
  margin-top: 0;
  padding: 1em;
  background: #fff;
  border: solid 0.1em #15935e;
  border-radius: 0.6em;
}
#kodoc-content p.intro {
  padding: 1em 20px;
  padding-left: 20px;
  margin: 0 -20px;
  font-size: 1.2em;
}
#kodoc-content a {
  color: #004352;
}
#kodoc-content a:hover {
  color: #00758f;
}
#kodoc-content a:active {
  text-decoration: none;
}

#kodoc-breadcrumb {
  margin: 0 0 1em;
  padding: 0 0 0.5em;
  list-style: none;
  border-bottom: solid 1px #15935e;
}
#kodoc-breadcrumb li {
  display: inline-block;
  margin: 0;
  padding: 0 0.4em 0 0;
  text-transform: uppercase;
  font-size: 11px;
}
#kodoc-breadcrumb li:before {
  content: "»";
  padding-right: 0.4em;
}
#kodoc-breadcrumb li a {
  color: #999;
  text-decoration: none;
}

#kodoc-topics {
}
#kodoc-topics ul,
#kodoc-topics ol {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
#kodoc-topics ul li,
#kodoc-topics ol li {
  margin: 0;
  padding: 0;
  margin-left: 1em;
}
#kodoc-topics ul li a.current,
#kodoc-topics ol li a.current {
  font-weight: bold;
}

#kodoc-topics span,
#kodoc-topics a {
  display: block;
  padding: 0;
  margin: 0;
}
#kodoc-topics span {
  cursor: pointer;
}
#kodoc-topics span.toggle {
  display: block;
  float: left;
  width: 1em;
  padding-right: 0.4em;
  margin-left: -1.4em;
  text-align: center;
}

#kodoc-topics li span {
  cursor: pointer;
}

#kodoc-footer {
  padding: 1em 0;
  background: #00262f;
  color: #405c63;
  text-shadow: #00262f 0.1em 0.1em 1px;
  font-size: 0.9em;
}
#kodoc-footer a {
  color: #809397;
}
#kodoc-footer div.last {
  text-align: right;
}
